# Changelog

## [8.3.1]

- UI adjust
- Code clean

## [8.3.0]

- Updated Bootstrap/Reactstrap
- Add theme switcher
- Updated dependencies
- Updated theme-colors, fonts, colors

## [8.2.4]

- Updated folllow Dependencies and DevDependencies: 
    "@amcharts/amcharts4": "^4.7.6" ->"^4.10.18",
    "@amcharts/amcharts4-geodata": "^4.1.8" -> "^4.1.20",
    "@fullcalendar/core": "5.3.0" -> "5.7.0", 
    "@fullcalendar/daygrid": "5.3.0" ->  "5.7.0",
    "@fullcalendar/interaction": "5.3.0" -> "5.7.0",
    "@fullcalendar/list": "5.3.0" -> "5.7.0",
    "@fullcalendar/react": "5.3.0" -> "5.7.0",
    "@fullcalendar/timegrid": "5.3.0" -> "5.7.0",
    "animate.css": "3.7.0" -> "4.1.1",
    "apexcharts": "^3.10.1" -> "^3.26.2",
    "axios": "^0.18.0" -> "^0.21.1", 
    "bootstrap": "4.0.0" -> "4.6.0",
    "bootstrap-slider": "^10.2.1" -> "^10.6.2", 
    "chroma-js": "^2.1.0" -> "^2.1.1", 
    "classnames": "^2.2.6" -> "^2.3.1", 
    "connected-react-router": "6.3.1" -> "6.9.1", 
    "cross-env": "^7.0.2" -> "^7.0.3", 
    "draft-js": "^0.10.5" -> "^0.11.7", 
    "echarts": "^4.4.0" -> "^4.9.0",
    "echarts-for-react": "^2.0.15-beta.1" -> "^2.0.16", 
    "file-saver": "^2.0.2" -> "^2.0.5", 
    "filesize": "^6.1.0" -> "^6.3.0", 
    "flot": "^0.8.0-alpha" -> "^0.8.3", 
    "formik": "1.5.1" -> "1.5.8", 
    "formsy-react": "0.19.5" -> "1.1.6", 
    "highcharts": "^7.2.0" -> "^7.2.2", 
    "md5": "^2.2.1" -> "^2.3.0", 
    "moment": "^2.22.2" -> "^2.29.1", 
    "qs": "^6.9.4" -> "^6.10.1", 
    "query-string": "^6.12.1" -> "^6.14.1", 
    "rc-hammerjs": "0.6.9" -> "0.6.10", 
    "react": "^16.13.1" -> "16.14.0", 
    "react-animate-height": "^2.0.16" ->  "^2.0.23", 
    "react-apexcharts": "^1.3.3" -> "^1.3.9", 
    "react-app-polyfill": "^0.1.3" -> "^0.2.2", 
    "react-autosize-textarea": "^5.0.0" ->"^5.0.1", 
    "react-bootstrap": "^1.0.0-beta.16" -> "^1.6.0", 
    "react-bootstrap-table": "4.1.5" -> "4.3.1", 
    "react-datetime": "^2.16.1" -> "^2.16.3", 
    "react-dev-utils": "^6.0.5" -> "^6.1.1", 
    "react-dom": "^16.5.2" -> "^16.14.0", 
    "react-draft-wysiwyg": "1.10.12" -> "1.14.7",
    "react-joyride": "^2.1.1" ->  "^2.3.0", 
    "react-redux": "^7.2.0" -> "^7.2.4", 
    "react-router-hash-link": "^1.2.1" -> "^1.2.2", 
    "react-scrollspy": "^3.3.5" -> "^3.4.3", 
    "react-select":  "^3.0.8" -> "^3.2.0", 
    "react-slick": "^0.23.1" -> "^0.28.1", 
    "react-sortable-hoc": "^0.8.3" -> "^0.8.4", 
    "react-sortable-tree": "^2.2.0" -> "^2.8.0", 
    "react-sortablejs": "1.5.0" -> "1.5.1", 
    "react-syntax-highlighter": "^10.1.2" -> "^10.3.5", 
    "react-table": "6.7.6" -> "6.11.4",
    "react-toastify": "^5.4.0" -> "^5.5.0", 
    "react-transition-group": "^2.5.2" -> "^2.9.0", 
    "redux": "^4.0.1" -> "4.1.0",
    "rickshaw": "1.6.6" -> "1.7.1", 
    "showdown": "1.8.6" -> "1.9.1", 
    "sortablejs": "1.10.0" -> "1.13.0", 
    "styled-components": "^5.1.0" -> "^5.3.0", 
    "uuid": "^3.3.3" -> "^3.4.0", 
    "xlsx": "^0.16.0" -> "^0.16.9", 
    "yup": "^0.28.5" -> "^0.32.9", 

    "devDependencies":
    "@babel/core": "7.4.4" -> "7.14.0", 
    "@babel/plugin-proposal-class-properties": "7.4.4" -> "7.13.0", 
    "@babel/plugin-proposal-optional-chaining": "^7.2.0" -> "^7.13.2", 
    "@svgr/webpack": "4.2.0" ->  "4.3.3", 
    "babel-eslint": "10.0.1" -> "10.1.0", 
    "babel-jest": "24.8.0" -> "24.9.0", 
    "babel-loader": "8.0.5" -> "8.2.2", 
    "babel-plugin-named-asset-import": "1.0.0-next.103" -> "1.0.0-next.154", 
    "babel-preset-react-app": "9.0.0" -> "9.1.2", 
    "bfj": "6.1.1" -> "6.1.2",
    "case-sensitive-paths-webpack-plugin": "2.2.0" ->  "2.4.0",  
    "dotenv": "8.0.0" -> "8.6.0", 
    "eslint-loader": "2.1.1" -> "2.13.1", 
    "eslint-plugin-flowtype": "3.8.1" -> "3.13.0",
    "eslint-plugin-import": "2.17.2" -> "2.22.1",
    "eslint-plugin-jsx-a11y": "6.2.1" -> "6.4.1",
    "eslint-plugin-react": "7.13.0" -> "7.23.2", 
    "eslint-plugin-react-hooks": "1.6.0" -> "1.7.0", 
    "html-webpack-plugin": "4.0.0-alpha.2" -> "4.5.2", 
    "jest": "24.8.0" - "24.9.0",
    "jest-pnp-resolver": "1.2.1" -> "1.2.2", 
    "jest-resolve": "24.8.0" -> "24.9.0", 
    "lodash": "4.17.11" -> "4.17.21", 
    "mini-css-extract-plugin": "0.6.0" -> "0.8.0", 
    "node-sass": "^4.14.0" ->  "^4.14.1", 
    "optimize-css-assets-webpack-plugin": "5.0.1" - "5.0.4", 
    "pnp-webpack-plugin": "1.4.3" -> "1.6.4", 
    "postcss-flexbugs-fixes": "4.1.0" -> "4.2.1", 
    "postcss-preset-env": "6.6.0" ->  "6.7.0",
    "postcss-safe-parser": "4.0.1" -> "4.0.2" ,
    "resolve": "1.10.1" -> "1.20.0",
    "sass-loader": "7.1.0" -> "7.3.1",
    "style-loader": "0.23.0" -> "0.23.1", 
    "terser-webpack-plugin": "1.2.3" -> "1.4.5", 
    "url-loader": "4.1.0" -> "4.1.1", 
    "webpack": "4.31.0" -> "4.46.0",
    "webpack-dev-server": "3.3.1" -> "3.11.2",
    "webpack-manifest-plugin": "2.0.4" ->  "2.2.0"

- add following packages in project
    react-bootstrap-table-next
    react-bootstrap-table2-filter
    react-bootstrap-table2-editor
    react-bootstrap-table2-paginator
    react-bootstrap-table2-overlay
    react-bootstrap-table2-toolkit

- Updated positioning pages Colors, Typography, Invoice, Wizard, alerts, SPackage, Badge, Navbar 
- Updated paths of images mock page


## [8.2.4]

- Updated inactive icons color

## [8.2.3]

- Updated documentation

## [8.2.2]

- Fix, updated svg attributes

## [8.2.1]

- Extended customizer, added more colors

## [8.2.0]

- Updated Icon Pack
- Refactored customization

## [8.1.4]

- Fixed localStorage bug for customization

## [8.1.3]

- Fix several links
- Change badge text in the sidebar
- Change alert text in the usermanager/ecommerce

## [8.1.2]

- Added link to flatlogic on login page

## [8.1.1]

- Updated fullcalendar package to v5.3.0

## [8.1.0]

- Updated react and highcharts packages

## [8.0.3]

- Updated line-awesome package 

## [8.0.2]

### Improvement

- Improved css for the avatar

## [8.0.1]

### New Features

- Added sample formik widget

## [8.0.0]

### New Features

- Added brand new Backend
- New tab to mange users
- Social login
- Manage users functionality
- Update password route
- Reset password route
- Upload avatar
- Refactored architecture

## [7.3.2]

### Fix

- Fixed small calendar events

## [7.3.1]

### Update

- Updated documentation

## [7.3.0]

### New Features

- Brand new chat component

## [7.2.0]

### New Features

- New exquisite design

## [7.1.1]

### New Features

- Virtual Tour

## [7.1.0]

### New Features

- Theme customizer

## [7.0.1]

### Fixed

- Fix bugs and css improvements

## [7.0.0]

### Updated

- Removed Jquery from core layout

### New Features

- New charts
