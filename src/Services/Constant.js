// master clarity
export const ADD_CLARITY_LOADING = 'ADD_CLARITY_LOADING'
export const ADD_CLARITY_SUCCESS = 'ADD_CLARITY_SUCCESS'
export const ADD_CLARITY_ERROR = 'ADD_CLARITY_ERROR'
export const RESET_ADD_CLARITY = 'RESET_ADD_CLARITY'

export const GET_CLARITY_LOADING = 'GET_CLARITY_LOADING'
export const GET_CLARITY_SUCCESS = 'GET_CLARITY_SUCCESS'
export const GET_CLARITY_ERROR = 'GET_CLARITY_ERROR'
export const RESET_GET_CLARITY = 'RESET_GET_CLARITY'

export const UPDATE_CLARITY_LOADING = 'UPDATE_CLARITY_LOADING'
export const UPDATE_CLARITY_SUCCESS = 'UPDATE_CLARITY_SUCCESS'
export const UPDATE_CLARITY_ERROR = 'UPDATE_CLARITY_ERROR'
export const RESET_UPDATE_CLARITY = 'RESET_UPDATE_CLARITY'

export const DELETE_CLARITY_SUCCESS = 'DELETE_CLARITY_SUCCESS'
export const DELETE_CLARITY_ERROR = 'DELETE_CLARITY_ERROR'
export const RESET_DELETE_CLARITY = 'RESET_DELETE_CLARITY'

export const CLARITY_STATUS_SUCCESS = 'CLARITY_STATUS_SUCCESS'
export const CLARITY_STATUS_ERROR = 'CLARITY_STATUS_ERROR'
export const RESET_CLARITY_STATUS = 'RESET_DELETE_CLARITY'

// master color
export const GET_COLOR_LOADING = 'GET_COLOR_LOADING'
export const GET_COLOR_SUCCESS = 'GET_COLOR_SUCCESS'
export const GET_COLOR_ERROR = 'GET_COLOR_ERROR'
export const RESET_GET_COLOR = 'RESET_GET_COLOR'

export const ADD_COLOR_LOADING = 'ADD_COLOR_LOADING'
export const ADD_COLOR_SUCCESS = 'ADD_COLOR_SUCCESS'
export const ADD_COLOR_ERROR = 'ADD_COLOR_ERROR'
export const RESET_ADD_COLOR = 'RESET_ADD_COLOR'

export const UPDATE_COLOR_LOADING = 'UPDATE_COLOR_LOADING'
export const UPDATE_COLOR_SUCCESS = 'UPDATE_COLOR_SUCCESS'
export const UPDATE_COLOR_ERROR = 'UPDATE_COLOR_ERROR'
export const RESET_UPDATE_COLOR = 'RESET_UPDATE_COLOR'

export const DELETE_COLOR_SUCCESS = 'DELETE_COLOR_SUCCESS'
export const DELETE_COLOR_ERROR = 'DELETE_COLOR_ERROR'
export const RESET_DELETE_COLOR = 'RESET_DELETE_COLOR'

export const COLOR_STATUS_SUCCESS = 'COLOR_STATUS_SUCCESS'
export const COLOR_STATUS_ERROR = 'COLOR_STATUS_ERROR'
export const RESET_COLOR_STATUS = 'RESET_DELETE_COLOR'

// master size
export const GET_SIZE_LOADING = 'GET_SIZE_LOADING'
export const GET_SIZE_SUCCESS = 'GET_SIZE_SUCCESS'
export const GET_SIZE_ERROR = 'GET_SIZE_ERROR'
export const RESET_GET_SIZE = 'RESET_GET_SIZE'

export const ADD_SIZE_LOADING = 'ADD_SIZE_LOADING'
export const ADD_SIZE_SUCCESS = 'ADD_SIZE_SUCCESS'
export const ADD_SIZE_ERROR = 'ADD_SIZE_ERROR'
export const RESET_ADD_SIZE = 'RESET_ADD_SIZE'

export const UPDATE_SIZE_LOADING = 'UPDATE_SIZE_LOADING'
export const UPDATE_SIZE_SUCCESS = 'UPDATE_SIZE_SUCCESS'
export const UPDATE_SIZE_ERROR = 'UPDATE_SIZE_ERROR'
export const RESET_UPDATE_SIZE = 'RESET_UPDATE_SIZE'

export const DELETE_SIZE_SUCCESS = 'DELETE_SIZE_SUCCESS'
export const DELETE_SIZE_ERROR = 'DELETE_SIZE_ERROR'
export const RESET_DELETE_SIZE = 'RESET_DELETE_SIZE'

export const SIZE_STATUS_SUCCESS = 'SIZE_STATUS_SUCCESS'
export const SIZE_STATUS_ERROR = 'SIZE_STATUS_ERROR'
export const RESET_SIZE_STATUS = 'RESET_DELETE_SIZE'

// master shape
export const GET_SHAPE_LOADING = 'GET_SHAPE_LOADING'
export const GET_SHAPE_SUCCESS = 'GET_SHAPE_SUCCESS'
export const GET_SHAPE_ERROR = 'GET_SHAPE_ERROR'
export const RESET_GET_SHAPE = 'RESET_GET_SHAPE'

export const ADD_SHAPE_LOADING = 'ADD_SHAPE_LOADING'
export const ADD_SHAPE_SUCCESS = 'ADD_SHAPE_SUCCESS'
export const ADD_SHAPE_ERROR = 'ADD_SHAPE_ERROR'
export const RESET_ADD_SHAPE = 'RESET_ADD_SHAPE'

export const UPDATE_SHAPE_LOADING = 'UPDATE_SHAPE_LOADING'
export const UPDATE_SHAPE_SUCCESS = 'UPDATE_SHAPE_SUCCESS'
export const UPDATE_SHAPE_ERROR = 'UPDATE_SHAPE_ERROR'
export const RESET_UPDATE_SHAPE = 'RESET_UPDATE_SHAPE'

export const DELETE_SHAPE_SUCCESS = 'DELETE_SHAPE_SUCCESS'
export const DELETE_SHAPE_ERROR = 'DELETE_SHAPE_ERROR'
export const RESET_DELETE_SHAPE = 'RESET_DELETE_SHAPE'

export const SHAPE_STATUS_SUCCESS = 'SHAPE_STATUS_SUCCESS'
export const SHAPE_STATUS_ERROR = 'SHAPE_STATUS_ERROR'
export const RESET_SHAPE_STATUS = 'RESET_DELETE_SHAPE'

// master fancy color
export const GET_FANCY_COLOR_LOADING = 'GET_FANCY_COLOR_LOADING'
export const GET_FANCY_COLOR_SUCCESS = 'GET_FANCY_COLOR_SUCCESS'
export const GET_FANCY_COLOR_ERROR = 'GET_FANCY_COLOR_ERROR'
export const RESET_GET_FANCY_COLOR = 'RESET_GET_FANCY_COLOR'

export const ADD_FANCY_COLOR_LOADING = 'ADD_FANCY_COLOR_LOADING'
export const ADD_FANCY_COLOR_SUCCESS = 'ADD_FANCY_COLOR_SUCCESS'
export const ADD_FANCY_COLOR_ERROR = 'ADD_FANCY_COLOR_ERROR'
export const RESET_ADD_FANCY_COLOR = 'RESET_ADD_FANCY_COLOR'

export const UPDATE_FANCY_COLOR_LOADING = 'UPDATE_FANCY_COLOR_LOADING'
export const UPDATE_FANCY_COLOR_SUCCESS = 'UPDATE_FANCY_COLOR_SUCCESS'
export const UPDATE_FANCY_COLOR_ERROR = 'UPDATE_FANCY_COLOR_ERROR'
export const RESET_UPDATE_FANCY_COLOR = 'RESET_UPDATE_FANCY_COLOR'

export const DELETE_FANCY_COLOR_SUCCESS = 'DELETE_FANCY_COLOR_SUCCESS'
export const DELETE_FANCY_COLOR_ERROR = 'DELETE_FANCY_COLOR_ERROR'
export const RESET_DELETE_FANCY_COLOR = 'RESET_DELETE_FANCY_COLOR'

export const FANCY_COLOR_STATUS_SUCCESS = 'FANCY_COLOR_STATUS_SUCCESS'
export const FANCY_COLOR_STATUS_ERROR = 'FANCY_COLOR_STATUS_ERROR'
export const RESET_FANCY_COLOR_STATUS = 'RESET_DELETE_FANCY_COLOR'

// master Finish
export const GET_FINISH_LOADING = 'GET_FINISH_LOADING'
export const GET_FINISH_SUCCESS = 'GET_FINISH_SUCCESS'
export const GET_FINISH_ERROR = 'GET_FINISH_ERROR'
export const RESET_GET_FINISH = 'RESET_GET_FINISH'

export const ADD_FINISH_LOADING = 'ADD_FINISH_LOADING'
export const ADD_FINISH_SUCCESS = 'ADD_FINISH_SUCCESS'
export const ADD_FINISH_ERROR = 'ADD_FINISH_ERROR'
export const RESET_ADD_FINISH = 'RESET_ADD_FINISH'

export const UPDATE_FINISH_LOADING = 'UPDATE_FINISH_LOADING'
export const UPDATE_FINISH_SUCCESS = 'UPDATE_FINISH_SUCCESS'
export const UPDATE_FINISH_ERROR = 'UPDATE_FINISH_ERROR'
export const RESET_UPDATE_FINISH = 'RESET_UPDATE_FINISH'

export const DELETE_FINISH_SUCCESS = 'DELETE_FINISH_SUCCESS'
export const DELETE_FINISH_ERROR = 'DELETE_FINISH_ERROR'
export const RESET_DELETE_FINISH = 'RESET_DELETE_FINISH'

export const FINISH_STATUS_SUCCESS = 'FINISH_STATUS_SUCCESS'
export const FINISH_STATUS_ERROR = 'FINISH_STATUS_ERROR'
export const RESET_FINISH_STATUS = 'RESET_DELETE_FINISH'

// master fluorescence
export const GET_FLUORESCENCE_LOADING = 'GET_FLUORESCENCE_LOADING'
export const GET_FLUORESCENCE_SUCCESS = 'GET_FLUORESCENCE_SUCCESS'
export const GET_FLUORESCENCE_ERROR = 'GET_FLUORESCENCE_ERROR'
export const RESET_GET_FLUORESCENCE = 'RESET_GET_FLUORESCENCE'

export const ADD_FLUORESCENCE_LOADING = 'ADD_FLUORESCENCE_LOADING'
export const ADD_FLUORESCENCE_SUCCESS = 'ADD_FLUORESCENCE_SUCCESS'
export const ADD_FLUORESCENCE_ERROR = 'ADD_FLUORESCENCE_ERROR'
export const RESET_ADD_FLUORESCENCE = 'RESET_ADD_FLUORESCENCE'

export const UPDATE_FLUORESCENCE_LOADING = 'UPDATE_FLUORESCENCE_LOADING'
export const UPDATE_FLUORESCENCE_SUCCESS = 'UPDATE_FLUORESCENCE_SUCCESS'
export const UPDATE_FLUORESCENCE_ERROR = 'UPDATE_FLUORESCENCE_ERROR'
export const RESET_UPDATE_FLUORESCENCE = 'RESET_UPDATE_FLUORESCENCE'

export const DELETE_FLUORESCENCE_SUCCESS = 'DELETE_FLUORESCENCE_SUCCESS'
export const DELETE_FLUORESCENCE_ERROR = 'DELETE_FLUORESCENCE_ERROR'
export const RESET_DELETE_FLUORESCENCE = 'RESET_DELETE_FLUORESCENCE'

export const FLUORESCENCE_STATUS_SUCCESS = 'FLUORESCENCE_STATUS_SUCCESS'
export const FLUORESCENCE_STATUS_ERROR = 'FLUORESCENCE_STATUS_ERROR'
export const RESET_FLUORESCENCE_STATUS = 'RESET_DELETE_FLUORESCENCE'


//  getcountry details

export const GET_COUNTRY_DETAILS_LOADING = 'GET_COUNTRY_DETAILS_LOADING'
export const GET_COUNTRY_DETAILS_SUCCESS = 'GET_COUNTRY_DETAILS_SUCCESS'
export const GET_COUNTRY_DETAILS_ERROR = 'GET_COUNTRY_DETAILS_ERROR'
export const RESET_GET_COUNTRY_DETAILS = 'RESET_GET_COUNTRY_DETAILS'



//  user management

export const GET_USER_LIST_LOADING = 'GET_USER_LIST_LOADING'
export const GET_USER_LIST_SUCCESS = 'GET_USER_LIST_SUCCESS'
export const GET_USER_LIST_ERROR = 'GET_USER_LIST_ERROR'
export const RESET_GET_USER_LIST = 'RESET_GET_USER_LIST'

export const ADD_USER_LIST_LOADING = 'ADD_USER_LIST_LOADING'
export const ADD_USER_LIST_SUCCESS = 'ADD_USER_LIST_SUCCESS'
export const ADD_USER_LIST_ERROR = 'ADD_USER_LIST_ERROR'
export const RESET_ADD_USER_LIST = 'RESET_ADD_USER_LIST'

export const UPDATE_USER_LIST_LOADING = 'UPDATE_USER_LIST_LOADING'
export const UPDATE_USER_LIST_SUCCESS = 'UPDATE_USER_LIST_SUCCESS'
export const UPDATE_USER_LIST_ERROR = 'UPDATE_USER_LIST_ERROR'
export const RESET_UPDATE_USER_LIST = 'RESET_UPDATE_USER_LIST'

export const DELETE_USER_LIST_SUCCESS = 'DELETE_USER_LIST_SUCCESS'
export const DELETE_USER_LIST_ERROR = 'DELETE_USER_LIST_ERROR'
export const RESET_DELETE_USER_LIST = 'RESET_DELETE_USER_LIST'

export const USER_LIST_STATUS_SUCCESS = 'USER_LIST_STATUS_SUCCESS'
export const USER_LIST_STATUS_ERROR = 'USER_LIST_STATUS_ERROR'
export const RESET_USER_LIST_STATUS = 'RESET_USER_LIST_STATUS'

//  stock management

export const GET_STOCK_LIST_LOADING = 'GET_STOCK_LIST_LOADING'
export const GET_STOCK_LIST_SUCCESS = 'GET_STOCK_LIST_SUCCESS'
export const GET_STOCK_LIST_ERROR = 'GET_STOCK_LIST_ERROR'
export const RESET_GET_STOCK_LIST = 'RESET_GET_STOCK_LIST'

export const ADD_STOCK_LIST_LOADING = 'ADD_STOCK_LIST_LOADING'
export const ADD_STOCK_LIST_SUCCESS = 'ADD_STOCK_LIST_SUCCESS'
export const ADD_STOCK_LIST_ERROR = 'ADD_STOCK_LIST_ERROR'
export const RESET_ADD_STOCK_LIST = 'RESET_ADD_STOCK_LIST'

export const UPDATE_STOCK_LIST_LOADING = 'UPDATE_STOCK_LIST_LOADING'
export const UPDATE_STOCK_LIST_SUCCESS = 'UPDATE_STOCK_LIST_SUCCESS'
export const UPDATE_STOCK_LIST_ERROR = 'UPDATE_STOCK_LIST_ERROR'
export const RESET_UPDATE_STOCK_LIST = 'RESET_UPDATE_STOCK_LIST'

export const DELETE_STOCK_LIST_SUCCESS = 'DELETE_STOCK_LIST_SUCCESS'
export const DELETE_STOCK_LIST_ERROR = 'DELETE_STOCK_LIST_ERROR'
export const RESET_DELETE_STOCK_LIST = 'RESET_DELETE_STOCK_LIST'

export const STOCK_LIST_STATUS_SUCCESS = 'STOCK_LIST_STATUS_SUCCESS'
export const STOCK_LIST_STATUS_ERROR = 'STOCK_LIST_STATUS_ERROR'
export const RESET_STOCK_LIST_STATUS = 'RESET_STOCK_LIST_STATUS'

// all master list
export const GET_ALL_MASTERS_LIST_LOADING = 'GET_ALL_MASTERS_LIST_LOADING'
export const GET_ALL_MASTERS_LIST_SUCCESS = 'GET_ALL_MASTERS_LIST_SUCCESS'
export const GET_ALL_MASTERS_LIST_ERROR = 'GET_ALL_MASTERS_LIST_ERROR'
export const RESET_GET_ALL_MASTERS_LIST = 'RESET_GET_ALL_MASTERS_LIST'

// Upload csv file and save
export const UPLOAD_CSV_LOADING = 'UPLOAD_CSV_LOADING'
export const UPLOAD_CSV_SUCCESS = 'UPLOAD_CSV_SUCCESS'
export const UPLOAD_CSV_ERROR = 'UPLOAD_CSV_ERROR'
export const RESET_UPLOAD_CSV = 'RESET_UPLOAD_CSV'

export const SAVE_CSV_LOADING = 'SAVE_CSV_LOADING'
export const SAVE_CSV_SUCCESS = 'SAVE_CSV_SUCCESS'
export const SAVE_CSV_ERROR = 'SAVE_CSV_ERROR'
export const RESET_SAVE_CSV = 'RESET_SAVE_CSV'


//  policy
export const GET_POLICY_LOADING = 'GET_POLICY_LOADING'
export const GET_POLICY_SUCCESS = 'GET_POLICY_SUCCESS'
export const GET_POLICY_ERROR = 'GET_POLICY_ERROR'
export const RESET_GET_POLICY = 'RESET_GET_POLICY'

export const ADD_POLICY_LOADING = 'ADD_POLICY_LOADING'
export const ADD_POLICY_SUCCESS = 'ADD_POLICY_SUCCESS'
export const ADD_POLICY_ERROR = 'ADD_POLICY_ERROR'
export const RESET_ADD_POLICY = 'RESET_ADD_POLICY'

export const DELETE_POLICY_SUCCESS = 'DELETE_POLICY_SUCCESS'
export const DELETE_POLICY_ERROR = 'DELETE_POLICY_ERROR'
export const RESET_DELETE_POLICY = 'RESET_DELETE_POLICY'

export const POLICY_STATUS_SUCCESS = 'POLICY_STATUS_SUCCESS'
export const POLICY_STATUS_ERROR = 'POLICY_STATUS_ERROR'
export const RESET_POLICY_STATUS = 'RESET_DELETE_POLICY'

//  marketing
export const GET_MARKETING_LOADING = 'GET_MARKETING_LOADING'
export const GET_MARKETING_SUCCESS = 'GET_MARKETING_SUCCESS'
export const GET_MARKETING_ERROR = 'GET_MARKETING_ERROR'
export const RESET_GET_MARKETING = 'RESET_GET_MARKETING'

export const ADD_MARKETING_LOADING = 'ADD_MARKETING_LOADING'
export const ADD_MARKETING_SUCCESS = 'ADD_MARKETING_SUCCESS'
export const ADD_MARKETING_ERROR = 'ADD_MARKETING_ERROR'
export const RESET_ADD_MARKETING = 'RESET_ADD_MARKETING'

export const DELETE_MARKETING_SUCCESS = 'DELETE_MARKETING_SUCCESS'
export const DELETE_MARKETING_ERROR = 'DELETE_MARKETING_ERROR'
export const RESET_DELETE_MARKETING = 'RESET_DELETE_MARKETING'

export const MARKETING_STATUS_SUCCESS = 'MARKETING_STATUS_SUCCESS'
export const MARKETING_STATUS_ERROR = 'MARKETING_STATUS_ERROR'
export const RESET_MARKETING_STATUS = 'RESET_DELETE_MARKETING'

//  slot

export const GET_SLOT_LOADING = 'GET_SLOT_LOADING'
export const GET_SLOT_SUCCESS = 'GET_SLOT_SUCCESS'
export const GET_SLOT_ERROR = 'GET_SLOT_ERROR'
export const RESET_GET_SLOT = 'RESET_GET_SLOT'

export const ADD_SLOT_LOADING = 'ADD_SLOT_LOADING'
export const ADD_SLOT_SUCCESS = 'ADD_SLOT_SUCCESS'
export const ADD_SLOT_ERROR = 'ADD_SLOT_ERROR'
export const RESET_ADD_SLOT = 'RESET_ADD_SLOT'

export const UPDATE_SLOT_LOADING = 'UPDATE_SLOT_LOADING'
export const UPDATE_SLOT_SUCCESS = 'UPDATE_SLOT_SUCCESS'
export const UPDATE_SLOT_ERROR = 'UPDATE_SLOT_ERROR'
export const RESET_UPDATE_SLOT = 'RESET_UPDATE_SLOT'

export const DELETE_SLOT_SUCCESS = 'DELETE_SLOT_SUCCESS'
export const DELETE_SLOT_ERROR = 'DELETE_SLOT_ERROR'
export const RESET_DELETE_SLOT = 'RESET_DELETE_SLOT'

export const SLOT_STATUS_SUCCESS = 'SLOT_STATUS_SUCCESS'
export const SLOT_STATUS_ERROR = 'SLOT_STATUS_ERROR'
export const RESET_SLOT_STATUS = 'RESET_DELETE_SLOT'

//  requestList

export const GET_REQUEST_LIST_LOADING = 'GET_REQUEST_LIST_LOADING'
export const GET_REQUEST_LIST_SUCCESS = 'GET_REQUEST_LIST_SUCCESS'
export const GET_REQUEST_LIST_ERROR = 'GET_REQUEST_LIST_ERROR'
export const RESET_GET_REQUEST_LIST = 'RESET_GET_REQUEST_LIST'


//  request list status change
export const REQUEST_LIST_STATUS_LOADING = 'REQUEST_LIST_STATUS_LOADING'
export const REQUEST_LIST_STATUS_SUCCESS = 'REQUEST_LIST_STATUS_SUCCESS'
export const REQUEST_LIST_STATUS_ERROR = 'REQUEST_LIST_STATUS_ERROR'
export const RESET_REQUEST_LIST_STATUS = 'RESET_REQUEST_LIST_STATUS'


//  request list delete
export const REQUEST_LIST_DELETE_LOADING = 'REQUEST_LIST_DELETE_LOADING'
export const REQUEST_LIST_DELETE_SUCCESS = 'REQUEST_LIST_DELETE_SUCCESS'
export const REQUEST_LIST_DELETE_ERROR = 'REQUEST_LIST_DELETE_ERROR'
export const RESET_REQUEST_LIST_DELETE = 'RESET_REQUEST_LIST_DELETE'


//  hold diamond

export const GET_HOLD_DIAMOND_LIST_LOADING = 'GET_HOLD_DIAMOND_LIST_LOADING'
export const GET_HOLD_DIAMOND_LIST_SUCCESS = 'GET_HOLD_DIAMOND_LIST_SUCCESS'
export const GET_HOLD_DIAMOND_LIST_ERROR = 'GET_HOLD_DIAMOND_LIST_ERROR'
export const RESET_GET_HOLD_DIAMOND_LIST = 'RESET_GET_HOLD_DIAMOND_LIST'

export const GET_HOLD_DIAMOND_USER_LOADING = 'GET_HOLD_DIAMOND_USER_LOADING'
export const GET_HOLD_DIAMOND_USER_SUCCESS = 'GET_HOLD_DIAMOND_USER_SUCCESS'
export const GET_HOLD_DIAMOND_USER_ERROR = 'GET_HOLD_DIAMOND_USER_ERROR'
export const RESET_GET_HOLD_DIAMOND_USER = 'RESET_GET_HOLD_DIAMOND_USER'

export const DELETE_HOLD_STONE_LOADING = 'DELETE_HOLD_STONE_LOADING'
export const DELETE_HOLD_STONE_SUCCESS = 'DELETE_HOLD_STONE_SUCCESS'
export const DELETE_HOLD_STONE_ERROR = 'DELETE_HOLD_STONE_ERROR'
export const RESET_DELETE_HOLD_STONE = 'RESET_DELETE_HOLD_STONE'


//  confirm diamond diamond

export const GET_CONFIRM_DIAMOND_LIST_LOADING = 'GET_CONFIRM_DIAMOND_LIST_LOADING'
export const GET_CONFIRM_DIAMOND_LIST_SUCCESS = 'GET_CONFIRM_DIAMOND_LIST_SUCCESS'
export const GET_CONFIRM_DIAMOND_LIST_ERROR = 'GET_CONFIRM_DIAMOND_LIST_ERROR'
export const RESET_GET_CONFIRM_DIAMOND_LIST = 'RESET_GET_CONFIRM_DIAMOND_LIST'

export const GET_CONFIRM_DIAMOND_USER_LOADING = 'GET_CONFIRM_DIAMOND_USER_LOADING'
export const GET_CONFIRM_DIAMOND_USER_SUCCESS = 'GET_CONFIRM_DIAMOND_USER_SUCCESS'
export const GET_CONFIRM_DIAMOND_USER_ERROR = 'GET_CONFIRM_DIAMOND_USER_ERROR'
export const RESET_GET_CONFIRM_DIAMOND_USER = 'RESET_GET_CONFIRM_DIAMOND_USER'


export const RELEASE_LOADING = 'RELEASE_LOADING'
export const RELEASE_SUCCESS = 'RELEASE_SUCCESS'
export const RELEASE_ERROR = 'RELEASE_ERROR'
export const RESET_RELEASE = 'RESET_RELEASE'

//  cart list

export const GET_CART_LIST_LOADING = 'GET_CART_LIST_LOADING'
export const GET_CART_LIST_SUCCESS = 'GET_CART_LIST_SUCCESS'
export const GET_CART_LIST_ERROR = 'GET_CART_LIST_ERROR'
export const RESET_GET_CART_LIST = 'RESET_GET_CART_LIST'

export const GET_CART_USER_LOADING = 'GET_CART_USER_LOADING'
export const GET_CART_USER_SUCCESS = 'GET_CART_USER_SUCCESS'
export const GET_CART_USER_ERROR = 'GET_CART_USER_ERROR'
export const RESET_GET_CART_USER = 'RESET_GET_CART_USER'

export const DELETE_CART_STONE_LOADING = 'DELETE_CART_STONE_LOADING'
export const DELETE_CART_STONE_SUCCESS = 'DELETE_CART_STONE_SUCCESS'
export const DELETE_CART_STONE_ERROR = 'DELETE_CART_STONE_ERROR'
export const RESET_DELETE_CART_STONE = 'RESET_DELETE_CART_STONE'


//  inquriy list

export const GET_INQUIRY_LIST_LOADING = 'GET_INQUIRY_LIST_LOADING'
export const GET_INQUIRY_LIST_SUCCESS = 'GET_INQUIRY_LIST_SUCCESS'
export const GET_INQUIRY_LIST_ERROR = 'GET_INQUIRY_LIST_ERROR'
export const RESET_GET_INQUIRY_LIST = 'RESET_GET_INQUIRY_LIST'

export const GET_INQUIRY_USER_LOADING = 'GET_INQUIRY_USER_LOADING'
export const GET_INQUIRY_USER_SUCCESS = 'GET_INQUIRY_USER_SUCCESS'
export const GET_INQUIRY_USER_ERROR = 'GET_INQUIRY_USER_ERROR'
export const RESET_GET_INQUIRY_USER = 'RESET_GET_INQUIRY_USER'

export const DELETE_INQUIRY_STONE_LOADING = 'DELETE_INQUIRY_STONE_LOADING'
export const DELETE_INQUIRY_STONE_SUCCESS = 'DELETE_INQUIRY_STONE_SUCCESS'
export const DELETE_INQUIRY_STONE_ERROR = 'DELETE_INQUIRY_STONE_ERROR'
export const RESET_DELETE_INQUIRY_STONE = 'RESET_DELETE_INQUIRY_STONE'

//   appoiment

export const GET_APPOIMENT_LOADING = 'GET_APPOIMENT_LOADING'
export const GET_APPOIMENT_SUCCESS = 'GET_APPOIMENT_SUCCESS'
export const GET_APPOIMENT_ERROR = 'GET_APPOIMENT_ERROR'
export const RESET_GET_APPOIMENT = 'RESET_GET_APPOIMENT'

export const DELETE_APPOIMENT_SUCCESS = 'DELETE_APPOIMENT_SUCCESS'
export const DELETE_APPOIMENT_ERROR = 'DELETE_APPOIMENT_ERROR'
export const RESET_DELETE_APPOIMENT = 'RESET_DELETE_APPOIMENT'

export const APPOIMENT_STATUS_SUCCESS = 'APPOIMENT_STATUS_SUCCESS'
export const APPOIMENT_STATUS_ERROR = 'APPOIMENT_STATUS_ERROR'
export const RESET_APPOIMENT_STATUS = 'RESET_DELETE_APPOIMENT'

//   demamd

export const GET_DEMAND_LOADING = 'GET_DEMAND_LOADING'
export const GET_DEMAND_SUCCESS = 'GET_DEMAND_SUCCESS'
export const GET_DEMAND_ERROR = 'GET_DEMAND_ERROR'
export const RESET_GET_DEMAND = 'RESET_GET_DEMAND'

export const DELETE_DEMAND_SUCCESS = 'DELETE_DEMAND_SUCCESS'
export const DELETE_DEMAND_ERROR = 'DELETE_DEMAND_ERROR'
export const RESET_DELETE_DEMAND = 'RESET_DELETE_DEMAND'

export const DEMAND_STATUS_SUCCESS = 'DEMAND_STATUS_SUCCESS'
export const DEMAND_STATUS_ERROR = 'DEMAND_STATUS_ERROR'
export const RESET_DEMAND_STATUS = 'RESET_DELETE_DEMAND'


//  star melee

export const GET_STAR_MELEE_LOADING = 'GET_STAR_MELEE_LOADING'
export const GET_STAR_MELEE_SUCCESS = 'GET_STAR_MELEE_SUCCESS'
export const GET_STAR_MELEE_ERROR = 'GET_STAR_MELEE_ERROR'
export const RESET_GET_STAR_MELEE = 'RESET_GET_STAR_MELEE'

export const ADD_STAR_MELEE_LOADING = 'ADD_STAR_MELEE_LOADING'
export const ADD_STAR_MELEE_SUCCESS = 'ADD_STAR_MELEE_SUCCESS'
export const ADD_STAR_MELEE_ERROR = 'ADD_STAR_MELEE_ERROR'
export const RESET_ADD_STAR_MELEE = 'RESET_ADD_STAR_MELEE'

export const UPDATE_STAR_MELEE_LOADING = 'UPDATE_STAR_MELEE_LOADING'
export const UPDATE_STAR_MELEE_SUCCESS = 'UPDATE_STAR_MELEE_SUCCESS'
export const UPDATE_STAR_MELEE_ERROR = 'UPDATE_STAR_MELEE_ERROR'
export const RESET_UPDATE_STAR_MELEE = 'RESET_UPDATE_STAR_MELEE'

export const DELETE_STAR_MELEE_SUCCESS = 'DELETE_STAR_MELEE_SUCCESS'
export const DELETE_STAR_MELEE_ERROR = 'DELETE_STAR_MELEE_ERROR'
export const RESET_DELETE_STAR_MELEE = 'RESET_DELETE_STAR_MELEE'

export const STAR_MELEE_STATUS_SUCCESS = 'STAR_MELEE_STATUS_SUCCESS'
export const STAR_MELEE_STATUS_ERROR = 'STAR_MELEE_STATUS_ERROR'
export const RESET_STAR_MELEE_STATUS = 'RESET_STAR_MELEE_STATUS'

export const STAR_MELEE_INQUIRY_LOADING = 'STAR_MELEE_INQUIRY_LOADING'
export const STAR_MELEE_INQUIRY_SUCCESS = 'STAR_MELEE_INQUIRY_SUCCESS'
export const STAR_MELEE_INQUIRY_ERROR = 'STAR_MELEE_INQUIRY_ERROR'
export const RESET_STAR_MELEE_INQUIRY = 'RESET_STAR_MELEE_INQUIRY'



// Upload star melee
export const UPLOAD_CSV_STAR_MELEE_LOADING = 'UPLOAD_CSV_STAR_MELEE_LOADING'
export const UPLOAD_CSV_STAR_MELEE_SUCCESS = 'UPLOAD_CSV_STAR_MELEE_SUCCESS'
export const UPLOAD_CSV_STAR_MELEE_ERROR = 'UPLOAD_CSV_STAR_MELEE_ERROR'
export const RESET_UPLOAD_CSV_STAR_MELEE = 'RESET_UPLOAD_CSV'




//  vendor management

export const GET_VENDOR_LIST_LOADING = 'GET_VENDOR_LIST_LOADING'
export const GET_VENDOR_LIST_SUCCESS = 'GET_VENDOR_LIST_SUCCESS'
export const GET_VENDOR_LIST_ERROR = 'GET_VENDOR_LIST_ERROR'
export const RESET_GET_VENDOR_LIST = 'RESET_GET_VENDOR_LIST'

export const GET_VENDOR_DIAMOND_LIST_LOADING = 'GET_VENDOR_DIAMOND_LIST_LOADING'
export const GET_VENDOR_DIAMOND_LIST_SUCCESS = 'GET_VENDOR_DIAMOND_LIST_SUCCESS'
export const GET_VENDOR_DIAMOND_LIST_ERROR = 'GET_VENDOR_DIAMOND_LIST_ERROR'
export const RESET_GET_VENDOR_DIAMOND_LIST = 'RESET_GET_VENDOR_DIAMOND_LIST'

export const ADD_VENDOR_LIST_LOADING = 'ADD_VENDOR_LIST_LOADING'
export const ADD_VENDOR_LIST_SUCCESS = 'ADD_VENDOR_LIST_SUCCESS'
export const ADD_VENDOR_LIST_ERROR = 'ADD_VENDOR_LIST_ERROR'
export const RESET_ADD_VENDOR_LIST = 'RESET_ADD_VENDOR_LIST'

export const UPDATE_VENDOR_LIST_LOADING = 'UPDATE_VENDOR_LIST_LOADING'
export const UPDATE_VENDOR_LIST_SUCCESS = 'UPDATE_VENDOR_LIST_SUCCESS'
export const UPDATE_VENDOR_LIST_ERROR = 'UPDATE_VENDOR_LIST_ERROR'
export const RESET_UPDATE_VENDOR_LIST = 'RESET_UPDATE_VENDOR_LIST'

export const DELETE_VENDOR_LIST_SUCCESS = 'DELETE_VENDOR_LIST_SUCCESS'
export const DELETE_VENDOR_LIST_ERROR = 'DELETE_VENDOR_LIST_ERROR'
export const RESET_DELETE_VENDOR_LIST = 'RESET_DELETE_VENDOR_LIST'

export const VENDOR_LIST_STATUS_SUCCESS = 'VENDOR_LIST_STATUS_SUCCESS'
export const VENDOR_LIST_STATUS_ERROR = 'VENDOR_LIST_STATUS_ERROR'
export const RESET_VENDOR_LIST_STATUS = 'RESET_DELETE_VENDOR_LIST'

//  vendor country
export const VENDOR_COUNTRY_LIST_SUCCESS = 'VENDOR_COUNTRY_LIST_SUCCESS'
export const VENDOR_COUNTRY_LIST_ERROR = 'VENDOR_COUNTRY_LIST_ERROR'
export const RESET_VENDOR_COUNTRY_LIST = 'RESET_DELETE_VENDOR_LIST'



//  merchant management

export const GET_MERCHANT_LIST_LOADING = 'GET_MERCHANT_LIST_LOADING'
export const GET_MERCHANT_LIST_SUCCESS = 'GET_MERCHANT_LIST_SUCCESS'
export const GET_MERCHANT_LIST_ERROR = 'GET_MERCHANT_LIST_ERROR'
export const RESET_GET_MERCHANT_LIST = 'RESET_GET_MERCHANT_LIST'

export const ADD_MERCHANT_LIST_LOADING = 'ADD_MERCHANT_LIST_LOADING'
export const ADD_MERCHANT_LIST_SUCCESS = 'ADD_MERCHANT_LIST_SUCCESS'
export const ADD_MERCHANT_LIST_ERROR = 'ADD_MERCHANT_LIST_ERROR'
export const RESET_ADD_MERCHANT_LIST = 'RESET_ADD_MERCHANT_LIST'

export const UPDATE_MERCHANT_LIST_LOADING = 'UPDATE_MERCHANT_LIST_LOADING'
export const UPDATE_MERCHANT_LIST_SUCCESS = 'UPDATE_MERCHANT_LIST_SUCCESS'
export const UPDATE_MERCHANT_LIST_ERROR = 'UPDATE_MERCHANT_LIST_ERROR'
export const RESET_UPDATE_MERCHANT_LIST = 'RESET_UPDATE_MERCHANT_LIST'

export const DELETE_MERCHANT_LIST_SUCCESS = 'DELETE_MERCHANT_LIST_SUCCESS'
export const DELETE_MERCHANT_LIST_ERROR = 'DELETE_MERCHANT_LIST_ERROR'
export const RESET_DELETE_MERCHANT_LIST = 'RESET_DELETE_MERCHANT_LIST'

export const MERCHANT_LIST_STATUS_SUCCESS = 'MERCHANT_LIST_STATUS_SUCCESS'
export const MERCHANT_LIST_STATUS_ERROR = 'MERCHANT_LIST_STATUS_ERROR'
export const RESET_MERCHANT_LIST_STATUS = 'RESET_MERCHANT_LIST_STATUS'

//  MERCHANT country
export const MERCHANT_COUNTRY_LIST_SUCCESS = 'MERCHANT_COUNTRY_LIST_SUCCESS'
export const MERCHANT_COUNTRY_LIST_ERROR = 'MERCHANT_COUNTRY_LIST_ERROR'
export const RESET_MERCHANT_COUNTRY_LIST = 'RESET_MERCHANT_COUNTRY_LIST'


// Invalid Stone
export const INVALID_STONE_LIST_SUCCESS = 'INVALID_STONE_LIST_SUCCESS'
export const INVALID_STONE_LIST_ERROR = 'INVALID_STONE_LIST_ERROR'
export const RESET_INVALID_STONE_LIST = 'RESET_INVALID_STONE_LIST'


// upload history
export const UPLOAD_HISTORY_SUCCESS = 'UPLOAD_HISTORY_SUCCESS'
export const UPLOAD_HISTORY_ERROR = 'UPLOAD_HISTORY_ERROR'
export const RESET_UPLOAD_HISTORY = 'RESET_UPLOAD_HISTORY'

export const UPLOAD_HISTORY_DOWNLOAD_SUCCESS = 'UPLOAD_HISTORY_DOWNLOAD_SUCCESS'
export const UPLOAD_HISTORY_DOWNLOAD_ERROR = 'UPLOAD_HISTORY_DOWNLOAD_ERROR'
export const RESET_UPLOAD_HISTORY_DOWNLOAD = 'RESET_UPLOAD_HISTORY_DOWNLOAD'

// diamond reset
export const DIAMOND_RESET_SUCCESS = 'DIAMOND_RESET_SUCCESS'
export const DIAMOND_RESET_ERROR = 'DIAMOND_RESET_ERROR'
export const RESET_DIAMOND_RESET = 'RESET_DIAMOND_RESET'
