import axios from "axios";
import { ADD_MARKETING_ERROR, ADD_MARKETING_LOADING, ADD_MARKETING_SUCCESS, DELETE_MARKETING_ERROR, DELETE_MARKETING_SUCCESS, GET_MARKETING_ERROR, GET_MARKETING_LOADING, GET_MARKETING_SUCCESS, MARKETING_STATUS_ERROR, MARKETING_STATUS_SUCCESS } from "../Constant";

export const getMarketing = () => {
  return (dispatch) => {
    dispatch({
      type: GET_MARKETING_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `marketing`,
    };

    axios(requestOptions)
      .then((resp) => {

        dispatch({
          type: GET_MARKETING_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_MARKETING_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_MARKETING_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addMarketing = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_MARKETING_LOADING,
    });
    const requestOptions = {
      method: "POST",
      headers: {
        "Content-Type": "multipart/form-data",
      },
      url: `marketing/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_MARKETING_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_MARKETING_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_MARKETING_ERROR
            });
          }
      });
  };
}

export const deleteMarketing = (id) => {
  return (dispatch) => {
    const requestOptions = {
      method: "DELETE",
      url: `marketing/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_MARKETING_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_MARKETING_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_MARKETING_ERROR,
            });
          }
      });
  };
}

export const changeMarketingStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `marketing/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: MARKETING_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: MARKETING_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: MARKETING_STATUS_ERROR,
            });
          }
      });
  };
}