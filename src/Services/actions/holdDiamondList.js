import axios from "axios";
import { DELETE_HOLD_STONE_ERROR, DELETE_HOLD_STONE_LOADING, DELETE_HOLD_STONE_SUCCESS, GET_HOLD_DIAMOND_LIST_ERROR, GET_HOLD_DIAMOND_LIST_LOADING, GET_HOLD_DIAMOND_LIST_SUCCESS, GET_HOLD_DIAMOND_USER_ERROR, GET_HOLD_DIAMOND_USER_LOADING, GET_HOLD_DIAMOND_USER_SUCCESS } from "../Constant";
export const getHoldDiamond = () => {
  return (dispatch) => {
    dispatch({
      type: GET_HOLD_DIAMOND_LIST_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `hold`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_HOLD_DIAMOND_LIST_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_HOLD_DIAMOND_LIST_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_HOLD_DIAMOND_LIST_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const getHoldDiamondUser = (id) => {
  return (dispatch) => {
    dispatch({
      type: GET_HOLD_DIAMOND_USER_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `hold/user/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_HOLD_DIAMOND_USER_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_HOLD_DIAMOND_USER_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_HOLD_DIAMOND_USER_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const deleteHoldStone = (details) => {
  return (dispatch) => {
    dispatch({
      type: DELETE_HOLD_STONE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `hold/delete`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_HOLD_STONE_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_HOLD_STONE_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: DELETE_HOLD_STONE_ERROR,
              errors: validationError
            });
          }
      });
  };
}