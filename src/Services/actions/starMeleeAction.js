import axios from "axios";
import { ADD_STAR_MELEE_ERROR, ADD_STAR_MELEE_LOADING, ADD_STAR_MELEE_SUCCESS, DELETE_STAR_MELEE_ERROR, DELETE_STAR_MELEE_SUCCESS, GET_STAR_MELEE_ERROR, GET_STAR_MELEE_LOADING, GET_STAR_MELEE_SUCCESS, RESET_STAR_MELEE_INQUIRY, STAR_MELEE_INQUIRY_ERROR, STAR_MELEE_INQUIRY_LOADING, STAR_MELEE_INQUIRY_SUCCESS, STAR_MELEE_STATUS_ERROR, STAR_MELEE_STATUS_SUCCESS, UPDATE_STAR_MELEE_ERROR, UPDATE_STAR_MELEE_LOADING, UPDATE_STAR_MELEE_SUCCESS, UPLOAD_CSV_STAR_MELEE_ERROR, UPLOAD_CSV_STAR_MELEE_LOADING, UPLOAD_CSV_STAR_MELEE_SUCCESS } from "../Constant";

export const getStarMelee = () => {
  return (dispatch) => {
    dispatch({
      type: GET_STAR_MELEE_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `starmelee`,
    };

    axios(requestOptions)
      .then((resp) => {

        dispatch({
          type: GET_STAR_MELEE_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_STAR_MELEE_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_STAR_MELEE_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addStarMelee = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_STAR_MELEE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `starmelee/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_STAR_MELEE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        const msg = error.response.data.error.error_message
        if (!error.response) {
          dispatch({
            type: ADD_STAR_MELEE_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_STAR_MELEE_ERROR
            });
          } else {
            dispatch({
              type: ADD_STAR_MELEE_ERROR,
              errorMsg: msg
            });
          }
      });
  };
}

export const updateStarMelee = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_STAR_MELEE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `starmelee/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_STAR_MELEE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        const msg = error.response.data.error.error_message
        if (!error.response) {
          dispatch({
            type: UPDATE_STAR_MELEE_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_STAR_MELEE_ERROR,
            });
          }
          else {
            dispatch({
              type: UPDATE_STAR_MELEE_ERROR,
              errorMsg: msg
            });
          }
      });
  };
}

export const deleteStarMelee = (id) => {
  return (dispatch) => {
    const requestOptions = {
      method: "DELETE",
      url: `starmelee/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_STAR_MELEE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_STAR_MELEE_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_STAR_MELEE_ERROR,
            });
          }
      });
  };
}

export const changeStarMeleeStatus = ({ id, details }) => {
  return (dispatch) => {
    const requestOptions = {
      method: "POST",
      url: `starmelee/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: STAR_MELEE_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: STAR_MELEE_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: STAR_MELEE_STATUS_ERROR,
            });
          }
      });
  };
}

export const uploadStarMeleeCsvFile = (details) => {
  return (dispatch) => {
    dispatch({
      type: UPLOAD_CSV_STAR_MELEE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      headers: {
        "Content-Type": "multipart/form-data",
      },
      url: `starmelee/import`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPLOAD_CSV_STAR_MELEE_SUCCESS,
          response: {
            data: resp.data.data
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPLOAD_CSV_STAR_MELEE_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPLOAD_CSV_STAR_MELEE_ERROR
            });
          }
      });
  };
}


export const getStarMeleeInquiry = () => {
  return (dispatch) => {
    dispatch({
      type: STAR_MELEE_INQUIRY_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `starmelee/inquiry`,
    };

    axios(requestOptions)
      .then((resp) => {

        dispatch({
          type: STAR_MELEE_INQUIRY_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: STAR_MELEE_INQUIRY_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: STAR_MELEE_INQUIRY_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const downloadStarmeleeFile = () => {
  return (dispatch) => {

    const requestOptions = {
      method: "GET",
      url: `export/starmelee`,
      'responseType': 'blob'
    };

    axios(requestOptions)
      .then((resp) => {
        const blob = new Blob([resp.data]);
        const fileName = `STAR_MELEE.xlsx`
        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(blob);
        a.download = fileName;
        document.body.appendChild(a);

        a.click();
        document.body.removeChild(a);
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
          }
      });
  };
}
