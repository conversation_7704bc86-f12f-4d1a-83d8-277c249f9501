import axios from "axios";
import { ADD_POLICY_ERROR, ADD_POLICY_LOADING, ADD_POLICY_SUCCESS, DELETE_POLICY_ERROR, DELETE_POLICY_SUCCESS, GET_POLICY_ERROR, GET_POLICY_LOADING, GET_POLICY_SUCCESS, POLICY_STATUS_ERROR, POLICY_STATUS_SUCCESS, UPDATE_POLICY_ERROR, UPDATE_POLICY_LOADING, UPDATE_POLICY_SUCCESS } from "../Constant";

export const getPolicy = () => {
  return (dispatch) => {
    dispatch({
      type: GET_POLICY_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `policy`,
    };

    axios(requestOptions)
      .then((resp) => {

        dispatch({
          type: GET_POLICY_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_POLICY_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_POLICY_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addPolicy = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_POLICY_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `policy/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_POLICY_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_POLICY_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_POLICY_ERROR
            });
          }
      });
  };
}

export const deletePolicy = (id) => {
  return (dispatch) => {
    const requestOptions = {
      method: "DELETE",
      url: `policy/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_POLICY_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_POLICY_ERROR,
            errorMsg: data.data.error.message
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_POLICY_ERROR,
            });
          } else {
            dispatch({
              type: DELETE_POLICY_ERROR,
              errorMsg: data.data.error.message
            });
          }
      });
  };
}

export const changePolicyStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `policy/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: POLICY_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: POLICY_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: POLICY_STATUS_ERROR,
            });
          }
      });
  };
}