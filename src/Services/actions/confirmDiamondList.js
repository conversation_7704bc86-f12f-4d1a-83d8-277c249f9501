import axios from "axios";
import { GET_CONFIRM_DIAMOND_LIST_ERROR, GET_CONFIRM_DIAMOND_LIST_LOADING, GET_CONFIRM_DIAMOND_LIST_SUCCESS, GET_CONFIRM_DIAMOND_USER_ERROR, GET_CONFIRM_DIAMOND_USER_LOADING, GET_CONFIRM_DIAMOND_USER_SUCCESS, RELEASE_ERROR, RELEASE_LOADING, RELEASE_SUCCESS } from "../Constant";


export const getConfirmDiamond = () => {
  return (dispatch) => {
    dispatch({
      type: GET_CONFIRM_DIAMOND_LIST_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `confirm`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_CONFIRM_DIAMOND_LIST_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_CONFIRM_DIAMOND_LIST_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_CONFIRM_DIAMOND_LIST_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const getConfirmDiamondUser = (id) => {
  return (dispatch) => {
    dispatch({
      type: GET_CONFIRM_DIAMOND_USER_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `confirm/user/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_CONFIRM_DIAMOND_USER_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_CONFIRM_DIAMOND_USER_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_CONFIRM_DIAMOND_USER_ERROR,
              errors: validationError
            });
          }
      });
  };
}


export const releaseList = (details) => {
  return (dispatch) => {
    dispatch({
      type: RELEASE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `confirm/release`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: RELEASE_SUCCESS
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: RELEASE_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: RELEASE_ERROR,
              errors: validationError
            });
          }
      });
  };
}