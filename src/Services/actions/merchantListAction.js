import axios from "axios";
import config from "../../config";
import { ADD_MERCHANT_LIST_ERROR, ADD_MERCHANT_LIST_LOADING, ADD_MERCHANT_LIST_SUCCESS, DELETE_MERCHANT_LIST_ERROR, DELETE_MERCHANT_LIST_SUCCESS, GET_MERCHANT_LIST_ERROR, GET_MERCHANT_LIST_LOADING, GET_MERCHANT_LIST_SUCCESS, MERCHANT_COUNTRY_LIST_ERROR, MERCHANT_COUNTRY_LIST_SUCCESS, MERCHANT_LIST_STATUS_ERROR, MERCHANT_LIST_STATUS_SUCCESS, UPDATE_MERCHANT_LIST_ERROR, UPDATE_MERCHANT_LIST_LOADING, UPDATE_MERCHANT_LIST_SUCCESS } from "../Constant";

export const getMerchantList = () => {
 return (dispatch) => {
  dispatch({
   type: GET_MERCHANT_LIST_LOADING,
  });
  const requestOptions = {
   method: "GET",
   url: `merchant`,
  };

  axios(requestOptions)
   .then((resp) => {

    dispatch({
     type: GET_MERCHANT_LIST_SUCCESS,
     response: {
      data: resp.data.data.result
     }
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: GET_MERCHANT_LIST_ERROR,
      netError: error,
     });

    } else
     if (data.status === 422) {
      let validationError = data.data.message;
      dispatch({
       type: GET_MERCHANT_LIST_ERROR,
       errors: validationError
      });
     }
   });
 };
}

export const addMerchantList = (details) => {
 return (dispatch) => {
  dispatch({
   type: ADD_MERCHANT_LIST_LOADING,
  });
  const requestOptions = {
   method: "POST",
   url: `merchant/create`,
   data: details
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: ADD_MERCHANT_LIST_SUCCESS,
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: ADD_MERCHANT_LIST_ERROR
     });

    } else
     if (data.status === 422) {
      dispatch({
       type: ADD_MERCHANT_LIST_ERROR,
       msg: data.data.error.error_message
      });
     }
   });
 };
}

export const updateMerchantList = ({ details, id }) => {
 return (dispatch) => {
  dispatch({
   type: UPDATE_MERCHANT_LIST_LOADING,
  });
  const requestOptions = {
   method: "POST",
   url: `merchant/update/${id}`,
   data: details
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: UPDATE_MERCHANT_LIST_SUCCESS,
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: UPDATE_MERCHANT_LIST_ERROR,
     });

    } else
     if (data.status === 422) {
      dispatch({
       type: UPDATE_MERCHANT_LIST_ERROR,
      });
     }
   });
 };
}

export const deleteMerchantList = (id) => {
 return (dispatch) => {
  const requestOptions = {
   method: "DELETE",
   url: `merchant/delete/${id}`,
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: DELETE_MERCHANT_LIST_SUCCESS,
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: DELETE_MERCHANT_LIST_ERROR,
     });

    } else
     if (data.status === 422) {
      dispatch({
       type: DELETE_MERCHANT_LIST_ERROR,
      });
     }
   });
 };
}

export const changeMerchantListStatus = ({ id, details }) => {
 return (dispatch) => {

  const requestOptions = {
   method: "POST",
   url: `merchant/status/${id}`,
   data: details
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: MERCHANT_LIST_STATUS_SUCCESS,
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: MERCHANT_LIST_STATUS_ERROR,
     });

    } else
     if (data.status === 422) {
      dispatch({
       type: MERCHANT_LIST_STATUS_ERROR,
      });
     }
   });
 };
}
export const MerchantCountryList = () => {
 return (dispatch) => {

  const requestOptions = {
   method: "GET",
   url: `${config.merchantUrl}country`
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: MERCHANT_COUNTRY_LIST_SUCCESS,
     response: resp.data.data.result
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: MERCHANT_COUNTRY_LIST_ERROR,
     });

    } else
     if (data.status === 422) {
      dispatch({
       type: MERCHANT_COUNTRY_LIST_ERROR,
      });
     }
   });
 };
}

