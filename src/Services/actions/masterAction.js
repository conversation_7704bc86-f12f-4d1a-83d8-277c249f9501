import axios from "axios";
import { ADD_<PERSON><PERSON><PERSON><PERSON>_ERROR, ADD_C<PERSON><PERSON>TY_LOADING, ADD_C<PERSON><PERSON>TY_SUCCESS, ADD_COLOR_ERROR, ADD_COLOR_LOADING, ADD_CO<PERSON>OR_SUCCESS, ADD_<PERSON>NCY_COLOR_ERROR, ADD_FANCY_COLOR_LOADING, ADD_FANCY_COLOR_SUCCESS, ADD_FINISH_ERROR, ADD_FINISH_LOADING, ADD_<PERSON>INISH_SUCCESS, ADD_FLUORESCENCE_ERROR, ADD_FLUORESCENCE_LOADING, ADD_<PERSON>UORESCENCE_SUCCESS, ADD_SH<PERSON>E_ERROR, ADD_SHAPE_LOADING, ADD_SHAPE_SUCCESS, ADD_SIZE_ERROR, ADD_SIZE_LOADING, ADD_SIZE_SUCCESS, CLARITY_STATUS_ERROR, CLARITY_STATUS_SUCCESS, COLOR_STATUS_ERROR, COLOR_STATUS_SUCCESS, DELETE_CLARITY_ERROR, DELETE_CLARITY_SUCCESS, DELETE_COLOR_ERROR, DELETE_COLOR_SUCCESS, DELETE_FANCY_COLOR_ERROR, DELETE_FANCY_COLOR_SUCCESS, DELETE_FINISH_ERROR, DELETE_FINISH_SUCCESS, DELETE_FLUORESCENCE_ERROR, DELETE_FLUORESCENCE_SUCCESS, DELETE_SHAPE_ERROR, DELETE_SHAPE_SUCCESS, DELETE_SIZE_ERROR, DELETE_SIZE_SUCCESS, FANCY_COLOR_STATUS_ERROR, FANCY_COLOR_STATUS_SUCCESS, FINISH_STATUS_ERROR, FINISH_STATUS_SUCCESS, FLUORESCENCE_STATUS_ERROR, FLUORESCENCE_STATUS_SUCCESS, GET_CLARITY_ERROR, GET_CLARITY_LOADING, GET_CLARITY_SUCCESS, GET_COLOR_ERROR, GET_COLOR_LOADING, GET_COLOR_SUCCESS, GET_FANCY_COLOR_ERROR, GET_FANCY_COLOR_LOADING, GET_FANCY_COLOR_SUCCESS, GET_FINISH_ERROR, GET_FINISH_LOADING, GET_FINISH_SUCCESS, GET_FLUORESCENCE_ERROR, GET_FLUORESCENCE_LOADING, GET_FLUORESCENCE_SUCCESS, GET_SHAPE_ERROR, GET_SHAPE_LOADING, GET_SHAPE_SUCCESS, GET_SIZE_ERROR, GET_SIZE_LOADING, GET_SIZE_SUCCESS, RESET_DELETE_COLOR, SHAPE_STATUS_ERROR, SHAPE_STATUS_SUCCESS, SIZE_STATUS_ERROR, SIZE_STATUS_SUCCESS, UPDATE_CLARITY_ERROR, UPDATE_CLARITY_LOADING, UPDATE_CLARITY_SUCCESS, UPDATE_COLOR_ERROR, UPDATE_COLOR_LOADING, UPDATE_COLOR_SUCCESS, UPDATE_FANCY_COLOR_ERROR, UPDATE_FANCY_COLOR_LOADING, UPDATE_FANCY_COLOR_SUCCESS, UPDATE_FINISH_ERROR, UPDATE_FINISH_LOADING, UPDATE_FINISH_SUCCESS, UPDATE_FLUORESCENCE_ERROR, UPDATE_FLUORESCENCE_LOADING, UPDATE_FLUORESCENCE_SUCCESS, UPDATE_SHAPE_ERROR, UPDATE_SHAPE_LOADING, UPDATE_SHAPE_SUCCESS, UPDATE_SIZE_ERROR, UPDATE_SIZE_LOADING, UPDATE_SIZE_SUCCESS } from "../Constant";

export const getClarity = () => {
  return (dispatch) => {
    dispatch({
      type: GET_CLARITY_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `clarity`,
    };

    axios(requestOptions)
      .then((resp) => {

        dispatch({
          type: GET_CLARITY_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_CLARITY_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_CLARITY_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addClarity = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_CLARITY_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `clarity/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_CLARITY_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_CLARITY_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_CLARITY_ERROR
            });
          }
      });
  };
}

export const updateClarity = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_CLARITY_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `clarity/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_CLARITY_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPDATE_CLARITY_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_CLARITY_ERROR,
            });
          }
      });
  };
}

export const deleteClarity = (id) => {
  return (dispatch) => {

    const requestOptions = {
      method: "DELETE",
      url: `clarity/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_CLARITY_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_CLARITY_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_CLARITY_ERROR,
            });
          }
      });
  };
}

export const changeClarityStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `clarity/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: CLARITY_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: CLARITY_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: CLARITY_STATUS_ERROR,
            });
          }
      });
  };
}

// ******************** color *******************
export const getColor = () => {
  return (dispatch) => {
    dispatch({
      type: GET_COLOR_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `color`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_COLOR_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_COLOR_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_COLOR_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addColor = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_COLOR_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `color/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_COLOR_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_COLOR_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_COLOR_ERROR
            });
          }
      });
  };
}

export const updateColor = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_COLOR_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `color/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_COLOR_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPDATE_COLOR_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_COLOR_ERROR,
            });
          }
      });
  };
}

export const deleteColor = (id) => {
  return (dispatch) => {

    const requestOptions = {
      method: "DELETE",
      url: `color/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_COLOR_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_COLOR_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_COLOR_ERROR,
            });
          }
      });
  };
}

export const changeColorStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `color/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: COLOR_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: COLOR_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: COLOR_STATUS_ERROR,
            });
          }
      });
  };
}

// ******************** size *******************
export const getSize = () => {
  return (dispatch) => {
    dispatch({
      type: GET_SIZE_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `size`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_SIZE_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_SIZE_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_SIZE_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addSize = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_SIZE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `size/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_SIZE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_SIZE_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_SIZE_ERROR
            });
          }
      });
  };
}

export const updateSize = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_SIZE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `size/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_SIZE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPDATE_SIZE_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_SIZE_ERROR,
            });
          }
      });
  };
}

export const deleteSize = (id) => {
  return (dispatch) => {

    const requestOptions = {
      method: "DELETE",
      url: `size/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_SIZE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_SIZE_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_SIZE_ERROR,
            });
          }
      });
  };
}

export const changeSizeStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `size/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: SIZE_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: SIZE_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: SIZE_STATUS_ERROR,
            });
          }
      });
  };
}

// ******************** shape *******************
export const getShape = () => {
  return (dispatch) => {
    dispatch({
      type: GET_SHAPE_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `shape`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_SHAPE_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_SHAPE_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_SHAPE_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addShape = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_SHAPE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `shape/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_SHAPE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_SHAPE_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_SHAPE_ERROR
            });
          }
      });
  };
}

export const updateShape = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_SHAPE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `shape/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_SHAPE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPDATE_SHAPE_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_SHAPE_ERROR,
            });
          }
      });
  };
}

export const deleteShape = (id) => {
  return (dispatch) => {

    const requestOptions = {
      method: "DELETE",
      url: `shape/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_SHAPE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_SHAPE_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_SHAPE_ERROR,
            });
          }
      });
  };
}

export const changeShapeStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `shape/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: SHAPE_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: SHAPE_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: SHAPE_STATUS_ERROR,
            });
          }
      });
  };
}

// ******************** fancy color *******************
export const getFancyColor = () => {
  return (dispatch) => {
    dispatch({
      type: GET_FANCY_COLOR_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `fancy-color`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_FANCY_COLOR_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_FANCY_COLOR_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_FANCY_COLOR_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addFancyColor = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_FANCY_COLOR_LOADING,
    });
    const requestOptions = {
      method: "POST",
      headers: {
        "Content-Type": "multipart/form-data",
      },
      url: `fancy-color/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_FANCY_COLOR_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_FANCY_COLOR_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_FANCY_COLOR_ERROR
            });
          }
      });
  };
}

export const updateFancyColor = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_FANCY_COLOR_LOADING,
    });
    const requestOptions = {
      method: "POST",
      headers: {
        "Content-Type": "multipart/form-data",
      },
      url: `fancy-color/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_FANCY_COLOR_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPDATE_FANCY_COLOR_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_FANCY_COLOR_ERROR,
            });
          }
      });
  };
}

export const deleteFancyColor = (id) => {
  return (dispatch) => {

    const requestOptions = {
      method: "DELETE",
      url: `fancy-color/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_FANCY_COLOR_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_FANCY_COLOR_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_FANCY_COLOR_ERROR,
            });
          }
      });
  };
}

export const changeFancyColorStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `fancy-color/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: FANCY_COLOR_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: FANCY_COLOR_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: FANCY_COLOR_STATUS_ERROR,
            });
          }
      });
  };
}

// ******************** fancy color *******************
export const getFinish = () => {
  return (dispatch) => {
    dispatch({
      type: GET_FINISH_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `finish`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_FINISH_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_FINISH_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_FINISH_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addFinish = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_FINISH_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `finish/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_FINISH_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_FINISH_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_FINISH_ERROR
            });
          }
      });
  };
}

export const updateFinish = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_FINISH_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `finish/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_FINISH_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPDATE_FINISH_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_FINISH_ERROR,
            });
          }
      });
  };
}

export const deleteFinish = (id) => {
  return (dispatch) => {

    const requestOptions = {
      method: "DELETE",
      url: `finish/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_FINISH_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_FINISH_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_FINISH_ERROR,
            });
          }
      });
  };
}

export const changeFinishStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `finish/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: FINISH_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: FINISH_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: FINISH_STATUS_ERROR,
            });
          }
      });
  };
}


// ******************** Fluorescence *******************
export const getFluorescence = () => {
  return (dispatch) => {
    dispatch({
      type: GET_FLUORESCENCE_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `fluorescence`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_FLUORESCENCE_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_FLUORESCENCE_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_FLUORESCENCE_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addFluorescence = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_FLUORESCENCE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `fluorescence/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_FLUORESCENCE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_FLUORESCENCE_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_FLUORESCENCE_ERROR
            });
          }
      });
  };
}

export const updateFluorescence = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_FLUORESCENCE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `fluorescence/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_FLUORESCENCE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPDATE_FLUORESCENCE_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_FLUORESCENCE_ERROR,
            });
          }
      });
  };
}

export const deleteFluorescence = (id) => {
  return (dispatch) => {

    const requestOptions = {
      method: "DELETE",
      url: `fluorescence/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_FLUORESCENCE_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_FLUORESCENCE_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_FLUORESCENCE_ERROR,
            });
          }
      });
  };
}

export const changeFluorescenceStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `fluorescence/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: FLUORESCENCE_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: FLUORESCENCE_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: FLUORESCENCE_STATUS_ERROR,
            });
          }
      });
  };
}