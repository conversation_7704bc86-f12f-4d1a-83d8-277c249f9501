import axios from "axios";
import { INVALID_STONE_LIST_ERROR, INVALID_STONE_LIST_SUCCESS } from "../Constant";

export const getInvalidStoneList = (details) => {
 return (dispatch) => {

  const requestOptions = {
   method: "POST",
   url: `product/invalid`,
   data: details
  };

  axios(requestOptions)
   .then((resp) => {

    dispatch({
     type: INVALID_STONE_LIST_SUCCESS,
     response: {
      data: resp.data.data.result
     }
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: INVALID_STONE_LIST_ERROR,
      netError: error,
     });

    } else
     if (data.status === 422) {
      let validationError = data.data.message;
      dispatch({
       type: INVALID_STONE_LIST_ERROR,
       errors: validationError
      });
     }
   });
 };
}
