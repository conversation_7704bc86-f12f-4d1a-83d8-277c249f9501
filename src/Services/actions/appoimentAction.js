import axios from "axios";
import { APPOIMENT_STATUS_ERROR, APPOIMENT_STATUS_SUCCESS, DELETE_APPOIMENT_ERROR, DELETE_APPOIMENT_SUCCESS, GET_APPOIMENT_ERROR, GET_APPOIMENT_LOADING, GET_APPOIMENT_SUCCESS } from "../Constant";

export const getAppoiment = () => {
 return (dispatch) => {
  dispatch({
   type: GET_APPOIMENT_LOADING,
  });
  const requestOptions = {
   method: "GET",
   url: `appointment`,
  };

  axios(requestOptions)
   .then((resp) => {

    dispatch({
     type: GET_APPOIMENT_SUCCESS,
     response: {
      data: resp.data.data.result
     }
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: GET_APPOIMENT_ERROR,
      netError: error,
     });

    } else
     if (data.status === 422) {
      let validationError = data.data.message;
      dispatch({
       type: GET_APPOIMENT_ERROR,
       errors: validationError
      });
     }
   });
 };
}


export const deleteAppoiment = (id) => {
 return (dispatch) => {

  const requestOptions = {
   method: "DELETE",
   url: `appointment/delete/${id}`,
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: DELETE_APPOIMENT_SUCCESS,
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: DELETE_APPOIMENT_ERROR,
     });

    } else
     if (data.status === 422) {
      dispatch({
       type: DELETE_APPOIMENT_ERROR,
      });
     }
   });
 };
}

export const changeAppoimentStatus = ({ id, details }) => {
 return (dispatch) => {

  const requestOptions = {
   method: "POST",
   url: `appointment/status/${id}`,
   data: details
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: APPOIMENT_STATUS_SUCCESS,
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: APPOIMENT_STATUS_ERROR,
     });

    } else
     if (data.status === 422) {
      dispatch({
       type: APPOIMENT_STATUS_ERROR,
      });
     }
   });
 };
}