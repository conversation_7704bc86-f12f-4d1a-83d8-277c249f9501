import axios from "axios";
import { ADD_SLOT_ERROR, ADD_SLOT_LOADING, ADD_SLOT_SUCCESS, DELETE_SLOT_ERROR, DELETE_SLOT_SUCCESS, GET_REQUEST_LIST_ERROR, GET_REQUEST_LIST_LOADING, GET_REQUEST_LIST_SUCCESS, GET_SLOT_ERROR, GET_SLOT_LOADING, GET_SLOT_SUCCESS, REQUEST_LIST_DELETE_ERROR, REQUEST_LIST_DELETE_LOADING, REQUEST_LIST_DELETE_SUCCESS, REQUEST_LIST_STATUS_ERROR, REQUEST_LIST_STATUS_LOADING, REQUEST_LIST_STATUS_SUCCESS, SLOT_STATUS_ERROR, SLOT_STATUS_SUCCESS, UPDATE_SLOT_ERROR, UPDATE_SLOT_LOADING, UPDATE_SLOT_SUCCESS } from "../Constant";

export const getSlot = () => {
  return (dispatch) => {
    dispatch({
      type: GET_SLOT_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `slot`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_SLOT_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_SLOT_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_SLOT_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addSlot = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_SLOT_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `slot/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_SLOT_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_SLOT_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_SLOT_ERROR
            });
          }
      });
  };
}

export const updateSlot = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_SLOT_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `slot/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_SLOT_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPDATE_SLOT_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_SLOT_ERROR,
            });
          }
      });
  };
}

export const deleteSlot = (id) => {
  return (dispatch) => {

    const requestOptions = {
      method: "DELETE",
      url: `slot/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_SLOT_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_SLOT_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_SLOT_ERROR,
            });
          } else if (data.status === 500) {
            dispatch({
              type: DELETE_SLOT_ERROR,
              errroMsg: data.data.error.message
            });
          }
      });
  };
}

export const changeSlotStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `slot/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: SLOT_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: SLOT_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: SLOT_STATUS_ERROR,
            });
          }
      });
  };
}

export const getViewRequestList = () => {
  return (dispatch) => {
    dispatch({
      type: GET_REQUEST_LIST_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `request`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_REQUEST_LIST_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_REQUEST_LIST_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_REQUEST_LIST_ERROR,
              errors: validationError
            });
          }
      });
  };
}
export const changeStatusViewRequestList = ({ id, details }) => {
  return (dispatch) => {
    dispatch({
      type: REQUEST_LIST_STATUS_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `/request/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: REQUEST_LIST_STATUS_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: REQUEST_LIST_STATUS_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: REQUEST_LIST_STATUS_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const deleteViewRequestList = (details) => {
  return (dispatch) => {
    dispatch({
      type: REQUEST_LIST_DELETE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `/request/delete`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: REQUEST_LIST_DELETE_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: REQUEST_LIST_DELETE_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: REQUEST_LIST_DELETE_ERROR,
              errors: validationError
            });
          }
      });
  };
}
