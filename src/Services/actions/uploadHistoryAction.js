import axios from "axios";
import { UPLOAD_HISTORY_DOWNLOAD_ERROR, UPLOAD_HISTORY_DOWNLOAD_SUCCESS, UPLOAD_HISTORY_ERROR, UPLOAD_HISTORY_SUCCESS } from "../Constant";
import moment from "moment";

export const getUploadHistoryList = () => {
 return (dispatch) => {

  const requestOptions = {
   method: "GET",
   url: `product/history`,
  };

  axios(requestOptions)
   .then((resp) => {

    dispatch({
     type: UPLOAD_HISTORY_SUCCESS,
     response: {
      data: resp.data.data.result
     }
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: UPLOAD_HISTORY_ERROR,
      netError: error,
     });

    } else
     if (data.status === 422) {
      let validationError = data.data.message;
      dispatch({
       type: UPLOAD_HISTORY_ERROR,
       errors: validationError
      });
     }
   });
 };
}


export const downloadUploadHistory = (id) => {
 return (dispatch) => {
   const requestOptions = {
     method: "GET",
     url: `product/history/${id}`,
     headers: {
       'Authorization': `Bearer ${localStorage.getItem("token")}`
     },
     'responseType': 'blob'
   }
   axios(requestOptions)
     .then((resp) => {
       const blob = new Blob([resp.data]);
       const fileName = `DD_UPLOAD_HISTORY_${moment().format('DDMMyyyy')}_${Math.floor(100000 + Math.random() * 9000)}.xlsx`
       const a = document.createElement('a');
       a.href = window.URL.createObjectURL(blob);
       a.download = fileName;
       document.body.appendChild(a);
       a.click();
       document.body.removeChild(a);
       dispatch({
         type: UPLOAD_HISTORY_DOWNLOAD_SUCCESS,
       });
     })
     .catch((error) => {
       let data = error.response;
       if (!error.response) {
         dispatch({
           type: UPLOAD_HISTORY_DOWNLOAD_ERROR,
           msg: error,
         });
       } else if (data.status === 422) {
         let validationError = data.data.message;
         dispatch({
           type: UPLOAD_HISTORY_DOWNLOAD_ERROR,
           msg: validationError,
         });
       }
     });
 };
};
