import axios from "axios";
import { DELETE_CART_STONE_ERROR, DELETE_CART_STONE_LOADING, DELETE_CART_STONE_SUCCESS, GET_CART_LIST_ERROR, GET_CART_LIST_LOADING, GET_CART_LIST_SUCCESS, GET_CART_USER_ERROR, GET_CART_USER_LOADING, GET_CART_USER_SUCCESS } from "../Constant";


export const getCartList = () => {
 return (dispatch) => {
  dispatch({
   type: GET_CART_LIST_LOADING,
  });
  const requestOptions = {
   method: "GET",
   url: `cart`,
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: GET_CART_LIST_SUCCESS,
     response: {
      data: resp.data.data.result
     }
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: GET_CART_LIST_ERROR,
      netError: error,
     });

    } else
     if (data.status === 422) {
      let validationError = data.data.message;
      dispatch({
       type: GET_CART_LIST_ERROR,
       errors: validationError
      });
     }
   });
 };
}

export const getCartUser = (id) => {
 return (dispatch) => {
  dispatch({
   type: GET_CART_USER_LOADING,
  });
  const requestOptions = {
   method: "GET",
   url: `cart/user/${id}`,
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: GET_CART_USER_SUCCESS,
     response: {
      data: resp.data.data.result
     }
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: GET_CART_USER_ERROR,
      netError: error,
     });

    } else
     if (data.status === 422) {
      let validationError = data.data.message;
      dispatch({
       type: GET_CART_USER_ERROR,
       errors: validationError
      });
     }
   });
 };
}

export const deleteStoneFromCart = (details) => {
 return (dispatch) => {
  dispatch({
   type: DELETE_CART_STONE_LOADING,
  });
  const requestOptions = {
   method: "POST",
   url: `cart/delete`,
   data: details
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: DELETE_CART_STONE_SUCCESS,
     response: {
      data: resp.data.data.result
     }
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: DELETE_CART_STONE_ERROR,
      netError: error,
     });

    } else
     if (data.status === 422) {
      let validationError = data.data.message;
      dispatch({
       type: DELETE_CART_STONE_ERROR,
       errors: validationError
      });
     }
   });
 };
}