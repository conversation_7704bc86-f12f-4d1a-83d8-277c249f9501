import axios from "axios";
import { ADD_STOCK_LIST_ERROR, ADD_STOCK_LIST_LOADING, ADD_STOCK_LIST_SUCCESS, DELETE_STOCK_LIST_ERROR, DELETE_STOCK_LIST_SUCCESS, DIAMOND_RESET_ERROR, DIAMOND_RESET_SUCCESS, GET_ALL_MASTERS_LIST_ERROR, GET_ALL_MASTERS_LIST_LOADING, GET_ALL_MASTERS_LIST_SUCCESS, GET_STOCK_LIST_ERROR, GET_STOCK_LIST_LOADING, GET_STOCK_LIST_SUCCESS, SAVE_CSV_ERROR, SAVE_CSV_LOADING, SAVE_CSV_SUCCESS, STOCK_LIST_STATUS_ERROR, STOCK_LIST_STATUS_SUCCESS, UPDATE_STOCK_LIST_ERROR, UPDATE_STOCK_LIST_LOADING, UPDATE_STOCK_LIST_SUCCESS, UPLOAD_CSV_ERROR, UPLOAD_CSV_LOADING, UPLOAD_CSV_SUCCESS } from "../Constant";

export const getStockList = () => {
  return (dispatch) => {
    dispatch({
      type: GET_STOCK_LIST_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `product`,
    };

    axios(requestOptions)
      .then((resp) => {

        dispatch({
          type: GET_STOCK_LIST_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_STOCK_LIST_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_STOCK_LIST_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addStockList = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_STOCK_LIST_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `product/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_STOCK_LIST_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        const msg = error.response.data.error.error_message
        if (!error.response) {
          dispatch({
            type: ADD_STOCK_LIST_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_STOCK_LIST_ERROR
            });
          } else {
            dispatch({
              type: ADD_STOCK_LIST_ERROR,
              errorMsg: msg
            });
          }
      });
  };
}

export const updateStockList = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_STOCK_LIST_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `product/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_STOCK_LIST_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        const msg = error.response.data.error.error_message
        if (!error.response) {
          dispatch({
            type: UPDATE_STOCK_LIST_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_STOCK_LIST_ERROR,
            });
          }
          else {
            dispatch({
              type: ADD_STOCK_LIST_ERROR,
              errorMsg: msg
            });
          }
      });
  };
}

export const deleteStockList = (id) => {
  return (dispatch) => {
    const requestOptions = {
      method: "DELETE",
      url: `product/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_STOCK_LIST_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_STOCK_LIST_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_STOCK_LIST_ERROR,
            });
          }
      });
  };
}

export const changeStockListStatus = ({ id, details }) => {
  return (dispatch) => {
    const requestOptions = {
      method: "POST",
      url: `product/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: STOCK_LIST_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: STOCK_LIST_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: STOCK_LIST_STATUS_ERROR,
            });
          }
      });
  };
}

export const getAllMastersList = () => {
  return (dispatch) => {
    dispatch({
      type: GET_ALL_MASTERS_LIST_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `product/master`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_ALL_MASTERS_LIST_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_ALL_MASTERS_LIST_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_ALL_MASTERS_LIST_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const uploadCsvFile = (details) => {
  return (dispatch) => {
    dispatch({
      type: UPLOAD_CSV_LOADING,
    });
    const requestOptions = {
      method: "POST",
      headers: {
        "Content-Type": "multipart/form-data",
      },
      url: `product/import`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPLOAD_CSV_SUCCESS,
          response: {
            data: resp.data.data
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPLOAD_CSV_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPLOAD_CSV_ERROR
            });
          } else if (data.status === 500) {
            dispatch({
              type: UPLOAD_CSV_ERROR
            });
          }
      });
  };
}

export const saveCsvFile = ({ details, id, diamond_type }) => {
  return (dispatch) => {
    dispatch({
      type: SAVE_CSV_LOADING,
    });
    const requestOptions = {
      method: "POST",
      headers: {
        "Content-Type": "multipart/form-data",
      },
      url: `product/import/${id}`,
      data: {
        diamond_type: diamond_type,
        data: JSON.stringify([...details])
      }
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: SAVE_CSV_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: SAVE_CSV_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: SAVE_CSV_ERROR
            });
          }
      });
  };
}

export const resetDiamondList = (id) => {
  return (dispatch) => {
    const requestOptions = {
      method: "GET",
      url: `product/reset/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {

        dispatch({
          type: DIAMOND_RESET_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DIAMOND_RESET_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: DIAMOND_RESET_ERROR,
              errors: validationError
            });
          }
      });
  };
}
