import axios from "axios";
import { DELETE_DEMAND_ERROR, DELETE_DEMAND_SUCCESS, DEMAND_STATUS_ERROR, DEMAND_STATUS_SUCCESS, GET_DEMAND_ERROR, GET_DEMAND_LOADING, GET_DEMAND_SUCCESS } from "../Constant";

export const getDemandList = () => {
 return (dispatch) => {
  dispatch({
   type: GET_DEMAND_LOADING,
  });
  const requestOptions = {
   method: "GET",
   url: `demand`,
  };

  axios(requestOptions)
   .then((resp) => {

    dispatch({
     type: GET_DEMAND_SUCCESS,
     response: {
      data: resp.data.data.result
     }
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: GET_DEMAND_ERROR,
      netError: error,
     });

    } else
     if (data.status === 422) {
      let validationError = data.data.message;
      dispatch({
       type: GET_DEMAND_ERROR,
       errors: validationError
      });
     }
   });
 };
}


export const deleteDemandList = (id) => {
 return (dispatch) => {

  const requestOptions = {
   method: "DELETE",
   url: `demand/delete/${id}`,
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: DELETE_DEMAND_SUCCESS,
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: DELETE_DEMAND_ERROR,
     });

    } else
     if (data.status === 422) {
      dispatch({
       type: DELETE_DEMAND_ERROR,
      });
     }
   });
 };
}

export const changeDemandListStatus = ({ id, details }) => {
 return (dispatch) => {

  const requestOptions = {
   method: "POST",
   url: `demand/status/${id}`,
   data: details
  };

  axios(requestOptions)
   .then((resp) => {
    dispatch({
     type: DEMAND_STATUS_SUCCESS,
    })
   })
   .catch((error) => {
    let data = error.response;
    if (!error.response) {
     dispatch({
      type: DEMAND_STATUS_ERROR,
     });

    } else
     if (data.status === 422) {
      dispatch({
       type: DEMAND_STATUS_ERROR,
      });
     }
   });
 };
}