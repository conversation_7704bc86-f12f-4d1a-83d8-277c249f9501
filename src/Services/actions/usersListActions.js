import axios from "axios";
import { ADD_USER_LIST_ERROR, ADD_USER_LIST_LOADING, ADD_USER_LIST_SUCCESS, DELETE_USER_LIST_ERROR, DELETE_USER_LIST_SUCCESS, GET_COUNTRY_DETAILS_ERROR, GET_COUNTRY_DETAILS_LOADING, GET_COUNTRY_DETAILS_SUCCESS, GET_USER_LIST_ERROR, GET_USER_LIST_LOADING, GET_USER_LIST_SUCCESS, UPDATE_USER_LIST_ERROR, UPDATE_USER_LIST_LOADING, UPDATE_USER_LIST_SUCCESS, USER_LIST_STATUS_ERROR, USER_LIST_STATUS_SUCCESS } from "../Constant";

export const getUserList = () => {
  return (dispatch) => {
    dispatch({
      type: GET_USER_LIST_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `user`,
    };

    axios(requestOptions)
      .then((resp) => {

        dispatch({
          type: GET_USER_LIST_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_USER_LIST_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_USER_LIST_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addUserList = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_USER_LIST_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `user/create`,
      headers: { "content-Type": "multipart/form-data" },
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_USER_LIST_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_USER_LIST_ERROR
          });
        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_USER_LIST_ERROR,
              msg: data.data.error.error_message
            });
          }
      });
  };
}

export const updateUserList = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_USER_LIST_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `user/update/${id}`,
      headers: { "content-Type": "multipart/form-data" },
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_USER_LIST_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPDATE_USER_LIST_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_USER_LIST_ERROR,
            });
          }
      });
  };
}

export const deleteUserList = (id) => {
  return (dispatch) => {
    const requestOptions = {
      method: "DELETE",
      url: `user/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_USER_LIST_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_USER_LIST_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_USER_LIST_ERROR,
            });
          }
      });
  };
}

export const changeUserListStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `user/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: USER_LIST_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: USER_LIST_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: USER_LIST_STATUS_ERROR,
            });
          }
      });
  };
}

export const getCountryList = () => {
  return (dispatch) => {
    dispatch({
      type: GET_COUNTRY_DETAILS_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `country`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_COUNTRY_DETAILS_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_COUNTRY_DETAILS_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_COUNTRY_DETAILS_ERROR,
              errors: validationError
            });
          }
      });
  };
}
