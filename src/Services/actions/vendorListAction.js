import axios from "axios";
import { ADD_VENDOR_LIST_ERROR, ADD_VENDOR_LIST_LOADING, ADD_VENDOR_LIST_SUCCESS, DELETE_VENDOR_LIST_ERROR, DELETE_VENDOR_LIST_SUCCESS, GET_VENDOR_DIAMOND_LIST_ERROR, GET_VENDOR_DIAMOND_LIST_LOADING, GET_VENDOR_DIAMOND_LIST_SUCCESS, GET_VENDOR_LIST_ERROR, GET_VENDOR_LIST_LOADING, GET_VENDOR_LIST_SUCCESS, UPDATE_VENDOR_LIST_ERROR, UPDATE_VENDOR_LIST_LOADING, UPDATE_VENDOR_LIST_SUCCESS, VENDOR_COUNTRY_LIST_ERROR, VENDOR_COUNTRY_LIST_SUCCESS, VENDOR_LIST_STATUS_ERROR, VENDOR_LIST_STATUS_SUCCESS } from "../Constant";
import config from "../../config";

export const getVendorList = () => {
  return (dispatch) => {
    dispatch({
      type: GET_VENDOR_LIST_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `vendor`,
    };

    axios(requestOptions)
      .then((resp) => {

        dispatch({
          type: GET_VENDOR_LIST_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_VENDOR_LIST_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_VENDOR_LIST_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const addVendorList = (details) => {
  return (dispatch) => {
    dispatch({
      type: ADD_VENDOR_LIST_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `vendor/create`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: ADD_VENDOR_LIST_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: ADD_VENDOR_LIST_ERROR
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: ADD_VENDOR_LIST_ERROR,
              msg: data.data.error.error_message
            });
          }
      });
  };
}

export const updateVendorList = ({ details, id }) => {
  return (dispatch) => {
    dispatch({
      type: UPDATE_VENDOR_LIST_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `vendor/update/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: UPDATE_VENDOR_LIST_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: UPDATE_VENDOR_LIST_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: UPDATE_VENDOR_LIST_ERROR,
            });
          }
      });
  };
}

export const deleteVendorList = (id) => {
  return (dispatch) => {
    const requestOptions = {
      method: "DELETE",
      url: `vendor/delete/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_VENDOR_LIST_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_VENDOR_LIST_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: DELETE_VENDOR_LIST_ERROR,
            });
          }
      });
  };
}

export const changeVendorListStatus = ({ id, details }) => {
  return (dispatch) => {

    const requestOptions = {
      method: "POST",
      url: `vendor/status/${id}`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: VENDOR_LIST_STATUS_SUCCESS,
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: VENDOR_LIST_STATUS_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: VENDOR_LIST_STATUS_ERROR,
            });
          }
      });
  };
}
export const VendorCountryList = () => {
  return (dispatch) => {

    const requestOptions = {
      method: "GET",
      url: `${config.vendorUrl}country`
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: VENDOR_COUNTRY_LIST_SUCCESS,
          response: resp.data.data.result
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: VENDOR_COUNTRY_LIST_ERROR,
          });

        } else
          if (data.status === 422) {
            dispatch({
              type: VENDOR_COUNTRY_LIST_ERROR,
            });
          }
      });
  };
}

export const getVEndorDiamondList = ({id, skip}) => {
  return (dispatch) => {
    dispatch({
      type: GET_VENDOR_DIAMOND_LIST_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `/vendor/vendor-wise-diamond/${id}?$count=true&$top=60&$skip=${skip}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_VENDOR_DIAMOND_LIST_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_VENDOR_DIAMOND_LIST_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_VENDOR_DIAMOND_LIST_ERROR,
              errors: validationError
            });
          }
      });
  };
}