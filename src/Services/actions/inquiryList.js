import axios from "axios";
import { DELETE_INQUIRY_STONE_ERROR, DELETE_INQUIRY_STONE_LOADING, DELETE_INQUIRY_STONE_SUCCESS, GET_INQUIRY_LIST_ERROR, GET_INQUIRY_LIST_LOADING, GET_INQUIRY_LIST_SUCCESS, GET_INQUIRY_USER_ERROR, GET_INQUIRY_USER_LOADING, GET_INQUIRY_USER_SUCCESS } from "../Constant";

export const getInquiryList = () => {
  return (dispatch) => {
    dispatch({
      type: GET_INQUIRY_LIST_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `inquiry`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_INQUIRY_LIST_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_INQUIRY_LIST_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_INQUIRY_LIST_ERROR,
              errors: validationError
            });
          }
      });
  };
}

export const getInquiryListUser = (id) => {
  return (dispatch) => {
    dispatch({
      type: GET_INQUIRY_USER_LOADING,
    });
    const requestOptions = {
      method: "GET",
      url: `inquiry/user/${id}`,
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: GET_INQUIRY_USER_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: GET_INQUIRY_USER_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: GET_INQUIRY_USER_ERROR,
              errors: validationError
            });
          }
      });
  };
}


export const deleteInquiry = (details) => {
  return (dispatch) => {
    dispatch({
      type: DELETE_INQUIRY_STONE_LOADING,
    });
    const requestOptions = {
      method: "POST",
      url: `inquiry/delete`,
      data: details
    };

    axios(requestOptions)
      .then((resp) => {
        dispatch({
          type: DELETE_INQUIRY_STONE_SUCCESS,
          response: {
            data: resp.data.data.result
          }
        })
      })
      .catch((error) => {
        let data = error.response;
        if (!error.response) {
          dispatch({
            type: DELETE_INQUIRY_STONE_ERROR,
            netError: error,
          });

        } else
          if (data.status === 422) {
            let validationError = data.data.message;
            dispatch({
              type: DELETE_INQUIRY_STONE_ERROR,
              errors: validationError
            });
          }
      });
  };
}