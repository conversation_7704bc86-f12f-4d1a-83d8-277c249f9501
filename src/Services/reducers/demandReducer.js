import { DELETE_DEMAND_ERROR, DELETE_DEMAND_SUCCESS, DEMAND_STATUS_ERROR, DEMAND_STATUS_SUCCESS, GET_DEMAND_ERROR, GET_DEMAND_LOADING, GET_DEMAND_SUCCESS, RESET_DELETE_DEMAND, RESET_DEMAND_STATUS, RESET_GET_DEMAND } from "../Constant";

let initialState = {

 getDemandList: {
  loading: false,
  error: false,
  data: null,
  success: false,
 },
 deleteDemandList: {
  loading: false,
  error: false,
  success: false,
 },
 statusDemandList: {
  loading: false,
  error: false,
  success: false,
 }


}

export default function demandReducer(state = initialState, action) {
 switch (action.type) {

  //  diamond list
  case GET_DEMAND_LOADING:
   return { ...state, getDemandList: { ...state.getDemandList, loading: true, error: false, success: false } };

  case GET_DEMAND_SUCCESS:
   return { ...state, getDemandList: { ...state.getDemandList, loading: false, error: false, success: true, data: action.response.data } };

  case GET_DEMAND_ERROR:
   return { ...state, getDemandList: { ...state.getDemandList, loading: false, error: true, success: false, data: null } };

  case RESET_GET_DEMAND:
   return { ...state, getDemandList: { ...state.getDemandList, loading: false, error: false, success: false } };

  //  user list
  case DELETE_DEMAND_SUCCESS:
   return { ...state, deleteDemandList: { ...state.deleteDemandList, error: false, success: true } };

  case DELETE_DEMAND_ERROR:
   return { ...state, deleteDemandList: { ...state.deleteDemandList, error: true, success: false } };

  case RESET_DELETE_DEMAND:
   return { ...state, deleteDemandList: { ...state.deleteDemandList, error: false, success: false } };


  //  user list
  case DEMAND_STATUS_SUCCESS:
   return { ...state, statusDemandList: { ...state.statusDemandList, error: false, success: true } };

  case DEMAND_STATUS_ERROR:
   return { ...state, statusDemandList: { ...state.statusDemandList, error: true, success: false } };

  case RESET_DEMAND_STATUS:
   return { ...state, statusDemandList: { ...state.statusDemandList, error: false, success: false } };

  default:
   return state;
 }
}
