import { DELETE_HOLD_STONE_ERROR, DELETE_HOLD_STONE_LOADING, DELETE_HOLD_STONE_SUCCESS, GET_HOLD_DIAMOND_LIST_ERROR, GET_HOLD_DIAMOND_LIST_LOADING, GET_HOLD_DIAMOND_LIST_SUCCESS, GET_HOLD_DIAMOND_USER_ERROR, GET_HOLD_DIAMOND_USER_LOADING, GET_HOLD_DIAMOND_USER_SUCCESS, GET_SLOT_ERROR, GET_SLOT_LOADING, GET_SLOT_SUCCESS, RESET_DELETE_HOLD_STONE, RESET_GET_HOLD_DIAMOND_LIST, RESET_GET_HOLD_DIAMOND_USER, RESET_GET_SLOT } from "../Constant";

let initialState = {

  getHoldDiamondList: {
    loading: false,
    error: false,
    data: null,
    success: false,
  },
  getHoldDiamondUserList: {
    loading: false,
    error: false,
    data: null,
    success: false,
  },
  deleteHoldStone: {
    loading: false,
    error: false,
    data: null,
    success: false,
  },


}

export default function holdDiamondReducer(state = initialState, action) {
  switch (action.type) {

    //  diamond list
    case GET_HOLD_DIAMOND_LIST_LOADING:
      return { ...state, getHoldDiamondList: { ...state.getHoldDiamondList, loading: true, error: false, success: false } };

    case GET_HOLD_DIAMOND_LIST_SUCCESS:
      return { ...state, getHoldDiamondList: { ...state.getHoldDiamondList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_HOLD_DIAMOND_LIST_ERROR:
      return { ...state, getHoldDiamondList: { ...state.getHoldDiamondList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_HOLD_DIAMOND_LIST:
      return { ...state, getHoldDiamondList: { ...state.getHoldDiamondList, loading: false, error: false, success: false } };

    //  user list
    case GET_HOLD_DIAMOND_USER_LOADING:
      return { ...state, getHoldDiamondUserList: { ...state.getHoldDiamondUserList, loading: true, error: false, success: false } };

    case GET_HOLD_DIAMOND_USER_SUCCESS:
      return { ...state, getHoldDiamondUserList: { ...state.getHoldDiamondUserList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_HOLD_DIAMOND_USER_ERROR:
      return { ...state, getHoldDiamondUserList: { ...state.getHoldDiamondUserList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_HOLD_DIAMOND_USER:
      return { ...state, getHoldDiamondUserList: { ...state.getHoldDiamondUserList, loading: false, error: false, success: false } };

    //  delete Stone
    case DELETE_HOLD_STONE_LOADING:
      return { ...state, deleteHoldStone: { ...state.deleteHoldStone, loading: true, error: false, success: false } };

    case DELETE_HOLD_STONE_SUCCESS:
      return { ...state, deleteHoldStone: { ...state.deleteHoldStone, loading: false, error: false, success: true, data: action.response.data } };

    case DELETE_HOLD_STONE_ERROR:
      return { ...state, deleteHoldStone: { ...state.deleteHoldStone, loading: false, error: true, success: false, data: null } };

    case RESET_DELETE_HOLD_STONE:
      return { ...state, deleteHoldStone: { ...state.deleteHoldStone, loading: false, error: false, success: false } };

    default:
      return state;
  }
}
