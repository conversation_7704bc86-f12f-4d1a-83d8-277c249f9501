import { ADD_MERCHANT_LIST_ERROR, ADD_MERCHANT_LIST_LOADING, ADD_MERCHANT_LIST_SUCCESS, DELETE_MERCHANT_LIST_ERROR, DELETE_MERCHANT_LIST_SUCCESS, GET_MERCHANT_LIST_ERROR, GET_MERCHANT_LIST_LOADING, GET_MERCHANT_LIST_SUCCESS, RESET_ADD_MERCHANT_LIST, RESET_DELETE_MERCHANT_LIST, RESET_GET_MERCHANT_LIST, RESET_UPDATE_MERCHANT_LIST, RESET_MERCHANT_COUNTRY_LIST, RESET_MERCHANT_LIST_STATUS, UPDATE_MERCHANT_LIST_ERROR, UPDATE_MERCHANT_LIST_LOADING, UPDATE_MERCHANT_LIST_SUCCESS, MERCHANT_COUNTRY_LIST_ERROR, MERCHANT_COUNTRY_LIST_SUCCESS, MERCHANT_LIST_STATUS_ERROR, MERCHANT_LIST_STATUS_SUCCESS } from "../Constant";

let initialState = {
 MerchantList: {
  loading: false,
  data: null,
  error: false,
  success: false,
 },
 addMerchantList: {
  loading: false,
  data: null,
  error: false,
  success: false,
  msg: null
 },
 updateMerchantList: {
  loading: false,
  data: null,
  error: false,
  success: false,
 },
 deleteMerchantList: {
  error: false,
  success: false,
 },
 statusMerchantList: {
  error: false,
  success: false,
 },
 MerchantCountryList: {
  data: null,
  error: false,
  success: false,
 }
};

export default function merchantReducer(state = initialState, action) {
 switch (action.type) {

  //  get user list
  case GET_MERCHANT_LIST_LOADING:
   return { ...state, MerchantList: { ...state.MerchantList, loading: true, error: false, success: false } };

  case GET_MERCHANT_LIST_SUCCESS:
   return { ...state, MerchantList: { ...state.MerchantList, loading: false, error: false, success: true, data: action.response.data } };

  case GET_MERCHANT_LIST_ERROR:
   return { ...state, MerchantList: { ...state.MerchantList, loading: false, error: true, success: false, data: null } };

  case RESET_GET_MERCHANT_LIST:
   return { ...state, MerchantList: { ...state.MerchantList, loading: false, error: false, success: false } };

  //  add user list
  case ADD_MERCHANT_LIST_LOADING:
   return { ...state, addMerchantList: { ...state.addMerchantList, loading: true, error: false, success: false } };

  case ADD_MERCHANT_LIST_SUCCESS:
   return { ...state, addMerchantList: { ...state.addMerchantList, loading: false, error: false, success: true } };

  case ADD_MERCHANT_LIST_ERROR:
   return { ...state, addMerchantList: { ...state.addMerchantList, loading: false, error: true, success: false, msg: action.msg } };

  case RESET_ADD_MERCHANT_LIST:
   return { ...state, addMerchantList: { ...state.addMerchantList, loading: false, error: false, success: false, msg: null } };


  //  update user list
  case UPDATE_MERCHANT_LIST_LOADING:
   return { ...state, updateMerchantList: { ...state.updateMerchantList, loading: true, error: false, success: false } };

  case UPDATE_MERCHANT_LIST_SUCCESS:
   return { ...state, updateMerchantList: { ...state.updateMerchantList, loading: false, error: false, success: true } };

  case UPDATE_MERCHANT_LIST_ERROR:
   return { ...state, updateMerchantList: { ...state.updateMerchantList, loading: false, error: true, success: false, msg: action.msg } };

  case RESET_UPDATE_MERCHANT_LIST:
   return { ...state, updateMerchantList: { ...state.updateMerchantList, loading: false, error: false, success: false } };


  //  delete user list
  case DELETE_MERCHANT_LIST_SUCCESS:
   return { ...state, deleteMerchantList: { ...state.deleteMerchantList, error: false, success: true } };

  case DELETE_MERCHANT_LIST_ERROR:
   return { ...state, deleteMerchantList: { ...state.deleteMerchantList, error: true, success: false } };

  case RESET_DELETE_MERCHANT_LIST:
   return { ...state, deleteMerchantList: { ...state.deleteMerchantList, error: false, success: false } };

  //  status user list
  case MERCHANT_LIST_STATUS_SUCCESS:
   return { ...state, statusMerchantList: { ...state.statusMerchantList, error: false, success: true } };

  case MERCHANT_LIST_STATUS_ERROR:
   return { ...state, statusMerchantList: { ...state.statusMerchantList, error: true, success: false } };

  case RESET_MERCHANT_LIST_STATUS:
   return { ...state, statusMerchantList: { ...state.statusMerchantList, error: false, success: false } };

  //  vendor  country
  case MERCHANT_COUNTRY_LIST_SUCCESS:
   return { ...state, MerchantCountryList: { ...state.MerchantCountryList, error: false, success: true, data: action.response } };

  case MERCHANT_COUNTRY_LIST_ERROR:
   return { ...state, MerchantCountryList: { ...state.MerchantCountryList, error: true, success: false, data: null } };

  case RESET_MERCHANT_COUNTRY_LIST:
   return { ...state, MerchantCountryList: { ...state.MerchantCountryList, error: false, success: false } };

  default:
   return state;
 }
}
