import { INVALID_STONE_LIST_ERROR, INVALID_STONE_LIST_SUCCESS, RESET_INVALID_STONE_LIST } from "../Constant";

let initialState = {
 invalidStoneList: {
  loading: false,
  data: null,
  error: false,
  success: false,
 }
};

export default function invalidStoneReducer(state = initialState, action) {
 switch (action.type) {

  //  get invalid stone list
  case INVALID_STONE_LIST_SUCCESS:
   return { ...state, invalidStoneList: { ...state.invalidStoneList, loading: false, error: false, success: true, data: action.response.data } };

  case INVALID_STONE_LIST_ERROR:
   return { ...state, invalidStoneList: { ...state.invalidStoneList, loading: false, error: true, success: false, data: null } };

  case RESET_INVALID_STONE_LIST:
   return { ...state, invalidStoneList: { ...state.invalidStoneList, loading: false, error: false, success: false } };

  default:
   return state;
 }
}
