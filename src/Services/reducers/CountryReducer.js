import { GET_COUNTRY_DETAILS_ERROR, GET_COUNTRY_DETAILS_LOADING, GET_COUNTRY_DETAILS_SUCCESS, RESET_GET_COUNTRY_DETAILS } from "../Constant";

let initialState = {
  countryDetails: {
    loading: false,
    data: null,
    error: false,
    success: false,
  }
};

export default function CountryReducer(state = initialState, action) {
  switch (action.type) {

    //  get user list
    case GET_COUNTRY_DETAILS_LOADING:
      return { ...state, countryDetails: { ...state.countryDetails, loading: true, error: false, success: false } };

    case GET_COUNTRY_DETAILS_SUCCESS:
      return { ...state, countryDetails: { ...state.countryDetails, loading: false, error: false, success: true, data: action.response.data } };

    case GET_COUNTRY_DETAILS_ERROR:
      return { ...state, countryDetails: { ...state.countryDetails, loading: false, error: true, success: false, data: null } };

    case RESET_GET_COUNTRY_DETAILS:
      return { ...state, countryDetails: { ...state.countryDetails, loading: false, error: false, success: false } };

    default:
      return state;
  }
}
