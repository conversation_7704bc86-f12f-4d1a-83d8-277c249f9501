import { DELETE_CART_STONE_ERROR, DELETE_CART_STONE_LOADING, DELETE_CART_STONE_SUCCESS, GET_CART_LIST_ERROR, GET_CART_LIST_LOADING, GET_CART_LIST_SUCCESS, GET_CART_USER_ERROR, GET_CART_USER_LOADING, GET_CART_USER_SUCCESS, RESET_DELETE_CART_STONE, RESET_GET_CART_LIST, RESET_GET_CART_USER } from "../Constant";

let initialState = {

  getCartList: {
    loading: false,
    error: false,
    data: null,
    success: false,
  },
  getCartUserList: {
    loading: false,
    error: false,
    data: null,
    success: false,
  },
  deleteStoneCart: {
    loading: false,
    error: false,
    data: null,
    success: false,
  },


}

export default function cartReducer(state = initialState, action) {
  switch (action.type) {

    //  diamond list
    case GET_CART_LIST_LOADING:
      return { ...state, getCartList: { ...state.getCartList, loading: true, error: false, success: false } };

    case GET_CART_LIST_SUCCESS:
      return { ...state, getCartList: { ...state.getCartList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_CART_LIST_ERROR:
      return { ...state, getCartList: { ...state.getCartList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_CART_LIST:
      return { ...state, getCartList: { ...state.getCartList, loading: false, error: false, success: false } };

    //  user list
    case GET_CART_USER_LOADING:
      return { ...state, getCartUserList: { ...state.getCartUserList, loading: true, error: false, success: false } };

    case GET_CART_USER_SUCCESS:
      return { ...state, getCartUserList: { ...state.getCartUserList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_CART_USER_ERROR:
      return { ...state, getCartUserList: { ...state.getCartUserList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_CART_USER:
      return { ...state, getCartUserList: { ...state.getCartUserList, loading: false, error: false, success: false } };


    //  delete stone
    case DELETE_CART_STONE_LOADING:
      return { ...state, deleteStoneCart: { ...state.deleteStoneCart, loading: true, error: false, success: false } };

    case DELETE_CART_STONE_SUCCESS:
      return { ...state, deleteStoneCart: { ...state.deleteStoneCart, loading: false, error: false, success: true, data: action.response.data } };

    case DELETE_CART_STONE_ERROR:
      return { ...state, deleteStoneCart: { ...state.deleteStoneCart, loading: false, error: true, success: false, data: null } };

    case RESET_DELETE_CART_STONE:
      return { ...state, deleteStoneCart: { ...state.deleteStoneCart, loading: false, error: false, success: false } };

    default:
      return state;
  }
}
