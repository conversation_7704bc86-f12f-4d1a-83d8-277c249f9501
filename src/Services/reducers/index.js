import { combineReducers } from 'redux';
import auth from '../reducers/auth';
import navigation from '../reducers/navigation';
import alerts from '../reducers/alerts';
import layout from '../reducers/layout';
import products from '../reducers/products';
import analytics from '../reducers/analytics';
import chat from '../reducers/chat';
import users from '../reducers/usersReducers';
import masterReducer from '../reducers/masterReducer';
import usersListReducers from '../reducers/usersListReducers';
import CountryReducer from '../reducers/CountryReducer';
import stockReducer from '../reducers/stockReducer';
import PolicyReducer from '../reducers/PolicyReducer';
import MarketingReducer from '../reducers/MarketingReducer';
import slotReducer from '../reducers/slotReducer';
import holdDiamondReducer from './holdDiamondReducer';
import confirmDiamondReducer from './confirmDiamondReducer';
import inquiryReducer from './inquiryReducer';
import cartReducer from './cartReducer';
import appoimentReducer from './appoimentReducer';
import demandReducer from './demandReducer';
import starMeleeReducer from './starMeleeReducer';
import vendorListReducer from './vendorListReducer';
import merchantReducer from './merchantReducer';
import invalidStoneReducer from './invalidStoneReducer';
import uploadHistoryReducer from './uploadHistoryReducer';
import { connectRouter } from 'connected-react-router';

export default (history) =>
  combineReducers({
    router: connectRouter(history),
    alerts,
    auth,
    navigation,
    layout,
    products,
    analytics,
    chat,
    users,
    masterReducer,
    usersListReducers,
    CountryReducer,
    stockReducer,
    MarketingReducer,
    slotReducer,
    holdDiamondReducer,
    cartReducer,
    confirmDiamondReducer,
    inquiryReducer,
    appoimentReducer,
    demandReducer,
    starMeleeReducer,
    PolicyReducer,
    merchantReducer,
    invalidStoneReducer,
    uploadHistoryReducer,
    vendorListReducer
  });
