import { ADD_STOCK_LIST_ERROR, ADD_STOCK_LIST_LOADING, ADD_STOCK_LIST_SUCCESS, DELETE_STOCK_LIST_ERROR, DELETE_STOCK_LIST_SUCCESS, DIAMOND_RESET_ERROR, DIAMOND_RESET_SUCCESS, GET_ALL_MASTERS_LIST_ERROR, GET_ALL_MASTERS_LIST_LOADING, GET_ALL_MASTERS_LIST_SUCCESS, GET_STOCK_LIST_ERROR, GET_STOCK_LIST_LOADING, GET_STOCK_LIST_SUCCESS, RESET_ADD_STOCK_LIST, RESET_DELETE_STOCK_LIST, RESET_DIAMOND_RESET, RESET_GET_ALL_MASTERS_LIST, RESET_GET_STOCK_LIST, RESET_SAVE_CSV, RESET_STOCK_LIST_STATUS, RESET_UPDATE_STOCK_LIST, RESET_UPLOAD_CSV, SAVE_CSV_ERROR, SAVE_CSV_LOADING, SAVE_CSV_SUCCESS, STOCK_LIST_STATUS_ERROR, STOCK_LIST_STATUS_SUCCESS, UPDATE_STOCK_LIST_ERROR, UPDATE_STOCK_LIST_LOADING, UPDATE_STOCK_LIST_SUCCESS, UPLOAD_CSV_ERROR, UPLOAD_CSV_LOADING, UPLOAD_CSV_SUCCESS } from "../Constant";

let initialState = {
  getAllMastersList: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  StockList: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  addStockList: {
    loading: false,
    data: null,
    error: false,
    success: false,
    msg: null
  },
  updateStockList: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  deleteStockList: {
    error: false,
    success: false,
  },
  statusStockList: {
    error: false,
    success: false,
  },
  uploadCsvFile: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  saveCsvFile: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  resetDiamond: {
    data: null,
    error: false,
    success: false,
  },
};

export default function stockReducer(state = initialState, action) {
  switch (action.type) {

    //  get all masters
    case GET_ALL_MASTERS_LIST_LOADING:
      return { ...state, getAllMastersList: { ...state.getAllMastersList, loading: true, error: false, success: false } };

    case GET_ALL_MASTERS_LIST_SUCCESS:
      return { ...state, getAllMastersList: { ...state.getAllMastersList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_ALL_MASTERS_LIST_ERROR:
      return { ...state, getAllMastersList: { ...state.getAllMastersList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_ALL_MASTERS_LIST:
      return { ...state, getAllMastersList: { ...state.getAllMastersList, loading: false, error: false, success: false } };

    //  get Stock list
    case GET_STOCK_LIST_LOADING:
      return { ...state, StockList: { ...state.StockList, loading: true, error: false, success: false } };

    case GET_STOCK_LIST_SUCCESS:
      return { ...state, StockList: { ...state.StockList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_STOCK_LIST_ERROR:
      return { ...state, StockList: { ...state.StockList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_STOCK_LIST:
      return { ...state, StockList: { ...state.StockList, loading: false, error: false, success: false } };

    //  add Stock list
    case ADD_STOCK_LIST_LOADING:
      return { ...state, addStockList: { ...state.addStockList, loading: true, error: false, success: false } };

    case ADD_STOCK_LIST_SUCCESS:
      return { ...state, addStockList: { ...state.addStockList, loading: false, error: false, success: true } };

    case ADD_STOCK_LIST_ERROR:
      return { ...state, addStockList: { ...state.addStockList, loading: false, error: true, success: false, msg: action.errorMsg } };

    case RESET_ADD_STOCK_LIST:
      return { ...state, addStockList: { ...state.addStockList, loading: false, error: false, success: false, msg: null } };


    //  update Stock list
    case UPDATE_STOCK_LIST_LOADING:
      return { ...state, updateStockList: { ...state.updateStockList, loading: true, error: false, success: false } };

    case UPDATE_STOCK_LIST_SUCCESS:
      return { ...state, updateStockList: { ...state.updateStockList, loading: false, error: false, success: true } };

    case UPDATE_STOCK_LIST_ERROR:
      return { ...state, updateStockList: { ...state.updateStockList, loading: false, error: true, success: false, msg: action.errorMsg } };

    case RESET_UPDATE_STOCK_LIST:
      return { ...state, updateStockList: { ...state.updateStockList, loading: false, error: false, success: false } };


    //  delete Stock list
    case DELETE_STOCK_LIST_SUCCESS:
      return { ...state, deleteStockList: { ...state.deleteStockList, error: false, success: true } };

    case DELETE_STOCK_LIST_ERROR:
      return { ...state, deleteStockList: { ...state.deleteStockList, error: true, success: false } };

    case RESET_DELETE_STOCK_LIST:
      return { ...state, deleteStockList: { ...state.deleteStockList, error: false, success: false } };

    //  status Stock list
    case STOCK_LIST_STATUS_SUCCESS:
      return { ...state, statusStockList: { ...state.statusStockList, error: false, success: true } };

    case STOCK_LIST_STATUS_ERROR:
      return { ...state, statusStockList: { ...state.statusStockList, error: true, success: false } };

    case RESET_STOCK_LIST_STATUS:
      return { ...state, statusStockList: { ...state.statusStockList, error: false, success: false } };

    //  upload csv
    case UPLOAD_CSV_LOADING:
      return { ...state, uploadCsvFile: { ...state.uploadCsvFile, loading: true, error: false, success: false } };

    case UPLOAD_CSV_SUCCESS:
      return { ...state, uploadCsvFile: { ...state.uploadCsvFile, loading: false, error: false, success: true, data: action.response.data } };

    case UPLOAD_CSV_ERROR:
      return { ...state, uploadCsvFile: { ...state.uploadCsvFile, loading: false, error: true, success: false, data: null } };

    case RESET_UPLOAD_CSV:
      return { ...state, uploadCsvFile: { ...state.uploadCsvFile, loading: false, error: false, success: false } };

    //  save csv
    case SAVE_CSV_LOADING:
      return { ...state, saveCsvFile: { ...state.saveCsvFile, loading: true, error: false, success: false } };

    case SAVE_CSV_SUCCESS:
      return { ...state, saveCsvFile: { ...state.saveCsvFile, loading: false, error: false, success: true, data: action.response.data } };

    case SAVE_CSV_ERROR:
      return { ...state, saveCsvFile: { ...state.saveCsvFile, loading: false, error: true, success: false, data: null } };

    case RESET_SAVE_CSV:
      return { ...state, saveCsvFile: { ...state.saveCsvFile, loading: false, error: false, success: false } };


    //  reset diamond

    case DIAMOND_RESET_SUCCESS:
      return { ...state, resetDiamond: { ...state.resetDiamond, loading: false, error: false, success: true, data: action.response.data } };

    case DIAMOND_RESET_ERROR:
      return { ...state, resetDiamond: { ...state.resetDiamond, loading: false, error: true, success: false, data: null } };

    case RESET_DIAMOND_RESET:
      return { ...state, resetDiamond: { ...state.resetDiamond, loading: false, error: false, success: false } };



    default:
      return state;
  }
}
