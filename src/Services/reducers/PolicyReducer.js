import { ADD_POLICY_ERROR, ADD_POLICY_LOADING, ADD_POLICY_SUCCESS, DELETE_POLICY_ERROR, DELETE_POLICY_SUCCESS, DELETE_STOCK_LIST_ERROR, DELETE_STOCK_LIST_SUCCESS, GET_ALL_MASTERS_LIST_ERROR, GET_ALL_MASTERS_LIST_LOADING, GET_ALL_MASTERS_LIST_SUCCESS, GET_POLICY_ERROR, GET_POLICY_LOADING, GET_POLICY_SUCCESS, GET_STOCK_LIST_ERROR, GET_STOCK_LIST_LOADING, GET_STOCK_LIST_SUCCESS, POLICY_STATUS_ERROR, POLICY_STATUS_SUCCESS, RESET_ADD_POLICY, RESET_ADD_STOCK_LIST, RESET_DELETE_POLICY, RESET_DELETE_STOCK_LIST, RESET_GET_ALL_MASTERS_LIST, RESET_GET_POLICY, RESET_GET_STOCK_LIST, RESET_POLICY_STATUS, RESET_SAVE_CSV, RESET_STOCK_LIST_STATUS, RESET_UPDATE_POLICY, RESET_UPDATE_STOCK_LIST, RESET_UPLOAD_CSV, SAVE_CSV_ERROR, SAVE_CSV_LOADING, SAVE_CSV_SUCCESS, STOCK_LIST_STATUS_ERROR, STOCK_LIST_STATUS_SUCCESS, UPDATE_POLICY_ERROR, UPDATE_POLICY_LOADING, UPDATE_POLICY_SUCCESS, UPDATE_STOCK_LIST_ERROR, UPDATE_STOCK_LIST_LOADING, UPDATE_STOCK_LIST_SUCCESS, UPLOAD_CSV_ERROR, UPLOAD_CSV_LOADING, UPLOAD_CSV_SUCCESS } from "../Constant";

let initialState = {
  getPolicy: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  addPolicy: {
    loading: false,
    data: null,
    error: false,
    success: false,
    msg: null
  },
  deletePolicy: {
    error: false,
    success: false,
    errorMsg: ""
  },
  statusPolicy: {
    error: false,
    success: false,
  },

};

export default function PolicyReducer(state = initialState, action) {
  switch (action.type) {

    //  get policy
    case GET_POLICY_LOADING:
      return { ...state, getPolicy: { ...state.getPolicy, loading: true, error: false, success: false } };

    case GET_POLICY_SUCCESS:
      return { ...state, getPolicy: { ...state.getPolicy, loading: false, error: false, success: true, data: action.response.data } };

    case GET_POLICY_ERROR:
      return { ...state, getPolicy: { ...state.getPolicy, loading: false, error: true, success: false, data: null } };

    case RESET_GET_POLICY:
      return { ...state, getPolicy: { ...state.getPolicy, loading: false, error: false, success: false } };

    //  add Stock list
    case ADD_POLICY_LOADING:
      return { ...state, addPolicy: { ...state.addPolicy, loading: true, error: false, success: false } };

    case ADD_POLICY_SUCCESS:
      return { ...state, addPolicy: { ...state.addPolicy, loading: false, error: false, success: true } };

    case ADD_POLICY_ERROR:
      return { ...state, addPolicy: { ...state.addPolicy, loading: false, error: true, success: false, msg: action.msg } };

    case RESET_ADD_POLICY:
      return { ...state, addPolicy: { ...state.addPolicy, loading: false, error: false, success: false, msg: null } };


    //  delete Stock list
    case DELETE_POLICY_SUCCESS:
      return { ...state, deletePolicy: { ...state.deletePolicy, error: false, success: true } };

    case DELETE_POLICY_ERROR:
      return { ...state, deletePolicy: { ...state.deletePolicy, error: true, success: false, errorMsg: action.errorMsg } };

    case RESET_DELETE_POLICY:
      return { ...state, deletePolicy: { ...state.deletePolicy, error: false, success: false } };

    //  status Stock list
    case POLICY_STATUS_SUCCESS:
      return { ...state, statusPolicy: { ...state.statusPolicy, error: false, success: true } };

    case POLICY_STATUS_ERROR:
      return { ...state, statusPolicy: { ...state.statusPolicy, error: true, success: false } };

    case RESET_POLICY_STATUS:
      return { ...state, statusPolicy: { ...state.statusPolicy, error: false, success: false } };


    default:
      return state;
  }
}
