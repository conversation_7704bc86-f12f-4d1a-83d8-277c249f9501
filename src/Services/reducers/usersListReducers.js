import { ADD_USER_LIST_ERROR, ADD_USER_LIST_LOADING, ADD_USER_LIST_SUCCESS, DELETE_USER_LIST_ERROR, DELETE_USER_LIST_SUCCESS, GET_USER_LIST_ERROR, GET_USER_LIST_LOADING, GET_USER_LIST_SUCCESS, RESET_ADD_USER_LIST, RESET_DELETE_USER_LIST, RESET_GET_USER_LIST, RESET_UPDATE_USER_LIST, RESET_USER_LIST_STATUS, UPDATE_USER_LIST_ERROR, UPDATE_USER_LIST_LOADING, UPDATE_USER_LIST_SUCCESS, USER_LIST_STATUS_ERROR, USER_LIST_STATUS_SUCCESS } from "../Constant";

let initialState = {
  userList: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  addUserList: {
    loading: false,
    data: null,
    error: false,
    success: false,
    msg: null
  },
  updateUserList: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  deleteUserList: {
    error: false,
    success: false,
  },
  statusUserList: {
    error: false,
    success: false,
  }
};

export default function usersListReducers(state = initialState, action) {
  switch (action.type) {

    //  get user list
    case GET_USER_LIST_LOADING:
      return { ...state, userList: { ...state.userList, loading: true, error: false, success: false } };

    case GET_USER_LIST_SUCCESS:
      return { ...state, userList: { ...state.userList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_USER_LIST_ERROR:
      return { ...state, userList: { ...state.userList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_USER_LIST:
      return { ...state, userList: { ...state.userList, loading: false, error: false, success: false } };

    //  add user list
    case ADD_USER_LIST_LOADING:
      return { ...state, addUserList: { ...state.addUserList, loading: true, error: false, success: false } };

    case ADD_USER_LIST_SUCCESS:
      return { ...state, addUserList: { ...state.addUserList, loading: false, error: false, success: true } };

    case ADD_USER_LIST_ERROR:
      return { ...state, addUserList: { ...state.addUserList, loading: false, error: true, success: false, msg: action.msg } };

    case RESET_ADD_USER_LIST:
      return { ...state, addUserList: { ...state.addUserList, loading: false, error: false, success: false, msg: null } };


    //  update user list
    case UPDATE_USER_LIST_LOADING:
      return { ...state, updateUserList: { ...state.updateUserList, loading: true, error: false, success: false } };

    case UPDATE_USER_LIST_SUCCESS:
      return { ...state, updateUserList: { ...state.updateUserList, loading: false, error: false, success: true } };

    case UPDATE_USER_LIST_ERROR:
      return { ...state, updateUserList: { ...state.updateUserList, loading: false, error: true, success: false, msg: action.msg } };

    case RESET_UPDATE_USER_LIST:
      return { ...state, updateUserList: { ...state.updateUserList, loading: false, error: false, success: false } };


    //  delete user list
    case DELETE_USER_LIST_SUCCESS:
      return { ...state, deleteUserList: { ...state.deleteUserList, error: false, success: true } };

    case DELETE_USER_LIST_ERROR:
      return { ...state, deleteUserList: { ...state.deleteUserList, error: true, success: false } };

    case RESET_DELETE_USER_LIST:
      return { ...state, deleteUserList: { ...state.deleteUserList, error: false, success: false } };

    //  status user list
    case USER_LIST_STATUS_SUCCESS:
      return { ...state, statusUserList: { ...state.statusUserList, error: false, success: true } };

    case USER_LIST_STATUS_ERROR:
      return { ...state, statusUserList: { ...state.statusUserList, error: true, success: false } };

    case RESET_USER_LIST_STATUS:
      return { ...state, statusUserList: { ...state.statusUserList, error: false, success: false } };

    default:
      return state;
  }
}
