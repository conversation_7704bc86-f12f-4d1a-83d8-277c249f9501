import { ADD_STAR_MELEE_ERROR, ADD_STAR_MELEE_LOADING, ADD_STAR_MELEE_SUCCESS, DELETE_STAR_MELEE_ERROR, DELETE_STAR_MELEE_SUCCESS, GET_STAR_MELEE_ERROR, GET_STAR_MELEE_LOADING, GET_STAR_MELEE_SUCCESS, RESET_ADD_STAR_MELEE, RESET_DELETE_STAR_MELEE, RESET_GET_STAR_MELEE, RESET_STAR_MELEE_INQUIRY, RESET_STAR_MELEE_STATUS, RESET_UPDATE_STAR_MELEE, RESET_UPLOAD_CSV_STAR_MELEE, STAR_MELEE_INQUIRY_ERROR, STAR_MELEE_INQUIRY_LOADING, STAR_MELEE_INQUIRY_SUCCESS, STAR_MELEE_STATUS_ERROR, STAR_MELEE_STATUS_SUCCESS, UPDATE_STAR_MELEE_ERROR, UPDATE_STAR_MELEE_LOADING, UPDATE_STAR_MELEE_SUCCESS, UPLOAD_CSV_STAR_MELEE_ERROR, UPLOAD_CSV_STAR_MELEE_LOADING, UPLOAD_CSV_STAR_MELEE_SUCCESS } from "../Constant";

let initialState = {

  StarMelee: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  addStarMelee: {
    loading: false,
    data: null,
    error: false,
    success: false,
    msg: null
  },
  updateStarMelee: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  deleteStarMelee: {
    error: false,
    success: false,
  },
  statusStarMelee: {
    error: false,
    success: false,
  },
  uploadStarMeleeCsvFile: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  starMeleeInquiry: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
};

export default function starMeleeReducer(state = initialState, action) {
  switch (action.type) {

    //  get Stock list
    case GET_STAR_MELEE_LOADING:
      return { ...state, StarMelee: { ...state.StarMelee, loading: true, error: false, success: false } };

    case GET_STAR_MELEE_SUCCESS:
      return { ...state, StarMelee: { ...state.StarMelee, loading: false, error: false, success: true, data: action.response.data } };

    case GET_STAR_MELEE_ERROR:
      return { ...state, StarMelee: { ...state.StarMelee, loading: false, error: true, success: false, data: null } };

    case RESET_GET_STAR_MELEE:
      return { ...state, StarMelee: { ...state.StarMelee, loading: false, error: false, success: false } };

    //  add Stock list
    case ADD_STAR_MELEE_LOADING:
      return { ...state, addStarMelee: { ...state.addStarMelee, loading: true, error: false, success: false } };

    case ADD_STAR_MELEE_SUCCESS:
      return { ...state, addStarMelee: { ...state.addStarMelee, loading: false, error: false, success: true } };

    case ADD_STAR_MELEE_ERROR:
      return { ...state, addStarMelee: { ...state.addStarMelee, loading: false, error: true, success: false, msg: action.errorMsg } };

    case RESET_ADD_STAR_MELEE:
      return { ...state, addStarMelee: { ...state.addStarMelee, loading: false, error: false, success: false, msg: null } };


    //  update Stock list
    case UPDATE_STAR_MELEE_LOADING:
      return { ...state, updateStarMelee: { ...state.updateStarMelee, loading: true, error: false, success: false } };

    case UPDATE_STAR_MELEE_SUCCESS:
      return { ...state, updateStarMelee: { ...state.updateStarMelee, loading: false, error: false, success: true } };

    case UPDATE_STAR_MELEE_ERROR:
      return { ...state, updateStarMelee: { ...state.updateStarMelee, loading: false, error: true, success: false, msg: action.errorMsg } };

    case RESET_UPDATE_STAR_MELEE:
      return { ...state, updateStarMelee: { ...state.updateStarMelee, loading: false, error: false, success: false } };


    //  delete Stock list
    case DELETE_STAR_MELEE_SUCCESS:
      return { ...state, deleteStarMelee: { ...state.deleteStarMelee, error: false, success: true } };

    case DELETE_STAR_MELEE_ERROR:
      return { ...state, deleteStarMelee: { ...state.deleteStarMelee, error: true, success: false } };

    case RESET_DELETE_STAR_MELEE:
      return { ...state, deleteStarMelee: { ...state.deleteStarMelee, error: false, success: false } };

    //  status Stock list
    case STAR_MELEE_STATUS_SUCCESS:
      return { ...state, statusStarMelee: { ...state.statusStarMelee, error: false, success: true } };

    case STAR_MELEE_STATUS_ERROR:
      return { ...state, statusStarMelee: { ...state.statusStarMelee, error: true, success: false } };

    case RESET_STAR_MELEE_STATUS:
      return { ...state, statusStarMelee: { ...state.statusStarMelee, error: false, success: false } };

    //  upload csv
    case UPLOAD_CSV_STAR_MELEE_LOADING:
      return { ...state, uploadStarMeleeCsvFile: { ...state.uploadStarMeleeCsvFile, loading: true, error: false, success: false } };

    case UPLOAD_CSV_STAR_MELEE_SUCCESS:
      return { ...state, uploadStarMeleeCsvFile: { ...state.uploadStarMeleeCsvFile, loading: false, error: false, success: true, data: action.response.data } };

    case UPLOAD_CSV_STAR_MELEE_ERROR:
      return { ...state, uploadStarMeleeCsvFile: { ...state.uploadStarMeleeCsvFile, loading: false, error: true, success: false, data: null } };

    case RESET_UPLOAD_CSV_STAR_MELEE:
      return { ...state, uploadStarMeleeCsvFile: { ...state.uploadStarMeleeCsvFile, loading: false, error: false, success: false } };

    //  star melee Inquiry
    case STAR_MELEE_INQUIRY_LOADING:
      return { ...state, starMeleeInquiry: { ...state.starMeleeInquiry, loading: true, error: false, success: false, } };

    case STAR_MELEE_INQUIRY_SUCCESS:
      return { ...state, starMeleeInquiry: { ...state.starMeleeInquiry, loading: false, error: false, success: true, data: action.response.data } };

    case STAR_MELEE_INQUIRY_ERROR:
      return { ...state, starMeleeInquiry: { ...state.starMeleeInquiry, loading: false, error: true, success: false, data: null } };

    case RESET_STAR_MELEE_INQUIRY:
      return { ...state, starMeleeInquiry: { ...state.starMeleeInquiry, loading: false, error: true, success: false, data: null } };



    default:
      return state;
  }
}
