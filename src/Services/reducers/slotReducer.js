import { ADD_SLOT_ERROR, ADD_SLOT_LOADING, ADD_SLOT_SUCCESS, DELETE_SLOT_ERROR, DELETE_SLOT_SUCCESS, GET_REQUEST_LIST_ERROR, GET_REQUEST_LIST_LOADING, GET_REQUEST_LIST_SUCCESS, GET_SLOT_ERROR, GET_SLOT_LOADING, GET_SLOT_SUCCESS, REQUEST_LIST_DELETE_ERROR, REQUEST_LIST_DELETE_LOADING, REQUEST_LIST_DELETE_SUCCESS, REQUEST_LIST_STATUS_ERROR, REQUEST_LIST_STATUS_LOADING, REQUEST_LIST_STATUS_SUCCESS, RESET_ADD_SLOT, RESET_DELETE_SLOT, RESET_GET_REQUEST_LIST, RESET_GET_SLOT, RESET_REQUEST_LIST_DELETE, RESET_REQUEST_LIST_STATUS, RESET_SLOT_STATUS, RESET_UPDATE_SLOT, SLOT_STATUS_ERROR, SLOT_STATUS_SUCCESS, UPDATE_SLOT_ERROR, UPDATE_SLOT_LOADING, UPDATE_SLOT_SUCCESS } from "../Constant";

let initialState = {

  addSlot: {
    loading: false,
    error: false,
    success: false,
  },
  getSlot: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  updateSlot: {
    loading: false,
    error: false,
    success: false,
  },
  deleteSlot: {
    loading: false,
    error: false,
    success: false,
    msg: null
  },
  statusSlot: {
    loading: false,
    error: false,
    success: false,
  },
  viewRequestList: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  viewRequstStatus: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  viewRequstDelete: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },


}

export default function slotReducer(state = initialState, action) {
  switch (action.type) {

    //  get slot
    case GET_SLOT_LOADING:
      return { ...state, getSlot: { ...state.getSlot, loading: true, error: false, success: false } };

    case GET_SLOT_SUCCESS:
      return { ...state, getSlot: { ...state.getSlot, loading: false, error: false, success: true, data: action.response.data } };

    case GET_SLOT_ERROR:
      return { ...state, getSlot: { ...state.getSlot, loading: false, error: true, success: false, data: null } };

    case RESET_GET_SLOT:
      return { ...state, getSlot: { ...state.getSlot, loading: false, error: false, success: false } };

    //  add slot
    case ADD_SLOT_LOADING:
      return { ...state, addSlot: { ...state.addSlot, loading: true, error: false, success: false } };

    case ADD_SLOT_SUCCESS:
      return { ...state, addSlot: { ...state.addSlot, loading: false, error: false, success: true } };

    case ADD_SLOT_ERROR:
      return { ...state, addSlot: { ...state.addSlot, loading: false, error: true, success: false, msg: action.msg } };

    case RESET_ADD_SLOT:
      return { ...state, addSlot: { ...state.addSlot, loading: false, error: false, success: false, msg: null } };

    //  update slot
    case UPDATE_SLOT_LOADING:
      return { ...state, updateSlot: { ...state.updateSlot, loading: true, error: false, success: false } };

    case UPDATE_SLOT_SUCCESS:
      return { ...state, updateSlot: { ...state.updateSlot, loading: false, error: false, success: true } };

    case UPDATE_SLOT_ERROR:
      return { ...state, updateSlot: { ...state.updateSlot, loading: false, error: true, success: false, msg: action.msg } };

    case RESET_UPDATE_SLOT:
      return { ...state, updateSlot: { ...state.updateSlot, loading: false, error: false, success: false, msg: null } };


    //  delete slot
    case DELETE_SLOT_SUCCESS:
      return { ...state, deleteSlot: { ...state.deleteSlot, error: false, success: true } };

    case DELETE_SLOT_ERROR:
      return { ...state, deleteSlot: { ...state.deleteSlot, error: true, success: false, msg: action.errroMsg } };

    case RESET_DELETE_SLOT:
      return { ...state, deleteSlot: { ...state.deleteSlot, error: false, success: false } };

    //  status slot
    case SLOT_STATUS_SUCCESS:
      return { ...state, statusSlot: { ...state.statusSlot, error: false, success: true } };

    case SLOT_STATUS_ERROR:
      return { ...state, statusSlot: { ...state.statusSlot, error: true, success: false } };

    case RESET_SLOT_STATUS:
      return { ...state, statusSlot: { ...state.statusSlot, error: false, success: false } };


    //  get requestList
    case GET_REQUEST_LIST_LOADING:
      return { ...state, viewRequestList: { ...state.viewRequestList, loading: true, error: false, success: false } };

    case GET_REQUEST_LIST_SUCCESS:
      return { ...state, viewRequestList: { ...state.viewRequestList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_REQUEST_LIST_ERROR:
      return { ...state, viewRequestList: { ...state.viewRequestList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_REQUEST_LIST:
      return { ...state, viewRequestList: { ...state.viewRequestList, loading: false, error: false, success: false } };


    //  status requestList
    case REQUEST_LIST_STATUS_LOADING:
      return { ...state, viewRequstStatus: { ...state.viewRequstStatus, loading: true, error: false, success: false } };

    case REQUEST_LIST_STATUS_SUCCESS:
      return { ...state, viewRequstStatus: { ...state.viewRequstStatus, loading: false, error: false, success: true, data: action.response.data } };

    case REQUEST_LIST_STATUS_ERROR:
      return { ...state, viewRequstStatus: { ...state.viewRequstStatus, loading: false, error: true, success: false, data: null } };

    case RESET_REQUEST_LIST_STATUS:
      return { ...state, viewRequstStatus: { ...state.viewRequstStatus, loading: false, error: false, success: false } };


    //  delete requestList
    case REQUEST_LIST_DELETE_LOADING:
      return { ...state, viewRequstDelete: { ...state.viewRequstDelete, loading: true, error: false, success: false } };

    case REQUEST_LIST_DELETE_SUCCESS:
      return { ...state, viewRequstDelete: { ...state.viewRequstDelete, loading: false, error: false, success: true, data: action.response.data } };

    case REQUEST_LIST_DELETE_ERROR:
      return { ...state, viewRequstDelete: { ...state.viewRequstDelete, loading: false, error: true, success: false, data: null } };

    case RESET_REQUEST_LIST_DELETE:
      return { ...state, viewRequstDelete: { ...state.viewRequstDelete, loading: false, error: false, success: false } };



    default:
      return state;
  }
}
