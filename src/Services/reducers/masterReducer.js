import { AD<PERSON>_<PERSON><PERSON><PERSON><PERSON>_ERROR, ADD_<PERSON><PERSON><PERSON><PERSON>_LOADING, ADD_C<PERSON><PERSON>TY_SUCCESS, ADD_COLOR_ERROR, ADD_COLOR_LOADING, ADD_COLOR_SUCCESS, ADD_FANCY_COLOR_ERROR, ADD_FANCY_COLOR_LOADING, ADD_FANCY_COLOR_SUCCESS, ADD_FINISH_ERROR, ADD_FINISH_LOADING, ADD_<PERSON>INISH_SUCCESS, ADD_FLUORESCENCE_ERROR, ADD_FLUORESCENCE_LOADING, ADD_FLUORESCENCE_SUCCESS, ADD_SHAPE_ERROR, ADD_SHAPE_LOADING, ADD_SHAPE_SUCCESS, ADD_SIZE_ERROR, ADD_SIZE_LOADING, ADD_SIZE_SUCCESS, CLARITY_STATUS_ERROR, CLARITY_STATUS_SUCCESS, COLOR_STATUS_ERROR, COLOR_STATUS_SUCCESS, DELETE_CLARITY_ERROR, <PERSON>LETE_C<PERSON>RITY_SUCCESS, DELETE_COLOR_ERROR, <PERSON>LETE_COLOR_SUCCESS, DELETE_FANCY_COLOR_ERROR, DELETE_FANCY_COLOR_SUCCESS, DELETE_FINISH_ERROR, DELETE_FINISH_SUCCESS, DELETE_FLUORESCENCE_ERROR, DELETE_FLUORESCENCE_SUCCESS, DELETE_SHAPE_ERROR, DELETE_SHAPE_SUCCESS, DELETE_SIZE_ERROR, DELETE_SIZE_SUCCESS, FANCY_COLOR_STATUS_ERROR, FANCY_COLOR_STATUS_SUCCESS, FINISH_STATUS_ERROR, FINISH_STATUS_SUCCESS, FLUORESCENCE_STATUS_ERROR, FLUORESCENCE_STATUS_SUCCESS, GET_CLARITY_ERROR, GET_CLARITY_LOADING, GET_CLARITY_SUCCESS, GET_COLOR_ERROR, GET_COLOR_LOADING, GET_COLOR_SUCCESS, GET_FANCY_COLOR_ERROR, GET_FANCY_COLOR_LOADING, GET_FANCY_COLOR_SUCCESS, GET_FINISH_ERROR, GET_FINISH_LOADING, GET_FINISH_SUCCESS, GET_FLUORESCENCE_ERROR, GET_FLUORESCENCE_LOADING, GET_FLUORESCENCE_SUCCESS, GET_SHAPE_ERROR, GET_SHAPE_LOADING, GET_SHAPE_SUCCESS, GET_SIZE_ERROR, GET_SIZE_LOADING, GET_SIZE_SUCCESS, RESET_ADD_CLARITY, RESET_ADD_COLOR, RESET_ADD_FANCY_COLOR, RESET_ADD_FINISH, RESET_ADD_FLUORESCENCE, RESET_ADD_SHAPE, RESET_ADD_SIZE, RESET_CLARITY_STATUS, RESET_COLOR_STATUS, RESET_DELETE_CLARITY, RESET_DELETE_COLOR, RESET_DELETE_FANCY_COLOR, RESET_DELETE_FINISH, RESET_DELETE_FLUORESCENCE, RESET_DELETE_SHAPE, RESET_DELETE_SIZE, RESET_FANCY_COLOR_STATUS, RESET_FINISH_STATUS, RESET_FLUORESCENCE_STATUS, RESET_GET_CLARITY, RESET_GET_COLOR, RESET_GET_FANCY_COLOR, RESET_GET_FINISH, RESET_GET_FLUORESCENCE, RESET_GET_SHAPE, RESET_GET_SIZE, RESET_SHAPE_STATUS, RESET_SIZE_STATUS, RESET_UPDATE_CLARITY, RESET_UPDATE_COLOR, RESET_UPDATE_FANCY_COLOR, RESET_UPDATE_FINISH, RESET_UPDATE_FLUORESCENCE, RESET_UPDATE_SHAPE, RESET_UPDATE_SIZE, SHAPE_STATUS_ERROR, SHAPE_STATUS_SUCCESS, SIZE_STATUS_ERROR, SIZE_STATUS_SUCCESS, UPDATE_CLARITY_ERROR, UPDATE_CLARITY_LOADING, UPDATE_CLARITY_SUCCESS, UPDATE_COLOR_ERROR, UPDATE_COLOR_LOADING, UPDATE_COLOR_SUCCESS, UPDATE_FANCY_COLOR_ERROR, UPDATE_FANCY_COLOR_LOADING, UPDATE_FANCY_COLOR_SUCCESS, UPDATE_FINISH_ERROR, UPDATE_FINISH_LOADING, UPDATE_FINISH_SUCCESS, UPDATE_FLUORESCENCE_ERROR, UPDATE_FLUORESCENCE_LOADING, UPDATE_FLUORESCENCE_SUCCESS, UPDATE_SHAPE_ERROR, UPDATE_SHAPE_LOADING, UPDATE_SHAPE_SUCCESS, UPDATE_SIZE_ERROR, UPDATE_SIZE_LOADING, UPDATE_SIZE_SUCCESS } from "../Constant";

let initialState = {
  Clarity: {
    addClarity: {
      loading: false,
      error: false,
      success: false,
    },
    getClarity: {
      loading: false,
      data: null,
      error: false,
      success: false,
    },
    updateClarity: {
      loading: false,
      error: false,
      success: false,
    },
    deleteClarity: {
      loading: false,
      error: false,
      success: false,
    },
    clarityStatus: {
      loading: false,
      error: false,
      success: false,
    },

  },
  Color: {
    addColor: {
      loading: false,
      error: false,
      success: false,
    },
    getColor: {
      loading: false,
      data: null,
      error: false,
      success: false,
    },
    updateColor: {
      loading: false,
      error: false,
      success: false,
    },
    deleteColor: {
      loading: false,
      error: false,
      success: false,
    },
    ColorStatus: {
      loading: false,
      error: false,
      success: false,
    },

  },
  Size: {
    getSize: {
      loading: false,
      data: null,
      error: false,
      success: false,
    },
    addSize: {
      loading: false,
      error: false,
      success: false,
    },
    updateSize: {
      loading: false,
      error: false,
      success: false,
    },
    deleteSize: {
      loading: false,
      error: false,
      success: false,
    },
    SizeStatus: {
      loading: false,
      error: false,
      success: false,
    },

  },
  Shape: {
    getShape: {
      loading: false,
      data: null,
      error: false,
      success: false,
    },
    addShape: {
      loading: false,
      error: false,
      success: false,
    },
    updateShape: {
      loading: false,
      error: false,
      success: false,
    },
    deleteShape: {
      loading: false,
      error: false,
      success: false,
    },
    ShapeStatus: {
      loading: false,
      error: false,
      success: false,
    },

  },
  FancyColor: {
    getFancyColor: {
      loading: false,
      data: null,
      error: false,
      success: false,
    },
    addFancyColor: {
      loading: false,
      error: false,
      success: false,
    },
    updateFancyColor: {
      loading: false,
      error: false,
      success: false,
    },
    deleteFancyColor: {
      loading: false,
      error: false,
      success: false,
    },
    FancyColorStatus: {
      loading: false,
      error: false,
      success: false,
    },

  },
  Finish: {
    getFinish: {
      loading: false,
      data: null,
      error: false,
      success: false,
    },
    addFinish: {
      loading: false,
      error: false,
      success: false,
    },
    updateFinish: {
      loading: false,
      error: false,
      success: false,
    },
    deleteFinish: {
      loading: false,
      error: false,
      success: false,
    },
    FinishStatus: {
      loading: false,
      error: false,
      success: false,
    },

  },
  Fluorescence: {
    getFluorescence: {
      loading: false,
      data: null,
      error: false,
      success: false,
    },
    addFluorescence: {
      loading: false,
      error: false,
      success: false,
    },
    updateFluorescence: {
      loading: false,
      error: false,
      success: false,
    },
    deleteFluorescence: {
      loading: false,
      error: false,
      success: false,
    },
    FluorescenceStatus: {
      loading: false,
      error: false,
      success: false,
    },

  }
};

export default function masterReducer(state = initialState, action) {
  switch (action.type) {


    //  ***************************** get clarity

    case GET_CLARITY_LOADING:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          getClarity: {
            ...state.Clarity.getClarity,
            loading: true,
            data: null,
            error: false,
            success: false,
          }
        }
      };

    case GET_CLARITY_SUCCESS:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          getClarity: {
            ...state.Clarity.getClarity,
            loading: false,
            error: false,
            errors: [],
            success: true,
            data: action.response.data
          }
        }
      };

    case GET_CLARITY_ERROR:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          getClarity: {
            ...state.Clarity.getClarity,
            loading: false,
            error: true,
            errors: action.errors,
            success: false,
            data: null
          }
        }
      };

    case RESET_GET_CLARITY:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          getClarity: {
            ...state.Clarity.getClarity,
            loading: false,
            error: false,
            errors: null,
            success: false
          }
        }
      };
    //  ***********************************************

    // *****************    add clarity
    case ADD_CLARITY_LOADING:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          addClarity: {
            ...state.Clarity.addClarity,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case ADD_CLARITY_SUCCESS:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          addClarity: {
            ...state.Clarity.addClarity,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case ADD_CLARITY_ERROR:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          addClarity: {
            ...state.Clarity.addClarity,
            loading: false,
            error: true,
            success: false
          }
        }
      };

    case RESET_ADD_CLARITY:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          addClarity: {
            ...state.Clarity.addClarity,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    //  *********************************************

    //  ********************************** update clarity
    case UPDATE_CLARITY_LOADING:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          updateClarity: {
            ...state.Clarity.updateClarity,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case UPDATE_CLARITY_SUCCESS:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          updateClarity: {
            ...state.Clarity.updateClarity,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case GET_CLARITY_ERROR:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          updateClarity: {
            ...state.Clarity.updateClarity,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_UPDATE_CLARITY:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          updateClarity: {
            ...state.Clarity.updateClarity,
            loading: false,
            error: false,
            success: false
          }
        }
      };

    // *************************************************************

    // **************************************** delete Clarity
    case DELETE_CLARITY_SUCCESS:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          deleteClarity: {
            ...state.Clarity.deleteClarity,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case DELETE_CLARITY_ERROR:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          deleteClarity: {
            ...state.Clarity.deleteClarity,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_DELETE_CLARITY:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          deleteClarity: {
            ...state.Clarity.deleteClarity,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    // ******************************* clarity status
    case CLARITY_STATUS_SUCCESS:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          clarityStatus: {
            ...state.Clarity.clarityStatus,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case CLARITY_STATUS_ERROR:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          clarityStatus: {
            ...state.Clarity.clarityStatus,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_CLARITY_STATUS:
      return {
        ...state, Clarity: {
          ...state.Clarity,
          clarityStatus: {
            ...state.Clarity.clarityStatus,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    //  ***************************** get color
    case GET_COLOR_LOADING:
      return {
        ...state, Color: {
          ...state.Color,
          getColor: {
            ...state.Color.getColor,
            loading: true,
            data: null,
            error: false,
            success: false,
          }
        }
      };

    case GET_COLOR_SUCCESS:
      return {
        ...state, Color: {
          ...state.Color,
          getColor: {
            ...state.Color.getColor,
            loading: false,
            error: false,
            errors: [],
            success: true,
            data: action.response.data
          }
        }
      };

    case GET_COLOR_ERROR:
      return {
        ...state, Color: {
          ...state.Color,
          getColor: {
            ...state.Color.getColor,
            loading: false,
            error: true,
            errors: action.errors,
            success: false,
            data: null
          }
        }
      };

    case RESET_GET_COLOR:
      return {
        ...state, Color: {
          ...state.Color,
          getColor: {
            ...state.Color.getColor,
            loading: false,
            error: false,
            errors: null,
            success: false
          }
        }
      };
    //  ***********************************************

    // *****************    add color
    case ADD_COLOR_LOADING:
      return {
        ...state, Color: {
          ...state.Color,
          addColor: {
            ...state.Color.addColor,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case ADD_COLOR_SUCCESS:
      return {
        ...state, Color: {
          ...state.Color,
          addColor: {
            ...state.Color.addColor,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case ADD_COLOR_ERROR:
      return {
        ...state, Color: {
          ...state.Color,
          addColor: {
            ...state.Color.addColor,
            loading: false,
            error: true,
            success: false
          }
        }
      };

    case RESET_ADD_COLOR:
      return {
        ...state, Color: {
          ...state.Color,
          addColor: {
            ...state.Color.addColor,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    //  *********************************************

    //  ********************************** update color
    case UPDATE_COLOR_LOADING:
      return {
        ...state, Color: {
          ...state.Color,
          updateColor: {
            ...state.Color.updateColor,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case UPDATE_COLOR_SUCCESS:
      return {
        ...state, Color: {
          ...state.Color,
          updateColor: {
            ...state.Color.updateColor,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case UPDATE_COLOR_ERROR:
      return {
        ...state, Color: {
          ...state.Color,
          updateColor: {
            ...state.Color.updateColor,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_UPDATE_COLOR:
      return {
        ...state, Color: {
          ...state.Color,
          updateColor: {
            ...state.Color.updateColor,
            loading: false,
            error: false,
            success: false
          }
        }
      };

    // *************************************************************

    // **************************************** delete color
    case DELETE_COLOR_SUCCESS:
      return {
        ...state, Color: {
          ...state.Color,
          deleteColor: {
            ...state.Color.deleteColor,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case DELETE_COLOR_ERROR:
      return {
        ...state, Color: {
          ...state.Color,
          deleteColor: {
            ...state.Color.deleteColor,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_DELETE_COLOR:
      return {
        ...state, Color: {
          ...state.Color,
          deleteColor: {
            ...state.Color.deleteColor,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    // ******************************* status color 
    case COLOR_STATUS_SUCCESS:
      return {
        ...state, Color: {
          ...state.Color,
          ColorStatus: {
            ...state.Color.ColorStatus,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case COLOR_STATUS_ERROR:
      return {
        ...state, Color: {
          ...state.Color,
          ColorStatus: {
            ...state.Color.ColorStatus,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_COLOR_STATUS:
      return {
        ...state, Color: {
          ...state.Color,
          ColorStatus: {
            ...state.Color.ColorStatus,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    //  ***************************** get size
    case GET_SIZE_LOADING:
      return {
        ...state, Size: {
          ...state.Size,
          getSize: {
            ...state.Size.getSize,
            loading: true,
            data: null,
            error: false,
            success: false,
          }
        }
      };

    case GET_SIZE_SUCCESS:
      return {
        ...state, Size: {
          ...state.Size,
          getSize: {
            ...state.Size.getSize,
            loading: false,
            error: false,
            errors: [],
            success: true,
            data: action.response.data
          }
        }
      };

    case GET_SIZE_ERROR:
      return {
        ...state, Size: {
          ...state.Size,
          getSize: {
            ...state.Size.getSize,
            loading: false,
            error: true,
            errors: action.errors,
            success: false,
            data: null
          }
        }
      };

    case RESET_GET_SIZE:
      return {
        ...state, Size: {
          ...state.Size,
          getSize: {
            ...state.Size.getSize,
            loading: false,
            error: false,
            errors: null,
            success: false
          }
        }
      };
    //  ***********************************************

    // *****************    add size
    case ADD_SIZE_LOADING:
      return {
        ...state, Size: {
          ...state.Size,
          addSize: {
            ...state.Size.addSize,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case ADD_SIZE_SUCCESS:
      return {
        ...state, Size: {
          ...state.Size,
          addSize: {
            ...state.Size.addSize,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case ADD_SIZE_ERROR:
      return {
        ...state, Size: {
          ...state.Size,
          addSize: {
            ...state.Size.addSize,
            loading: false,
            error: true,
            success: false
          }
        }
      };

    case RESET_ADD_SIZE:
      return {
        ...state, Size: {
          ...state.Size,
          addSize: {
            ...state.Size.addSize,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    //  *********************************************

    //  ********************************** update size
    case UPDATE_SIZE_LOADING:
      return {
        ...state, Size: {
          ...state.Size,
          updateSize: {
            ...state.Size.updateSize,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case UPDATE_SIZE_SUCCESS:
      return {
        ...state, Size: {
          ...state.Size,
          updateSize: {
            ...state.Size.updateSize,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case UPDATE_SIZE_ERROR:
      return {
        ...state, Size: {
          ...state.Size,
          updateSize: {
            ...state.Size.updateSize,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_UPDATE_SIZE:
      return {
        ...state, Size: {
          ...state.Size,
          updateSize: {
            ...state.Size.updateSize,
            loading: false,
            error: false,
            success: false
          }
        }
      };

    // *************************************************************

    // **************************************** delete size
    case DELETE_SIZE_SUCCESS:
      return {
        ...state, Size: {
          ...state.Size,
          deleteSize: {
            ...state.Size.deleteSize,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case DELETE_SIZE_ERROR:
      return {
        ...state, Size: {
          ...state.Size,
          deleteSize: {
            ...state.Size.deleteSize,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_DELETE_SIZE:
      return {
        ...state, Size: {
          ...state.Size,
          deleteSize: {
            ...state.Size.deleteSize,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    // ******************************* status size 
    case SIZE_STATUS_SUCCESS:
      return {
        ...state, Size: {
          ...state.Size,
          SizeStatus: {
            ...state.Size.SizeStatus,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case SIZE_STATUS_ERROR:
      return {
        ...state, Size: {
          ...state.Size,
          SizeStatus: {
            ...state.Size.SizeStatus,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_SIZE_STATUS:
      return {
        ...state, Size: {
          ...state.Size,
          SizeStatus: {
            ...state.Size.SizeStatus,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    //  ***************************** get shape
    case GET_SHAPE_LOADING:
      return {
        ...state, Shape: {
          ...state.Shape,
          getShape: {
            ...state.Shape.getShape,
            loading: true,
            data: null,
            error: false,
            success: false,
          }
        }
      };

    case GET_SHAPE_SUCCESS:
      return {
        ...state, Shape: {
          ...state.Shape,
          getShape: {
            ...state.Shape.getShape,
            loading: false,
            error: false,
            errors: [],
            success: true,
            data: action.response.data
          }
        }
      };

    case GET_SHAPE_ERROR:
      return {
        ...state, Shape: {
          ...state.Shape,
          getShape: {
            ...state.Shape.getShape,
            loading: false,
            error: true,
            errors: action.errors,
            success: false,
            data: null
          }
        }
      };

    case RESET_GET_SHAPE:
      return {
        ...state, Shape: {
          ...state.Shape,
          getShape: {
            ...state.Shape.getShape,
            loading: false,
            error: false,
            errors: null,
            success: false
          }
        }
      };
    //  ***********************************************

    // *****************    add shape
    case ADD_SHAPE_LOADING:
      return {
        ...state, Shape: {
          ...state.Shape,
          addShape: {
            ...state.Shape.addShape,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case ADD_SHAPE_SUCCESS:
      return {
        ...state, Shape: {
          ...state.Shape,
          addShape: {
            ...state.Shape.addShape,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case ADD_SHAPE_ERROR:
      return {
        ...state, Shape: {
          ...state.Shape,
          addShape: {
            ...state.Shape.addShape,
            loading: false,
            error: true,
            success: false
          }
        }
      };

    case RESET_ADD_SHAPE:
      return {
        ...state, Shape: {
          ...state.Shape,
          addShape: {
            ...state.Shape.addShape,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    //  *********************************************

    //  ********************************** update shape
    case UPDATE_SHAPE_LOADING:
      return {
        ...state, Shape: {
          ...state.Shape,
          updateShape: {
            ...state.Shape.updateShape,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case UPDATE_SHAPE_SUCCESS:
      return {
        ...state, Shape: {
          ...state.Shape,
          updateShape: {
            ...state.Shape.updateShape,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case UPDATE_SHAPE_ERROR:
      return {
        ...state, Shape: {
          ...state.Shape,
          updateShape: {
            ...state.Shape.updateShape,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_UPDATE_SHAPE:
      return {
        ...state, Shape: {
          ...state.Shape,
          updateShape: {
            ...state.Shape.updateShape,
            loading: false,
            error: false,
            success: false
          }
        }
      };

    // *************************************************************

    // **************************************** delete shape
    case DELETE_SHAPE_SUCCESS:
      return {
        ...state, Shape: {
          ...state.Shape,
          deleteShape: {
            ...state.Shape.deleteShape,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case DELETE_SHAPE_ERROR:
      return {
        ...state, Shape: {
          ...state.Shape,
          deleteShape: {
            ...state.Shape.deleteShape,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_DELETE_SHAPE:
      return {
        ...state, Shape: {
          ...state.Shape,
          deleteShape: {
            ...state.Shape.deleteShape,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    // ******************************* status shape 
    case SHAPE_STATUS_SUCCESS:
      return {
        ...state, Shape: {
          ...state.Shape,
          ShapeStatus: {
            ...state.Shape.ShapeStatus,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case SHAPE_STATUS_ERROR:
      return {
        ...state, Shape: {
          ...state.Shape,
          ShapeStatus: {
            ...state.Shape.ShapeStatus,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_SHAPE_STATUS:
      return {
        ...state, Shape: {
          ...state.Shape,
          ShapeStatus: {
            ...state.Shape.ShapeStatus,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    //  ***************************** get fancy color
    case GET_FANCY_COLOR_LOADING:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          getFancyColor: {
            ...state.FancyColor.getFancyColor,
            loading: true,
            data: null,
            error: false,
            success: false,
          }
        }
      };

    case GET_FANCY_COLOR_SUCCESS:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          getFancyColor: {
            ...state.FancyColor.getFancyColor,
            loading: false,
            error: false,
            errors: [],
            success: true,
            data: action.response.data
          }
        }
      };

    case GET_FANCY_COLOR_ERROR:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          getFancyColor: {
            ...state.FancyColor.getFancyColor,
            loading: false,
            error: true,
            errors: action.errors,
            success: false,
            data: null
          }
        }
      };

    case RESET_GET_FANCY_COLOR:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          getFancyColor: {
            ...state.FancyColor.getFancyColor,
            loading: false,
            error: false,
            errors: null,
            success: false
          }
        }
      };
    //  ***********************************************

    // *****************    add fancy color
    case ADD_FANCY_COLOR_LOADING:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          addFancyColor: {
            ...state.FancyColor.addFancyColor,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case ADD_FANCY_COLOR_SUCCESS:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          addFancyColor: {
            ...state.FancyColor.addFancyColor,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case ADD_FANCY_COLOR_ERROR:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          addFancyColor: {
            ...state.FancyColor.addFancyColor,
            loading: false,
            error: true,
            success: false
          }
        }
      };

    case RESET_ADD_FANCY_COLOR:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          addFancyColor: {
            ...state.FancyColor.addFancyColor,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    //  *********************************************

    //  ********************************** update fancy color
    case UPDATE_FANCY_COLOR_LOADING:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          updateFancyColor: {
            ...state.FancyColor.updateFancyColor,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case UPDATE_FANCY_COLOR_SUCCESS:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          updateFancyColor: {
            ...state.FancyColor.updateFancyColor,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case UPDATE_FANCY_COLOR_ERROR:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          updateFancyColor: {
            ...state.FancyColor.updateFancyColor,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_UPDATE_FANCY_COLOR:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          updateFancyColor: {
            ...state.FancyColor.updateFancyColor,
            loading: false,
            error: false,
            success: false
          }
        }
      };

    // *************************************************************

    // **************************************** delete fancy color
    case DELETE_FANCY_COLOR_SUCCESS:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          deleteFancyColor: {
            ...state.FancyColor.deleteFancyColor,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case DELETE_FANCY_COLOR_ERROR:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          deleteFancyColor: {
            ...state.FancyColor.deleteFancyColor,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_DELETE_FANCY_COLOR:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          deleteFancyColor: {
            ...state.FancyColor.deleteFancyColor,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    // ******************************* status fancy color 
    case FANCY_COLOR_STATUS_SUCCESS:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          FancyColorStatus: {
            ...state.FancyColor.FancyColorStatus,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case FANCY_COLOR_STATUS_ERROR:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          FancyColorStatus: {
            ...state.FancyColor.FancyColorStatus,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_FANCY_COLOR_STATUS:
      return {
        ...state, FancyColor: {
          ...state.FancyColor,
          FancyColorStatus: {
            ...state.FancyColor.FancyColorStatus,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    //  ***************************** get finish
    case GET_FINISH_LOADING:
      return {
        ...state, Finish: {
          ...state.Finish,
          getFinish: {
            ...state.Finish.getFinish,
            loading: true,
            data: null,
            error: false,
            success: false,
          }
        }
      };

    case GET_FINISH_SUCCESS:
      return {
        ...state, Finish: {
          ...state.Finish,
          getFinish: {
            ...state.Finish.getFinish,
            loading: false,
            error: false,
            errors: [],
            success: true,
            data: action.response.data
          }
        }
      };

    case GET_FINISH_ERROR:
      return {
        ...state, Finish: {
          ...state.Finish,
          getFinish: {
            ...state.Finish.getFinish,
            loading: false,
            error: true,
            errors: action.errors,
            success: false,
            data: null
          }
        }
      };

    case RESET_GET_FINISH:
      return {
        ...state, Finish: {
          ...state.Finish,
          getFinish: {
            ...state.Finish.getFinish,
            loading: false,
            error: false,
            errors: null,
            success: false
          }
        }
      };
    //  ***********************************************

    // *****************    add finish
    case ADD_FINISH_LOADING:
      return {
        ...state, Finish: {
          ...state.Finish,
          addFinish: {
            ...state.Finish.addFinish,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case ADD_FINISH_SUCCESS:
      return {
        ...state, Finish: {
          ...state.Finish,
          addFinish: {
            ...state.Finish.addFinish,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case ADD_FINISH_ERROR:
      return {
        ...state, Finish: {
          ...state.Finish,
          addFinish: {
            ...state.Finish.addFinish,
            loading: false,
            error: true,
            success: false
          }
        }
      };

    case RESET_ADD_FINISH:
      return {
        ...state, Finish: {
          ...state.Finish,
          addFinish: {
            ...state.Finish.addFinish,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    //  *********************************************

    //  ********************************** update finish
    case UPDATE_FINISH_LOADING:
      return {
        ...state, Finish: {
          ...state.Finish,
          updateFinish: {
            ...state.Finish.updateFinish,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case UPDATE_FINISH_SUCCESS:
      return {
        ...state, Finish: {
          ...state.Finish,
          updateFinish: {
            ...state.Finish.updateFinish,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case UPDATE_FINISH_ERROR:
      return {
        ...state, Finish: {
          ...state.Finish,
          updateFinish: {
            ...state.Finish.updateFinish,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_UPDATE_FINISH:
      return {
        ...state, Finish: {
          ...state.Finish,
          updateFinish: {
            ...state.Finish.updateFinish,
            loading: false,
            error: false,
            success: false
          }
        }
      };

    // *************************************************************

    // **************************************** delete finish
    case DELETE_FINISH_SUCCESS:
      return {
        ...state, Finish: {
          ...state.Finish,
          deleteFinish: {
            ...state.Finish.deleteFinish,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case DELETE_FINISH_ERROR:
      return {
        ...state, Finish: {
          ...state.Finish,
          deleteFinish: {
            ...state.Finish.deleteFinish,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_DELETE_FINISH:
      return {
        ...state, Finish: {
          ...state.Finish,
          deleteFinish: {
            ...state.Finish.deleteFinish,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    // ******************************* status finish
    case FINISH_STATUS_SUCCESS:
      return {
        ...state, Finish: {
          ...state.Finish,
          FinishStatus: {
            ...state.Finish.FinishStatus,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case FINISH_STATUS_ERROR:
      return {
        ...state, Finish: {
          ...state.Finish,
          FinishStatus: {
            ...state.Finish.FinishStatus,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_FINISH_STATUS:
      return {
        ...state, Finish: {
          ...state.Finish,
          FinishStatus: {
            ...state.Finish.FinishStatus,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    //  ***************************** get Fluorescence
    case GET_FLUORESCENCE_LOADING:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          getFluorescence: {
            ...state.Fluorescence.getFluorescence,
            loading: true,
            data: null,
            error: false,
            success: false,
          }
        }
      };

    case GET_FLUORESCENCE_SUCCESS:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          getFluorescence: {
            ...state.Fluorescence.getFluorescence,
            loading: false,
            error: false,
            errors: [],
            success: true,
            data: action.response.data
          }
        }
      };

    case GET_FLUORESCENCE_ERROR:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          getFluorescence: {
            ...state.Fluorescence.getFluorescence,
            loading: false,
            error: true,
            errors: action.errors,
            success: false,
            data: null
          }
        }
      };

    case RESET_GET_FLUORESCENCE:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          getFluorescence: {
            ...state.Fluorescence.getFluorescence,
            loading: false,
            error: false,
            errors: null,
            success: false
          }
        }
      };
    //  ***********************************************

    // *****************    add Fluorescence
    case ADD_FLUORESCENCE_LOADING:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          addFluorescence: {
            ...state.Fluorescence.addFluorescence,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case ADD_FLUORESCENCE_SUCCESS:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          addFluorescence: {
            ...state.Fluorescence.addFluorescence,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case ADD_FLUORESCENCE_ERROR:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          addFluorescence: {
            ...state.Fluorescence.addFluorescence,
            loading: false,
            error: true,
            success: false
          }
        }
      };

    case RESET_ADD_FLUORESCENCE:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          addFluorescence: {
            ...state.Fluorescence.addFluorescence,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    //  *********************************************

    //  ********************************** update Fluorescence
    case UPDATE_FLUORESCENCE_LOADING:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          updateFluorescence: {
            ...state.Fluorescence.updateFluorescence,
            loading: true,
            error: false,
            success: false,
          }
        }
      };

    case UPDATE_FLUORESCENCE_SUCCESS:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          updateFluorescence: {
            ...state.Fluorescence.updateFluorescence,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case UPDATE_FLUORESCENCE_ERROR:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          updateFluorescence: {
            ...state.Fluorescence.updateFluorescence,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_UPDATE_FLUORESCENCE:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          updateFluorescence: {
            ...state.Fluorescence.updateFluorescence,
            loading: false,
            error: false,
            success: false
          }
        }
      };

    // *************************************************************

    // **************************************** delete Fluorescence
    case DELETE_FLUORESCENCE_SUCCESS:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          deleteFluorescence: {
            ...state.Fluorescence.deleteFluorescence,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case DELETE_FLUORESCENCE_ERROR:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          deleteFluorescence: {
            ...state.Fluorescence.deleteFluorescence,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_DELETE_FLUORESCENCE:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          deleteFluorescence: {
            ...state.Fluorescence.deleteFluorescence,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************

    // ******************************* status Fluorescence
    case FLUORESCENCE_STATUS_SUCCESS:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          FluorescenceStatus: {
            ...state.Fluorescence.FluorescenceStatus,
            loading: false,
            error: false,
            success: true,
          }
        }
      };

    case FLUORESCENCE_STATUS_ERROR:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          FluorescenceStatus: {
            ...state.Fluorescence.FluorescenceStatus,
            loading: false,
            error: true,
            success: false,
          }
        }
      };

    case RESET_FLUORESCENCE_STATUS:
      return {
        ...state, Fluorescence: {
          ...state.Fluorescence,
          FluorescenceStatus: {
            ...state.Fluorescence.FluorescenceStatus,
            loading: false,
            error: false,
            success: false
          }
        }
      };
    // **************************************************




    default:
      return state;
  }
}
