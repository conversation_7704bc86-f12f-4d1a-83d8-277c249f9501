import { RESET_UPLOAD_HISTORY, RESET_UPLOAD_HISTORY_DOWNLOAD, UPLOAD_HISTORY_DOWNLOAD_ERROR, UPLOAD_HISTORY_DOWNLOAD_SUCCESS, UPLOAD_HISTORY_ERROR, UPLOAD_HISTORY_SUCCESS } from "../Constant";

let initialState = {
 getUploadHistoryList: {
  loading: false,
  data: null,
  error: false,
  success: false,
 },
 downloadHistory: {
  error: false,
  errors: null,
  success: false,
 }
};

export default function uploadHistoryReducer(state = initialState, action) {
 switch (action.type) {

  //  get invalid stone list
  case UPLOAD_HISTORY_SUCCESS:
   return { ...state, getUploadHistoryList: { ...state.getUploadHistoryList, loading: false, error: false, success: true, data: action.response.data } };

  case UPLOAD_HISTORY_ERROR:
   return { ...state, getUploadHistoryList: { ...state.getUploadHistoryList, loading: false, error: true, success: false, data: null } };

  case RESET_UPLOAD_HISTORY:
   return { ...state, getUploadHistoryList: { ...state.getUploadHistoryList, loading: false, error: false, success: false } };


  case UPLOAD_HISTORY_DOWNLOAD_SUCCESS:
   return { ...state, downloadHistory: { ...state.downloadHistory, success: true, error: false, errors: null }, };

  case UPLOAD_HISTORY_DOWNLOAD_ERROR:
   return { ...state, downloadHistory: { ...state.downloadHistory, success: false, error: true, errors: action.msg } };

  case RESET_UPLOAD_HISTORY_DOWNLOAD:
   return { ...state, downloadHistory: { ...state.downloadHistory, success: false, error: false, errors: null } };




  default:
   return state;
 }
}
