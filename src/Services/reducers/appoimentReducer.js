import { APPOIMENT_STATUS_ERROR, APPOIMENT_STATUS_SUCCESS, DELETE_APPOIMENT_ERROR, DELETE_APPOIMENT_SUCCESS, GET_APPOIMENT_ERROR, GET_APPOIMENT_LOADING, GET_APPOIMENT_SUCCESS, RESET_APPOIMENT_STATUS, RESET_DELETE_APPOIMENT, RESET_GET_APPOIMENT } from "../Constant";

let initialState = {

 getAppoiment: {
  loading: false,
  error: false,
  data: null,
  success: false,
 },
 deleteAppoiment: {
  loading: false,
  error: false,
  success: false,
 },
 statusAppoiment: {
  loading: false,
  error: false,
  success: false,
 }


}

export default function appoimentReducer(state = initialState, action) {
 switch (action.type) {

  //  diamond list
  case GET_APPOIMENT_LOADING:
   return { ...state, getAppoiment: { ...state.getAppoiment, loading: true, error: false, success: false } };

  case GET_APPOIMENT_SUCCESS:
   return { ...state, getAppoiment: { ...state.getAppoiment, loading: false, error: false, success: true, data: action.response.data } };

  case GET_APPOIMENT_ERROR:
   return { ...state, getAppoiment: { ...state.getAppoiment, loading: false, error: true, success: false, data: null } };

  case RESET_GET_APPOIMENT:
   return { ...state, getAppoiment: { ...state.getAppoiment, loading: false, error: false, success: false } };

  //  user list
  case DELETE_APPOIMENT_SUCCESS:
   return { ...state, deleteAppoiment: { ...state.deleteAppoiment, error: false, success: true } };

  case DELETE_APPOIMENT_ERROR:
   return { ...state, deleteAppoiment: { ...state.deleteAppoiment, error: true, success: false } };

  case RESET_DELETE_APPOIMENT:
   return { ...state, deleteAppoiment: { ...state.deleteAppoiment, error: false, success: false } };


  //  user list
  case APPOIMENT_STATUS_SUCCESS:
   return { ...state, statusAppoiment: { ...state.statusAppoiment, error: false, success: true } };

  case APPOIMENT_STATUS_ERROR:
   return { ...state, statusAppoiment: { ...state.statusAppoiment, error: true, success: false } };

  case RESET_APPOIMENT_STATUS:
   return { ...state, statusAppoiment: { ...state.statusAppoiment, error: false, success: false } };

  default:
   return state;
 }
}
