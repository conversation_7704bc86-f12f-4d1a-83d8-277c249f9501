import { ADD_VENDOR_LIST_ERROR, ADD_VENDOR_LIST_LOADING, ADD_VENDOR_LIST_SUCCESS, DELETE_VENDOR_LIST_ERROR, DELETE_VENDOR_LIST_SUCCESS, GET_VENDOR_DIAMOND_LIST_ERROR, GET_VENDOR_DIAMOND_LIST_LOADING, GET_VENDOR_DIAMOND_LIST_SUCCESS, GET_VENDOR_LIST_ERROR, GET_VENDOR_LIST_LOADING, GET_VENDOR_LIST_SUCCESS, RESET_ADD_VENDOR_LIST, RESET_DELETE_VENDOR_LIST, RESET_GET_VENDOR_DIAMOND_LIST, RESET_GET_VENDOR_LIST, RESET_UPDATE_VENDOR_LIST, RESET_VENDOR_COUNTRY_LIST, RESET_VENDOR_LIST_STATUS, UPDATE_VENDOR_LIST_ERROR, UPDATE_VENDOR_LIST_LOADING, UPDATE_VENDOR_LIST_SUCCESS, VENDOR_COUNTRY_LIST_ERROR, VENDOR_COUNTRY_LIST_SUCCESS, VENDOR_LIST_STATUS_ERROR, VENDOR_LIST_STATUS_SUCCESS } from "../Constant";

let initialState = {
  VendorList: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  VendorDiamondList: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  addVendorList: {
    loading: false,
    data: null,
    error: false,
    success: false,
    msg: null
  },
  updateVendorList: {
    loading: false,
    data: null,
    error: false,
    success: false,
  },
  deleteVendorList: {
    error: false,
    success: false,
  },
  statusVendorList: {
    error: false,
    success: false,
  },
  vendorCountryList: {
    data: null,
    error: false,
    success: false,
  }
};

export default function vendorListReducer(state = initialState, action) {
  switch (action.type) {
    //  get user list
    case GET_VENDOR_LIST_LOADING:
      return { ...state, VendorList: { ...state.VendorList, loading: true, error: false, success: false } };

    case GET_VENDOR_LIST_SUCCESS:
      return { ...state, VendorList: { ...state.VendorList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_VENDOR_LIST_ERROR:
      return { ...state, VendorList: { ...state.VendorList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_VENDOR_LIST:
      return { ...state, VendorList: { ...state.VendorList, loading: false, error: false, success: false } };

    //  get user diampond list
    case GET_VENDOR_DIAMOND_LIST_LOADING:
      return { ...state, VendorDiamondList: { ...state.VendorDiamondList, loading: true, error: false, success: false } };

    case GET_VENDOR_DIAMOND_LIST_SUCCESS:
      return { ...state, VendorDiamondList: { ...state.VendorDiamondList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_VENDOR_DIAMOND_LIST_ERROR:
      return { ...state, VendorDiamondList: { ...state.VendorDiamondList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_VENDOR_DIAMOND_LIST:
      return { ...state, VendorDiamondList: { ...state.VendorDiamondList, loading: false, error: false, success: false } };

    //  add user list
    case ADD_VENDOR_LIST_LOADING:
      return { ...state, addVendorList: { ...state.addVendorList, loading: true, error: false, success: false } };

    case ADD_VENDOR_LIST_SUCCESS:
      return { ...state, addVendorList: { ...state.addVendorList, loading: false, error: false, success: true } };

    case ADD_VENDOR_LIST_ERROR:
      return { ...state, addVendorList: { ...state.addVendorList, loading: false, error: true, success: false, msg: action.msg } };

    case RESET_ADD_VENDOR_LIST:
      return { ...state, addVendorList: { ...state.addVendorList, loading: false, error: false, success: false, msg: null } };


    //  update user list
    case UPDATE_VENDOR_LIST_LOADING:
      return { ...state, updateVendorList: { ...state.updateVendorList, loading: true, error: false, success: false } };

    case UPDATE_VENDOR_LIST_SUCCESS:
      return { ...state, updateVendorList: { ...state.updateVendorList, loading: false, error: false, success: true } };

    case UPDATE_VENDOR_LIST_ERROR:
      return { ...state, updateVendorList: { ...state.updateVendorList, loading: false, error: true, success: false, msg: action.msg } };

    case RESET_UPDATE_VENDOR_LIST:
      return { ...state, updateVendorList: { ...state.updateVendorList, loading: false, error: false, success: false } };


    //  delete user list
    case DELETE_VENDOR_LIST_SUCCESS:
      return { ...state, deleteVendorList: { ...state.deleteVendorList, error: false, success: true } };

    case DELETE_VENDOR_LIST_ERROR:
      return { ...state, deleteVendorList: { ...state.deleteVendorList, error: true, success: false } };

    case RESET_DELETE_VENDOR_LIST:
      return { ...state, deleteVendorList: { ...state.deleteVendorList, error: false, success: false } };

    //  status user list
    case VENDOR_LIST_STATUS_SUCCESS:
      return { ...state, statusVendorList: { ...state.statusVendorList, error: false, success: true } };

    case VENDOR_LIST_STATUS_ERROR:
      return { ...state, statusVendorList: { ...state.statusVendorList, error: true, success: false } };

    case RESET_VENDOR_LIST_STATUS:
      return { ...state, statusVendorList: { ...state.statusVendorList, error: false, success: false } };

    //  vendor  country
    case VENDOR_COUNTRY_LIST_SUCCESS:
      return { ...state, vendorCountryList: { ...state.vendorCountryList, error: false, success: true, data: action.response } };

    case VENDOR_COUNTRY_LIST_ERROR:
      return { ...state, vendorCountryList: { ...state.vendorCountryList, error: true, success: false, data: null } };

    case RESET_VENDOR_COUNTRY_LIST:
      return { ...state, vendorCountryList: { ...state.vendorCountryList, error: false, success: false } };

    default:
      return state;
  }
}