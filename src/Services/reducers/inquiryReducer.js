import { DELETE_INQUIRY_STONE_ERROR, DELETE_INQUIRY_STONE_LOADING, DELETE_INQUIRY_STONE_SUCCESS, GET_INQUIRY_LIST_ERROR, GET_INQUIRY_LIST_LOADING, GET_INQUIRY_LIST_SUCCESS, GET_INQUIRY_USER_ERROR, GET_INQUIRY_USER_LOADING, GET_INQUIRY_USER_SUCCESS, RESET_DELETE_INQUIRY_STONE, RESET_GET_INQUIRY_LIST, RESET_GET_INQUIRY_USER } from "../Constant";

let initialState = {

  getInquiryList: {
    loading: false,
    error: false,
    data: null,
    success: false,
  },
  getInquirydUserList: {
    loading: false,
    error: false,
    data: null,
    success: false,
  },
  deleteInquiry: {
    loading: false,
    error: false,
    data: null,
    success: false,
  },


}

export default function inquiryReducer(state = initialState, action) {
  switch (action.type) {

    //  diamond list
    case GET_INQUIRY_LIST_LOADING:
      return { ...state, getInquiryList: { ...state.getInquiryList, loading: true, error: false, success: false } };

    case GET_INQUIRY_LIST_SUCCESS:
      return { ...state, getInquiryList: { ...state.getInquiryList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_INQUIRY_LIST_ERROR:
      return { ...state, getInquiryList: { ...state.getInquiryList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_INQUIRY_LIST:
      return { ...state, getInquiryList: { ...state.getInquiryList, loading: false, error: false, success: false } };

    //  user list
    case GET_INQUIRY_USER_LOADING:
      return { ...state, getInquirydUserList: { ...state.getInquirydUserList, loading: true, error: false, success: false } };

    case GET_INQUIRY_USER_SUCCESS:
      return { ...state, getInquirydUserList: { ...state.getInquirydUserList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_INQUIRY_USER_ERROR:
      return { ...state, getInquirydUserList: { ...state.getInquirydUserList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_INQUIRY_USER:
      return { ...state, getInquirydUserList: { ...state.getInquirydUserList, loading: false, error: false, success: false } };

    //  delete Inquiry
    case DELETE_INQUIRY_STONE_LOADING:
      return { ...state, deleteInquiry: { ...state.deleteInquiry, loading: true, error: false, success: false } };

    case DELETE_INQUIRY_STONE_SUCCESS:
      return { ...state, deleteInquiry: { ...state.deleteInquiry, loading: false, error: false, success: true, data: action.response.data } };

    case DELETE_INQUIRY_STONE_ERROR:
      return { ...state, deleteInquiry: { ...state.deleteInquiry, loading: false, error: true, success: false, data: null } };

    case RESET_DELETE_INQUIRY_STONE:
      return { ...state, deleteInquiry: { ...state.deleteInquiry, loading: false, error: false, success: false } };

    default:
      return state;
  }
}
