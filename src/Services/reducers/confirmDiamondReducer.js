import { GET_CONFIRM_DIAMOND_LIST_ERROR, GET_CONFIRM_DIAMOND_LIST_LOADING, GET_CONFIRM_DIAMOND_LIST_SUCCESS, GET_CONFIRM_DIAMOND_USER_ERROR, GET_CONFIRM_DIAMOND_USER_LOADING, GET_CONFIRM_DIAMOND_USER_SUCCESS, GET_HOLD_DIAMOND_LIST_ERROR, GET_HOLD_DIAMOND_LIST_LOADING, GET_HOLD_DIAMOND_LIST_SUCCESS, GET_HOLD_DIAMOND_USER_ERROR, GET_HOLD_DIAMOND_USER_LOADING, GET_HOLD_DIAMOND_USER_SUCCESS, GET_SLOT_ERROR, GET_SLOT_LOADING, GET_SLOT_SUCCESS, RELEASE_ERROR, RELEASE_LOADING, RELEASE_SUCCESS, RESET_GET_CONFIRM_DIAMOND_LIST, RESET_GET_CONFIRM_DIAMOND_USER, RESET_GET_HOLD_DIAMOND_LIST, RESET_GET_HOLD_DIAMOND_USER, RESET_GET_SLOT, RESET_RELEASE } from "../Constant";

let initialState = {

  getConfirmDiamondList: {
    loading: false,
    error: false,
    data: null,
    success: false,
  },
  getConfirmDiamondUserList: {
    loading: false,
    error: false,
    data: null,
    success: false,
  }
  , releaseList: {
    loading: false,
    error: false,
    success: false,
  }


}

export default function confirmDiamondReducer(state = initialState, action) {
  switch (action.type) {

    //  diamond list
    case GET_CONFIRM_DIAMOND_LIST_LOADING:
      return { ...state, getConfirmDiamondList: { ...state.getConfirmDiamondList, loading: true, error: false, success: false } };

    case GET_CONFIRM_DIAMOND_LIST_SUCCESS:
      return { ...state, getConfirmDiamondList: { ...state.getConfirmDiamondList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_CONFIRM_DIAMOND_LIST_ERROR:
      return { ...state, getConfirmDiamondList: { ...state.getConfirmDiamondList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_CONFIRM_DIAMOND_LIST:
      return { ...state, getConfirmDiamondList: { ...state.getConfirmDiamondList, loading: false, error: false, success: false } };

    //  user list
    case GET_CONFIRM_DIAMOND_USER_LOADING:
      return { ...state, getConfirmDiamondUserList: { ...state.getConfirmDiamondUserList, loading: true, error: false, success: false } };

    case GET_CONFIRM_DIAMOND_USER_SUCCESS:
      return { ...state, getConfirmDiamondUserList: { ...state.getConfirmDiamondUserList, loading: false, error: false, success: true, data: action.response.data } };

    case GET_CONFIRM_DIAMOND_USER_ERROR:
      return { ...state, getConfirmDiamondUserList: { ...state.getConfirmDiamondUserList, loading: false, error: true, success: false, data: null } };

    case RESET_GET_CONFIRM_DIAMOND_USER:
      return { ...state, getConfirmDiamondUserList: { ...state.getConfirmDiamondUserList, loading: false, error: false, success: false } };

    //  user list
    case RELEASE_LOADING:
      return { ...state, releaseList: { ...state.releaseList, loading: true, error: false, success: false } };

    case RELEASE_SUCCESS:
      return { ...state, releaseList: { ...state.releaseList, loading: false, error: false, success: true } };

    case RELEASE_ERROR:
      return { ...state, releaseList: { ...state.releaseList, loading: false, error: true, success: false} };

    case RESET_RELEASE:
      return { ...state, releaseList: { ...state.releaseList, loading: false, error: false, success: false } };

    default:
      return state;
  }
}
