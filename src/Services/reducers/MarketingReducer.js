import { ADD_MARKETING_ERROR, ADD_MARKETING_LOADING, ADD_MARKETING_SUCCESS, DELETE_MARKETING_ERROR, DELETE_MARKETING_SUCCESS, GET_MARKETING_ERROR, GET_MARKETING_LOADING, GET_MARKETING_SUCCESS, MARKETING_STATUS_ERROR, MARKETING_STATUS_SUCCESS, RESET_ADD_MARKETING, RESET_DELETE_MARKETING, RESET_GET_MARKETING, RESET_MARKETING_STATUS } from "../Constant";

let initialState = {
 getMarketing: {
  loading: false,
  data: null,
  error: false,
  success: false,
 },
 addMarketing: {
  loading: false,
  data: null,
  error: false,
  success: false,
  msg: null
 },
 deleteMarketing: {
  error: false,
  success: false,
 },
 statusMarketing: {
  error: false,
  success: false,
 },

};

export default function MarketingReducer(state = initialState, action) {
 switch (action.type) {

  //  get Marketing
  case GET_MARKETING_LOADING:
   return { ...state, getMarketing: { ...state.getMarketing, loading: true, error: false, success: false } };

  case GET_MARKETING_SUCCESS:
   return { ...state, getMarketing: { ...state.getMarketing, loading: false, error: false, success: true, data: action.response.data } };

  case GET_MARKETING_ERROR:
   return { ...state, getMarketing: { ...state.getMarketing, loading: false, error: true, success: false, data: null } };

  case RESET_GET_MARKETING:
   return { ...state, getMarketing: { ...state.getMarketing, loading: false, error: false, success: false } };

  //  add marketing
  case ADD_MARKETING_LOADING:
   return { ...state, addMarketing: { ...state.addMarketing, loading: true, error: false, success: false } };

  case ADD_MARKETING_SUCCESS:
   return { ...state, addMarketing: { ...state.addMarketing, loading: false, error: false, success: true } };

  case ADD_MARKETING_ERROR:
   return { ...state, addMarketing: { ...state.addMarketing, loading: false, error: true, success: false, msg: action.msg } };

  case RESET_ADD_MARKETING:
   return { ...state, addMarketing: { ...state.addMarketing, loading: false, error: false, success: false, msg: null } };


  //  delete marketing
  case DELETE_MARKETING_SUCCESS:
   return { ...state, deleteMarketing: { ...state.deleteMarketing, error: false, success: true } };

  case DELETE_MARKETING_ERROR:
   return { ...state, deleteMarketing: { ...state.deleteMarketing, error: true, success: false } };

  case RESET_DELETE_MARKETING:
   return { ...state, deleteMarketing: { ...state.deleteMarketing, error: false, success: false } };

  //  status marketing
  case MARKETING_STATUS_SUCCESS:
   return { ...state, statusMarketing: { ...state.statusMarketing, error: false, success: true } };

  case MARKETING_STATUS_ERROR:
   return { ...state, statusMarketing: { ...state.statusMarketing, error: true, success: false } };

  case RESET_MARKETING_STATUS:
   return { ...state, statusMarketing: { ...state.statusMarketing, error: false, success: false } };


  default:
   return state;
 }
}
