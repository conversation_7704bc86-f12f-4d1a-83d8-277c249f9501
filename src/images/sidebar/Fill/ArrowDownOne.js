import React from 'react'

const ArrowDownOne = () => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M10.6667 7.99999C10.6667 8.15199 10.6154 8.30332 10.512 8.42666L7.1787 12.4267C6.94336 12.7093 6.5227 12.748 6.24003 12.512C5.95736 12.2767 5.91936 11.8567 6.1547 11.5733L9.1387 7.99266L6.26203 4.41799C6.03136 4.13132 6.0767 3.71132 6.36336 3.48066C6.65003 3.24999 7.06936 3.29532 7.3007 3.58199L10.5194 7.58199C10.6174 7.70399 10.6667 7.85199 10.6667 7.99999Z" fill="currentColor"/>
        <mask id="mask120" mask-type="alpha" maskUnits="userSpaceOnUse" x="6" y="3" width="5" height="10">
        <path fillRule="evenodd" clipRule="evenodd" d="M10.6667 7.99999C10.6667 8.15199 10.6154 8.30332 10.512 8.42666L7.1787 12.4267C6.94336 12.7093 6.5227 12.748 6.24003 12.512C5.95736 12.2767 5.91936 11.8567 6.1547 11.5733L9.1387 7.99266L6.26203 4.41799C6.03136 4.13132 6.0767 3.71132 6.36336 3.48066C6.65003 3.24999 7.06936 3.29532 7.3007 3.58199L10.5194 7.58199C10.6174 7.70399 10.6667 7.85199 10.6667 7.99999Z" fill="white"/>
        </mask>
        <g mask="url(#mask120)">
        <rect y="16" width="16" height="16" transform="rotate(-90 0 16)" fill="currentColor"/>
        </g>
    </svg>
)

export default ArrowDownOne