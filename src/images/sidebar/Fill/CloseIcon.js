import React from 'react'

const CloseIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M7.99998 7.05729L5.13798 4.19529C4.87732 3.93462 4.45598 3.93462 4.19532 4.19529C3.93465 4.45595 3.93465 4.87729 4.19532 5.13795L7.05732 7.99996L4.19532 10.862C3.93465 11.1226 3.93465 11.544 4.19532 11.8046C4.45598 12.0653 4.87732 12.0653 5.13798 11.8046L7.99998 8.94262L10.862 11.8046C11.1227 12.0653 11.544 12.0653 11.8047 11.8046C11.9347 11.6746 12 11.504 12 11.3333C12 11.1626 11.9347 10.992 11.8047 10.862L8.94265 7.99996L11.8047 5.13795C11.9347 5.00795 12 4.83729 12 4.66662C12 4.49595 11.9347 4.32529 11.8047 4.19529C11.544 3.93462 11.1227 3.93462 10.862 4.19529L7.99998 7.05729Z" fill="currentColor"/>
        <mask id="mask121" mask-type="alpha" maskUnits="userSpaceOnUse" x="3" y="3" width="9" height="10">
        <path fillRule="evenodd" clipRule="evenodd" d="M7.99998 7.05729L5.13798 4.19529C4.87732 3.93462 4.45598 3.93462 4.19532 4.19529C3.93465 4.45595 3.93465 4.87729 4.19532 5.13795L7.05732 7.99996L4.19532 10.862C3.93465 11.1226 3.93465 11.544 4.19532 11.8046C4.45598 12.0653 4.87732 12.0653 5.13798 11.8046L7.99998 8.94262L10.862 11.8046C11.1227 12.0653 11.544 12.0653 11.8047 11.8046C11.9347 11.6746 12 11.504 12 11.3333C12 11.1626 11.9347 10.992 11.8047 10.862L8.94265 7.99996L11.8047 5.13795C11.9347 5.00795 12 4.83729 12 4.66662C12 4.49595 11.9347 4.32529 11.8047 4.19529C11.544 3.93462 11.1227 3.93462 10.862 4.19529L7.99998 7.05729Z" fill="white"/>
        </mask>
        <g mask="url(#mask121)">
        <rect y="16" width="16" height="16" transform="rotate(-90 0 16)" fill="currentColor"/>
        </g>
    </svg>    
)


export default CloseIcon