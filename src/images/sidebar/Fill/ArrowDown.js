import React from 'react'

const ArrowDown = () => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M7.99998 10.6667C7.84798 10.6667 7.69665 10.6153 7.57331 10.512L3.57331 7.17867C3.29065 6.94333 3.25198 6.52267 3.48798 6.24C3.72331 5.95733 4.14331 5.91933 4.42665 6.15467L8.00731 9.13867L11.582 6.262C11.8686 6.03133 12.2886 6.07667 12.5193 6.36333C12.75 6.65 12.7046 7.06933 12.418 7.30067L8.41798 10.5193C8.29598 10.6173 8.14798 10.6667 7.99998 10.6667Z" fill="currentColor"/>
        <mask id="mask119" mask-type="alpha" maskUnits="userSpaceOnUse" x="3" y="6" width="10" height="5">
        <path fillRule="evenodd" clipRule="evenodd" d="M7.99998 10.6667C7.84798 10.6667 7.69665 10.6153 7.57331 10.512L3.57331 7.17867C3.29065 6.94333 3.25198 6.52267 3.48798 6.24C3.72331 5.95733 4.14331 5.91933 4.42665 6.15467L8.00731 9.13867L11.582 6.262C11.8686 6.03133 12.2886 6.07667 12.5193 6.36333C12.75 6.65 12.7046 7.06933 12.418 7.30067L8.41798 10.5193C8.29598 10.6173 8.14798 10.6667 7.99998 10.6667Z" fill="white"/>
        </mask>
        <g mask="url(#mask119)">
        <rect width="16" height="16" fill="currentColor"/>
        </g>
    </svg>    
)


export default ArrowDown