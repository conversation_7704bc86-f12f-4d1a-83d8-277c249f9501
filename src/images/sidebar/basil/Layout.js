import React from 'react'

const Layout = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M8.92916 4.47028C7.65795 4.3282 6.34204 4.3282 5.07082 4.47028C4.76193 4.5048 4.51833 4.74939 4.48359 5.04645C4.33178 6.34439 4.33178 7.6556 4.48359 8.95355C4.51833 9.2506 4.76193 9.49519 5.07082 9.52972C6.34204 9.67179 7.65795 9.67179 8.92916 9.52972C9.23805 9.49519 9.48165 9.2506 9.5164 8.95355C9.6682 7.6556 9.6682 6.34439 9.5164 5.04645C9.48165 4.74939 9.23805 4.5048 8.92916 4.47028ZM4.90421 2.97956C6.28616 2.82511 7.71383 2.82511 9.09577 2.97956C10.0866 3.0903 10.8891 3.87029 11.0062 4.8722C11.1716 6.28591 11.1716 7.71408 11.0062 9.1278C10.8891 10.1297 10.0866 10.9097 9.09577 11.0204C7.71383 11.1749 6.28616 11.1749 4.90421 11.0204C3.91337 10.9097 3.11093 10.1297 2.99375 9.1278C2.8284 7.71408 2.8284 6.28591 2.99375 4.8722C3.11093 3.87029 3.91337 3.0903 4.90421 2.97956Z" />
            <path fillRule="evenodd" clipRule="evenodd" d="M8.92916 14.4703C7.65795 14.3282 6.34204 14.3282 5.07082 14.4703C4.76193 14.5048 4.51833 14.7494 4.48359 15.0465C4.33178 16.3444 4.33178 17.6556 4.48359 18.9535C4.51833 19.2506 4.76193 19.4952 5.07082 19.5297C6.34204 19.6718 7.65795 19.6718 8.92916 19.5297C9.23805 19.4952 9.48165 19.2506 9.5164 18.9535C9.6682 17.6556 9.6682 16.3444 9.5164 15.0465C9.48165 14.7494 9.23805 14.5048 8.92916 14.4703ZM4.90421 12.9796C6.28616 12.8251 7.71383 12.8251 9.09577 12.9796C10.0866 13.0903 10.8891 13.8703 11.0062 14.8722C11.1716 16.2859 11.1716 17.7141 11.0062 19.1278C10.8891 20.1297 10.0866 20.9097 9.09577 21.0204C7.71383 21.1749 6.28616 21.1749 4.90421 21.0204C3.91337 20.9097 3.11093 20.1297 2.99375 19.1278C2.8284 17.7141 2.8284 16.2859 2.99375 14.8722C3.11093 13.8703 3.91337 13.0903 4.90421 12.9796Z" />
            <path fillRule="evenodd" clipRule="evenodd" d="M18.9292 4.47028C17.6579 4.3282 16.342 4.3282 15.0708 4.47028C14.7619 4.5048 14.5183 4.74939 14.4836 5.04645C14.3318 6.34439 14.3318 7.6556 14.4836 8.95355C14.5183 9.2506 14.7619 9.49519 15.0708 9.52972C16.342 9.67179 17.6579 9.67179 18.9292 9.52972C19.2381 9.49519 19.4817 9.2506 19.5164 8.95355C19.6682 7.6556 19.6682 6.34439 19.5164 5.04645C19.4817 4.74939 19.2381 4.5048 18.9292 4.47028ZM14.9042 2.97956C16.2862 2.82511 17.7138 2.82511 19.0958 2.97956C20.0866 3.0903 20.8891 3.87029 21.0062 4.8722C21.1716 6.28591 21.1716 7.71408 21.0062 9.1278C20.8891 10.1297 20.0866 10.9097 19.0958 11.0204C17.7138 11.1749 16.2862 11.1749 14.9042 11.0204C13.9134 10.9097 13.1109 10.1297 12.9937 9.1278C12.8284 7.71408 12.8284 6.28591 12.9937 4.8722C13.1109 3.87029 13.9134 3.0903 14.9042 2.97956Z" />
            <path fillRule="evenodd" clipRule="evenodd" d="M18.9292 14.4703C17.6579 14.3282 16.342 14.3282 15.0708 14.4703C14.7619 14.5048 14.5183 14.7494 14.4836 15.0465C14.3318 16.3444 14.3318 17.6556 14.4836 18.9535C14.5183 19.2506 14.7619 19.4952 15.0708 19.5297C16.342 19.6718 17.6579 19.6718 18.9292 19.5297C19.2381 19.4952 19.4817 19.2506 19.5164 18.9535C19.6682 17.6556 19.6682 16.3444 19.5164 15.0465C19.4817 14.7494 19.2381 14.5048 18.9292 14.4703ZM14.9042 12.9796C16.2862 12.8251 17.7138 12.8251 19.0958 12.9796C20.0866 13.0903 20.8891 13.8703 21.0062 14.8722C21.1716 16.2859 21.1716 17.7141 21.0062 19.1278C20.8891 20.1297 20.0866 20.9097 19.0958 21.0204C17.7138 21.1749 16.2862 21.1749 14.9042 21.0204C13.9134 20.9097 13.1109 20.1297 12.9937 19.1278C12.8284 17.7141 12.8284 16.2859 12.9937 14.8722C13.1109 13.8703 13.9134 13.0903 14.9042 12.9796Z" />
    </svg>

)


export default Layout
