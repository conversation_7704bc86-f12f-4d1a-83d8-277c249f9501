import React from 'react';

const PieChart = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M17.3511 11.7147C16.9153 11.5597 16.4403 11.7872 16.2878 12.2222C15.3478 14.8805 12.8203 16.6663 10.0003 16.6663C6.32443 16.6663 3.3336 13.6763 3.3336 9.99965C3.3336 7.17965 5.11943 4.65215 7.77776 3.71215C8.21193 3.55965 8.43943 3.08382 8.2861 2.64965C8.13276 2.21632 7.65693 1.98799 7.22276 2.14215C3.89943 3.31632 1.66693 6.47382 1.66693 9.99965C1.66693 14.5947 5.40526 18.333 10.0003 18.333C13.5261 18.333 16.6836 16.1013 17.8586 12.7772C18.0119 12.3438 17.7853 11.868 17.3511 11.7147ZM11.6667 8.33333V3.39249C14.2175 3.75916 16.2408 5.78166 16.6075 8.33333H11.6667ZM10.8333 1.66666C10.3733 1.66666 10 2.03916 10 2.49999V9.16666C10 9.62749 10.3733 9.99999 10.8333 9.99999H17.5C17.96 9.99999 18.3333 9.62749 18.3333 9.16666C18.3333 5.03082 14.9683 1.66666 10.8333 1.66666Z" fill="currentColor"/>
        <mask id="mask114" mask-type="alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="18" height="18">
        <path fillRule="evenodd" clipRule="evenodd" d="M17.3511 11.7147C16.9153 11.5597 16.4403 11.7872 16.2878 12.2222C15.3478 14.8805 12.8203 16.6663 10.0003 16.6663C6.32443 16.6663 3.3336 13.6763 3.3336 9.99965C3.3336 7.17965 5.11943 4.65215 7.77776 3.71215C8.21193 3.55965 8.43943 3.08382 8.2861 2.64965C8.13276 2.21632 7.65693 1.98799 7.22276 2.14215C3.89943 3.31632 1.66693 6.47382 1.66693 9.99965C1.66693 14.5947 5.40526 18.333 10.0003 18.333C13.5261 18.333 16.6836 16.1013 17.8586 12.7772C18.0119 12.3438 17.7853 11.868 17.3511 11.7147ZM11.6667 8.33333V3.39249C14.2175 3.75916 16.2408 5.78166 16.6075 8.33333H11.6667ZM10.8333 1.66666C10.3733 1.66666 10 2.03916 10 2.49999V9.16666C10 9.62749 10.3733 9.99999 10.8333 9.99999H17.5C17.96 9.99999 18.3333 9.62749 18.3333 9.16666C18.3333 5.03082 14.9683 1.66666 10.8333 1.66666Z" fill="white"/>
        </mask>
        <g mask="url(#mask114)">
        <rect width="20" height="20" fill="currentColor"/>
        </g>
    </svg>
)

export default PieChart