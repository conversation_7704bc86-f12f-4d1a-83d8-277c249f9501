import React from 'react';

const Settings = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M3.66898 9.68508C4.59898 10.0042 5.33981 10.7826 5.64981 11.7692L5.68314 11.8692C6.03814 12.9159 5.89398 14.0426 5.29898 14.8876C5.19064 15.0409 5.21481 15.2242 5.32898 15.3109L7.05564 16.6226C7.11648 16.6684 7.17564 16.6684 7.21231 16.6642C7.25398 16.6576 7.31481 16.6351 7.36398 16.5651L7.55648 16.2917C8.13064 15.4776 9.05564 14.9751 10.0323 14.9467C11.129 14.9242 12.114 15.4267 12.7273 16.3126L12.8256 16.4551C12.8748 16.5251 12.9348 16.5484 12.9773 16.5551C13.014 16.5626 13.074 16.5601 13.134 16.5134L14.8515 15.2176C14.9715 15.1276 14.9981 14.9351 14.909 14.8059L14.6923 14.4934C14.134 13.6867 13.9681 12.6401 14.249 11.6942C14.554 10.6642 15.3298 9.84925 16.3256 9.51591L16.4931 9.45925C16.6273 9.41508 16.6998 9.24841 16.6523 9.09508L15.9965 6.99425C15.9656 6.89592 15.9023 6.85175 15.8673 6.83341C15.8173 6.80758 15.7631 6.80341 15.7115 6.82091L15.4281 6.91508C14.459 7.23758 13.3898 7.06258 12.569 6.44508L12.479 6.37758C11.699 5.79092 11.2348 4.84508 11.2381 3.84841L11.2398 3.61508C11.2398 3.50425 11.1873 3.43508 11.1556 3.40341C11.1256 3.37258 11.0748 3.33591 11.0031 3.33591L8.88064 3.33341C8.75064 3.33341 8.64481 3.45758 8.64398 3.61091L8.64314 3.81258C8.63898 4.82508 8.16481 5.78841 7.37481 6.39091L7.26731 6.47258C6.39814 7.13341 5.26481 7.32008 4.23814 6.97008C4.19898 6.95675 4.16231 6.95925 4.12731 6.97758C4.10064 6.99091 4.05231 7.02508 4.02898 7.10091L3.34814 9.26425C3.29898 9.42175 3.37981 9.58591 3.53231 9.63841L3.66898 9.68508ZM7.17814 18.3334C6.77314 18.3334 6.37981 18.2017 6.04814 17.9492L4.32148 16.6384C3.49648 16.0134 3.31398 14.8109 3.91398 13.9584C4.22564 13.5167 4.28981 12.9492 4.10648 12.4109L4.06064 12.2709C3.90814 11.7859 3.55981 11.4092 3.12898 11.2617H3.12814L2.99231 11.2142C1.97731 10.8667 1.43564 9.79091 1.75814 8.76425L2.43814 6.60175C2.59231 6.11258 2.92481 5.71758 3.37481 5.49008C3.81481 5.26841 4.31231 5.23425 4.77731 5.39341C5.27648 5.56341 5.83064 5.47091 6.25814 5.14591L6.36564 5.06425C6.74564 4.77425 6.97481 4.30341 6.97648 3.80591L6.97731 3.60508C6.98148 2.53508 7.83564 1.66675 8.87981 1.66675H8.88314L11.0056 1.66925C11.5073 1.67008 11.9806 1.86841 12.3373 2.22758C12.7065 2.59841 12.9081 3.09425 12.9065 3.62341L12.9048 3.85591C12.9031 4.32758 13.119 4.77341 13.4831 5.04675L13.5723 5.11425C13.9548 5.40175 14.4531 5.48425 14.9006 5.33425L15.1831 5.24008C15.664 5.08008 16.1756 5.11925 16.6265 5.35008C17.089 5.58675 17.4306 5.99425 17.5873 6.49841L18.2431 8.59925C18.5598 9.61425 18.0115 10.7092 17.0223 11.0401L16.8548 11.0959C16.3748 11.2576 15.9973 11.6576 15.8465 12.1676C15.7081 12.6351 15.7881 13.1492 16.0623 13.5442L16.279 13.8567C16.874 14.7167 16.684 15.9234 15.8556 16.5476L14.1381 17.8442C13.7256 18.1559 13.2198 18.2817 12.7123 18.2009C12.2006 18.1184 11.754 17.8351 11.4548 17.4034L11.3565 17.2601C11.0648 16.8401 10.5981 16.5851 10.109 16.6126C9.61898 16.6259 9.19564 16.8584 8.91898 17.2517L8.72648 17.5251C8.42481 17.9526 7.97731 18.2317 7.46814 18.3117C7.37064 18.3267 7.27398 18.3334 7.17814 18.3334ZM9.99998 8.75C9.31081 8.75 8.74998 9.31084 8.74998 10C8.74998 10.6892 9.31081 11.25 9.99998 11.25C10.6891 11.25 11.25 10.6892 11.25 10C11.25 9.31084 10.6891 8.75 9.99998 8.75ZM9.99998 12.9167C8.39164 12.9167 7.08331 11.6083 7.08331 10C7.08331 8.39167 8.39164 7.08333 9.99998 7.08333C11.6083 7.08333 12.9166 8.39167 12.9166 10C12.9166 11.6083 11.6083 12.9167 9.99998 12.9167Z" fill="currentColor"/>
        <mask id="mask117" mask-type="alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="18" height="18">
        <path fillRule="evenodd" clipRule="evenodd" d="M3.66898 9.68508C4.59898 10.0042 5.33981 10.7826 5.64981 11.7692L5.68314 11.8692C6.03814 12.9159 5.89398 14.0426 5.29898 14.8876C5.19064 15.0409 5.21481 15.2242 5.32898 15.3109L7.05564 16.6226C7.11648 16.6684 7.17564 16.6684 7.21231 16.6642C7.25398 16.6576 7.31481 16.6351 7.36398 16.5651L7.55648 16.2917C8.13064 15.4776 9.05564 14.9751 10.0323 14.9467C11.129 14.9242 12.114 15.4267 12.7273 16.3126L12.8256 16.4551C12.8748 16.5251 12.9348 16.5484 12.9773 16.5551C13.014 16.5626 13.074 16.5601 13.134 16.5134L14.8515 15.2176C14.9715 15.1276 14.9981 14.9351 14.909 14.8059L14.6923 14.4934C14.134 13.6867 13.9681 12.6401 14.249 11.6942C14.554 10.6642 15.3298 9.84925 16.3256 9.51591L16.4931 9.45925C16.6273 9.41508 16.6998 9.24841 16.6523 9.09508L15.9965 6.99425C15.9656 6.89592 15.9023 6.85175 15.8673 6.83341C15.8173 6.80758 15.7631 6.80341 15.7115 6.82091L15.4281 6.91508C14.459 7.23758 13.3898 7.06258 12.569 6.44508L12.479 6.37758C11.699 5.79092 11.2348 4.84508 11.2381 3.84841L11.2398 3.61508C11.2398 3.50425 11.1873 3.43508 11.1556 3.40341C11.1256 3.37258 11.0748 3.33591 11.0031 3.33591L8.88064 3.33341C8.75064 3.33341 8.64481 3.45758 8.64398 3.61091L8.64314 3.81258C8.63898 4.82508 8.16481 5.78841 7.37481 6.39091L7.26731 6.47258C6.39814 7.13341 5.26481 7.32008 4.23814 6.97008C4.19898 6.95675 4.16231 6.95925 4.12731 6.97758C4.10064 6.99091 4.05231 7.02508 4.02898 7.10091L3.34814 9.26425C3.29898 9.42175 3.37981 9.58591 3.53231 9.63841L3.66898 9.68508ZM7.17814 18.3334C6.77314 18.3334 6.37981 18.2017 6.04814 17.9492L4.32148 16.6384C3.49648 16.0134 3.31398 14.8109 3.91398 13.9584C4.22564 13.5167 4.28981 12.9492 4.10648 12.4109L4.06064 12.2709C3.90814 11.7859 3.55981 11.4092 3.12898 11.2617H3.12814L2.99231 11.2142C1.97731 10.8667 1.43564 9.79091 1.75814 8.76425L2.43814 6.60175C2.59231 6.11258 2.92481 5.71758 3.37481 5.49008C3.81481 5.26841 4.31231 5.23425 4.77731 5.39341C5.27648 5.56341 5.83064 5.47091 6.25814 5.14591L6.36564 5.06425C6.74564 4.77425 6.97481 4.30341 6.97648 3.80591L6.97731 3.60508C6.98148 2.53508 7.83564 1.66675 8.87981 1.66675H8.88314L11.0056 1.66925C11.5073 1.67008 11.9806 1.86841 12.3373 2.22758C12.7065 2.59841 12.9081 3.09425 12.9065 3.62341L12.9048 3.85591C12.9031 4.32758 13.119 4.77341 13.4831 5.04675L13.5723 5.11425C13.9548 5.40175 14.4531 5.48425 14.9006 5.33425L15.1831 5.24008C15.664 5.08008 16.1756 5.11925 16.6265 5.35008C17.089 5.58675 17.4306 5.99425 17.5873 6.49841L18.2431 8.59925C18.5598 9.61425 18.0115 10.7092 17.0223 11.0401L16.8548 11.0959C16.3748 11.2576 15.9973 11.6576 15.8465 12.1676C15.7081 12.6351 15.7881 13.1492 16.0623 13.5442L16.279 13.8567C16.874 14.7167 16.684 15.9234 15.8556 16.5476L14.1381 17.8442C13.7256 18.1559 13.2198 18.2817 12.7123 18.2009C12.2006 18.1184 11.754 17.8351 11.4548 17.4034L11.3565 17.2601C11.0648 16.8401 10.5981 16.5851 10.109 16.6126C9.61898 16.6259 9.19564 16.8584 8.91898 17.2517L8.72648 17.5251C8.42481 17.9526 7.97731 18.2317 7.46814 18.3117C7.37064 18.3267 7.27398 18.3334 7.17814 18.3334ZM9.99998 8.75C9.31081 8.75 8.74998 9.31084 8.74998 10C8.74998 10.6892 9.31081 11.25 9.99998 11.25C10.6891 11.25 11.25 10.6892 11.25 10C11.25 9.31084 10.6891 8.75 9.99998 8.75ZM9.99998 12.9167C8.39164 12.9167 7.08331 11.6083 7.08331 10C7.08331 8.39167 8.39164 7.08333 9.99998 7.08333C11.6083 7.08333 12.9166 8.39167 12.9166 10C12.9166 11.6083 11.6083 12.9167 9.99998 12.9167Z" fill="white"/>
        </mask>
        <g mask="url(#mask117)">
        <rect width="20" height="20" fill="currentColor"/>
        </g>
    </svg>    
)


export default Settings