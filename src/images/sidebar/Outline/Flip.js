import React from 'react';

const Flip = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M6.00584 8.08918C5.68001 8.41502 5.15334 8.41502 4.82751 8.08918L2.3275 5.58918C2.00167 5.26335 2.00167 4.73668 2.3275 4.41085L4.82751 1.91085C4.99001 1.74835 5.20334 1.66668 5.41667 1.66668C5.63 1.66668 5.84334 1.74835 6.00584 1.91085C6.33167 2.23668 6.33167 2.76335 6.00584 3.08918L4.92834 4.16668H14.925C16.575 4.16668 17.9167 5.48585 17.9167 7.10752V9.16668C17.9167 9.62751 17.5442 10 17.0833 10C16.6225 10 16.25 9.62751 16.25 9.16668V7.10752C16.25 6.40501 15.6558 5.83335 14.925 5.83335H4.92834L6.00584 6.91085C6.33167 7.23668 6.33167 7.76335 6.00584 8.08918ZM15.0715 15.8333H5.07485C3.42568 15.8333 2.08318 14.5142 2.08318 12.8925V10.8333C2.08318 10.3725 2.45568 10 2.91651 10C3.37735 10 3.74985 10.3725 3.74985 10.8333V12.8925C3.74985 13.5942 4.34401 14.1667 5.07485 14.1667H15.0715L13.994 13.0892C13.669 12.7633 13.669 12.2367 13.994 11.9108C14.1565 11.7483 14.3698 11.6667 14.5832 11.6667C14.7965 11.6667 15.0098 11.7483 15.1723 11.9108L17.6723 14.4108C17.9982 14.7367 17.9982 15.2633 17.6723 15.5892L15.1723 18.0892C14.8473 18.415 14.3198 18.415 13.994 18.0892C13.669 17.7633 13.669 17.2367 13.994 16.9108L15.0715 15.8333Z" fill="currentColor"/>
        <mask id="mask105" mask-type="alpha" maskUnits="userSpaceOnUse" x="2" y="1" width="16" height="18">
        <path fillRule="evenodd" clipRule="evenodd" d="M6.00584 8.08918C5.68001 8.41502 5.15334 8.41502 4.82751 8.08918L2.3275 5.58918C2.00167 5.26335 2.00167 4.73668 2.3275 4.41085L4.82751 1.91085C4.99001 1.74835 5.20334 1.66668 5.41667 1.66668C5.63 1.66668 5.84334 1.74835 6.00584 1.91085C6.33167 2.23668 6.33167 2.76335 6.00584 3.08918L4.92834 4.16668H14.925C16.575 4.16668 17.9167 5.48585 17.9167 7.10752V9.16668C17.9167 9.62751 17.5442 10 17.0833 10C16.6225 10 16.25 9.62751 16.25 9.16668V7.10752C16.25 6.40501 15.6558 5.83335 14.925 5.83335H4.92834L6.00584 6.91085C6.33167 7.23668 6.33167 7.76335 6.00584 8.08918ZM15.0715 15.8333H5.07485C3.42568 15.8333 2.08318 14.5142 2.08318 12.8925V10.8333C2.08318 10.3725 2.45568 10 2.91651 10C3.37735 10 3.74985 10.3725 3.74985 10.8333V12.8925C3.74985 13.5942 4.34401 14.1667 5.07485 14.1667H15.0715L13.994 13.0892C13.669 12.7633 13.669 12.2367 13.994 11.9108C14.1565 11.7483 14.3698 11.6667 14.5832 11.6667C14.7965 11.6667 15.0098 11.7483 15.1723 11.9108L17.6723 14.4108C17.9982 14.7367 17.9982 15.2633 17.6723 15.5892L15.1723 18.0892C14.8473 18.415 14.3198 18.415 13.994 18.0892C13.669 17.7633 13.669 17.2367 13.994 16.9108L15.0715 15.8333Z" fill="white"/>
        </mask>
        <g mask="url(#mask105)">
        <rect y="20" width="20" height="20" transform="rotate(-90 0 20)" fill="currentColor"/>
        </g>
    </svg>
);

export default Flip;