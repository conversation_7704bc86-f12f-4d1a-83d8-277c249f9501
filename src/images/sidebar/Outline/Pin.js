import React from 'react';

const Pin = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M9.99998 9.16624C9.31081 9.16624 8.74998 8.6054 8.74998 7.91624C8.74998 7.22707 9.31081 6.66624 9.99998 6.66624C10.6891 6.66624 11.25 7.22707 11.25 7.91624C11.25 8.6054 10.6891 9.16624 9.99998 9.16624ZM9.99998 4.99957C8.39165 4.99957 7.08331 6.3079 7.08331 7.91624C7.08331 9.52457 8.39165 10.8329 9.99998 10.8329C11.6083 10.8329 12.9166 9.52457 12.9166 7.91624C12.9166 6.3079 11.6083 4.99957 9.99998 4.99957ZM9.99998 16.3717C8.60415 15.0517 4.99998 11.3458 4.99998 8.26832C4.99998 5.54749 7.24248 3.33332 9.99998 3.33332C12.7575 3.33332 15 5.54749 15 8.26832C15 11.3458 11.3958 15.0517 9.99998 16.3717ZM9.99998 1.66666C6.32415 1.66666 3.33331 4.62749 3.33331 8.26832C3.33331 12.8308 9.20748 17.9175 9.45748 18.1317C9.61415 18.2658 9.80665 18.3333 9.99998 18.3333C10.1933 18.3333 10.3858 18.2658 10.5425 18.1317C10.7925 17.9175 16.6666 12.8308 16.6666 8.26832C16.6666 4.62749 13.6758 1.66666 9.99998 1.66666Z" fill="currentColor"/>
        <mask id="mask115" mask-type="alpha" maskUnits="userSpaceOnUse" x="3" y="1" width="14" height="18">
        <path fillRule="evenodd" clipRule="evenodd" d="M9.99998 9.16624C9.31081 9.16624 8.74998 8.6054 8.74998 7.91624C8.74998 7.22707 9.31081 6.66624 9.99998 6.66624C10.6891 6.66624 11.25 7.22707 11.25 7.91624C11.25 8.6054 10.6891 9.16624 9.99998 9.16624ZM9.99998 4.99957C8.39165 4.99957 7.08331 6.3079 7.08331 7.91624C7.08331 9.52457 8.39165 10.8329 9.99998 10.8329C11.6083 10.8329 12.9166 9.52457 12.9166 7.91624C12.9166 6.3079 11.6083 4.99957 9.99998 4.99957ZM9.99998 16.3717C8.60415 15.0517 4.99998 11.3458 4.99998 8.26832C4.99998 5.54749 7.24248 3.33332 9.99998 3.33332C12.7575 3.33332 15 5.54749 15 8.26832C15 11.3458 11.3958 15.0517 9.99998 16.3717ZM9.99998 1.66666C6.32415 1.66666 3.33331 4.62749 3.33331 8.26832C3.33331 12.8308 9.20748 17.9175 9.45748 18.1317C9.61415 18.2658 9.80665 18.3333 9.99998 18.3333C10.1933 18.3333 10.3858 18.2658 10.5425 18.1317C10.7925 17.9175 16.6666 12.8308 16.6666 8.26832C16.6666 4.62749 13.6758 1.66666 9.99998 1.66666Z" fill="white"/>
        </mask>
        <g mask="url(#mask115)">
        <rect width="20" height="20" fill="currentColor"/>
        </g>
    </svg>   
)


export default Pin