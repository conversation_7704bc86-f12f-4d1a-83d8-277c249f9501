import React from 'react';

const Keypad = () => (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M15.8334 16.6667C15.3725 16.6667 15 16.2942 15 15.8333C15 15.3725 15.3725 15 15.8334 15C16.2942 15 16.6667 15.3725 16.6667 15.8333C16.6667 16.2942 16.2942 16.6667 15.8334 16.6667ZM15.8334 13.3333C14.455 13.3333 13.3334 14.455 13.3334 15.8333C13.3334 17.2117 14.455 18.3333 15.8334 18.3333C17.2117 18.3333 18.3334 17.2117 18.3334 15.8333C18.3334 14.455 17.2117 13.3333 15.8334 13.3333ZM10 16.6667C9.53919 16.6667 9.16669 16.2942 9.16669 15.8333C9.16669 15.3725 9.53919 15 10 15C10.4609 15 10.8334 15.3725 10.8334 15.8333C10.8334 16.2942 10.4609 16.6667 10 16.6667ZM10 13.3333C8.62169 13.3333 7.50002 14.455 7.50002 15.8333C7.50002 17.2117 8.62169 18.3333 10 18.3333C11.3784 18.3333 12.5 17.2117 12.5 15.8333C12.5 14.455 11.3784 13.3333 10 13.3333ZM4.16669 16.6667C3.70585 16.6667 3.33335 16.2942 3.33335 15.8333C3.33335 15.3725 3.70585 15 4.16669 15C4.62752 15 5.00002 15.3725 5.00002 15.8333C5.00002 16.2942 4.62752 16.6667 4.16669 16.6667ZM4.16669 13.3333C2.78835 13.3333 1.66669 14.455 1.66669 15.8333C1.66669 17.2117 2.78835 18.3333 4.16669 18.3333C5.54502 18.3333 6.66669 17.2117 6.66669 15.8333C6.66669 14.455 5.54502 13.3333 4.16669 13.3333ZM15.8334 10.8333C15.3725 10.8333 15 10.4608 15 10C15 9.53917 15.3725 9.16667 15.8334 9.16667C16.2942 9.16667 16.6667 9.53917 16.6667 10C16.6667 10.4608 16.2942 10.8333 15.8334 10.8333ZM15.8334 7.5C14.455 7.5 13.3334 8.62167 13.3334 10C13.3334 11.3783 14.455 12.5 15.8334 12.5C17.2117 12.5 18.3334 11.3783 18.3334 10C18.3334 8.62167 17.2117 7.5 15.8334 7.5ZM10 10.8333C9.53919 10.8333 9.16669 10.4608 9.16669 10C9.16669 9.53917 9.53919 9.16667 10 9.16667C10.4609 9.16667 10.8334 9.53917 10.8334 10C10.8334 10.4608 10.4609 10.8333 10 10.8333ZM10 7.5C8.62169 7.5 7.50002 8.62167 7.50002 10C7.50002 11.3783 8.62169 12.5 10 12.5C11.3784 12.5 12.5 11.3783 12.5 10C12.5 8.62167 11.3784 7.5 10 7.5ZM4.16669 10.8333C3.70585 10.8333 3.33335 10.4608 3.33335 10C3.33335 9.53917 3.70585 9.16667 4.16669 9.16667C4.62752 9.16667 5.00002 9.53917 5.00002 10C5.00002 10.4608 4.62752 10.8333 4.16669 10.8333ZM4.16669 7.5C2.78835 7.5 1.66669 8.62167 1.66669 10C1.66669 11.3783 2.78835 12.5 4.16669 12.5C5.54502 12.5 6.66669 11.3783 6.66669 10C6.66669 8.62167 5.54502 7.5 4.16669 7.5ZM15.8334 3.33334C16.2942 3.33334 16.6667 3.70584 16.6667 4.16667C16.6667 4.62751 16.2942 5.00001 15.8334 5.00001C15.3725 5.00001 15 4.62751 15 4.16667C15 3.70584 15.3725 3.33334 15.8334 3.33334ZM15.8334 6.66667C17.2117 6.66667 18.3334 5.54501 18.3334 4.16667C18.3334 2.78834 17.2117 1.66667 15.8334 1.66667C14.455 1.66667 13.3334 2.78834 13.3334 4.16667C13.3334 5.54501 14.455 6.66667 15.8334 6.66667ZM10 5.00001C9.53919 5.00001 9.16669 4.62667 9.16669 4.16667C9.16669 3.70584 9.53919 3.33334 10 3.33334C10.4609 3.33334 10.8334 3.70584 10.8334 4.16667C10.8334 4.62667 10.4609 5.00001 10 5.00001ZM10 1.66667C8.62169 1.66667 7.50002 2.78834 7.50002 4.16667C7.50002 5.54501 8.62169 6.66667 10 6.66667C11.3784 6.66667 12.5 5.54501 12.5 4.16667C12.5 2.78834 11.3784 1.66667 10 1.66667ZM4.16669 5.00001C3.70585 5.00001 3.33335 4.62751 3.33335 4.16667C3.33335 3.70584 3.70585 3.33334 4.16669 3.33334C4.62752 3.33334 5.00002 3.70584 5.00002 4.16667C5.00002 4.62751 4.62752 5.00001 4.16669 5.00001ZM4.16669 1.66667C2.78835 1.66667 1.66669 2.78834 1.66669 4.16667C1.66669 5.54501 2.78835 6.66667 4.16669 6.66667C5.54502 6.66667 6.66669 5.54501 6.66669 4.16667C6.66669 2.78834 5.54502 1.66667 4.16669 1.66667Z" fill="currentColor"/>
        <mask id="mask108" mask-type="alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="18" height="18">
        <path fillRule="evenodd" clipRule="evenodd" d="M15.8334 16.6667C15.3725 16.6667 15 16.2942 15 15.8333C15 15.3725 15.3725 15 15.8334 15C16.2942 15 16.6667 15.3725 16.6667 15.8333C16.6667 16.2942 16.2942 16.6667 15.8334 16.6667ZM15.8334 13.3333C14.455 13.3333 13.3334 14.455 13.3334 15.8333C13.3334 17.2117 14.455 18.3333 15.8334 18.3333C17.2117 18.3333 18.3334 17.2117 18.3334 15.8333C18.3334 14.455 17.2117 13.3333 15.8334 13.3333ZM10 16.6667C9.53919 16.6667 9.16669 16.2942 9.16669 15.8333C9.16669 15.3725 9.53919 15 10 15C10.4609 15 10.8334 15.3725 10.8334 15.8333C10.8334 16.2942 10.4609 16.6667 10 16.6667ZM10 13.3333C8.62169 13.3333 7.50002 14.455 7.50002 15.8333C7.50002 17.2117 8.62169 18.3333 10 18.3333C11.3784 18.3333 12.5 17.2117 12.5 15.8333C12.5 14.455 11.3784 13.3333 10 13.3333ZM4.16669 16.6667C3.70585 16.6667 3.33335 16.2942 3.33335 15.8333C3.33335 15.3725 3.70585 15 4.16669 15C4.62752 15 5.00002 15.3725 5.00002 15.8333C5.00002 16.2942 4.62752 16.6667 4.16669 16.6667ZM4.16669 13.3333C2.78835 13.3333 1.66669 14.455 1.66669 15.8333C1.66669 17.2117 2.78835 18.3333 4.16669 18.3333C5.54502 18.3333 6.66669 17.2117 6.66669 15.8333C6.66669 14.455 5.54502 13.3333 4.16669 13.3333ZM15.8334 10.8333C15.3725 10.8333 15 10.4608 15 10C15 9.53917 15.3725 9.16667 15.8334 9.16667C16.2942 9.16667 16.6667 9.53917 16.6667 10C16.6667 10.4608 16.2942 10.8333 15.8334 10.8333ZM15.8334 7.5C14.455 7.5 13.3334 8.62167 13.3334 10C13.3334 11.3783 14.455 12.5 15.8334 12.5C17.2117 12.5 18.3334 11.3783 18.3334 10C18.3334 8.62167 17.2117 7.5 15.8334 7.5ZM10 10.8333C9.53919 10.8333 9.16669 10.4608 9.16669 10C9.16669 9.53917 9.53919 9.16667 10 9.16667C10.4609 9.16667 10.8334 9.53917 10.8334 10C10.8334 10.4608 10.4609 10.8333 10 10.8333ZM10 7.5C8.62169 7.5 7.50002 8.62167 7.50002 10C7.50002 11.3783 8.62169 12.5 10 12.5C11.3784 12.5 12.5 11.3783 12.5 10C12.5 8.62167 11.3784 7.5 10 7.5ZM4.16669 10.8333C3.70585 10.8333 3.33335 10.4608 3.33335 10C3.33335 9.53917 3.70585 9.16667 4.16669 9.16667C4.62752 9.16667 5.00002 9.53917 5.00002 10C5.00002 10.4608 4.62752 10.8333 4.16669 10.8333ZM4.16669 7.5C2.78835 7.5 1.66669 8.62167 1.66669 10C1.66669 11.3783 2.78835 12.5 4.16669 12.5C5.54502 12.5 6.66669 11.3783 6.66669 10C6.66669 8.62167 5.54502 7.5 4.16669 7.5ZM15.8334 3.33334C16.2942 3.33334 16.6667 3.70584 16.6667 4.16667C16.6667 4.62751 16.2942 5.00001 15.8334 5.00001C15.3725 5.00001 15 4.62751 15 4.16667C15 3.70584 15.3725 3.33334 15.8334 3.33334ZM15.8334 6.66667C17.2117 6.66667 18.3334 5.54501 18.3334 4.16667C18.3334 2.78834 17.2117 1.66667 15.8334 1.66667C14.455 1.66667 13.3334 2.78834 13.3334 4.16667C13.3334 5.54501 14.455 6.66667 15.8334 6.66667ZM10 5.00001C9.53919 5.00001 9.16669 4.62667 9.16669 4.16667C9.16669 3.70584 9.53919 3.33334 10 3.33334C10.4609 3.33334 10.8334 3.70584 10.8334 4.16667C10.8334 4.62667 10.4609 5.00001 10 5.00001ZM10 1.66667C8.62169 1.66667 7.50002 2.78834 7.50002 4.16667C7.50002 5.54501 8.62169 6.66667 10 6.66667C11.3784 6.66667 12.5 5.54501 12.5 4.16667C12.5 2.78834 11.3784 1.66667 10 1.66667ZM4.16669 5.00001C3.70585 5.00001 3.33335 4.62751 3.33335 4.16667C3.33335 3.70584 3.70585 3.33334 4.16669 3.33334C4.62752 3.33334 5.00002 3.70584 5.00002 4.16667C5.00002 4.62751 4.62752 5.00001 4.16669 5.00001ZM4.16669 1.66667C2.78835 1.66667 1.66669 2.78834 1.66669 4.16667C1.66669 5.54501 2.78835 6.66667 4.16669 6.66667C5.54502 6.66667 6.66669 5.54501 6.66669 4.16667C6.66669 2.78834 5.54502 1.66667 4.16669 1.66667Z" fill="white"/>
        </mask>
        <g mask="url(#mask108)">
        <rect width="20" height="20" fill="currentColor"/>
        </g>
    </svg>
)

export default Keypad