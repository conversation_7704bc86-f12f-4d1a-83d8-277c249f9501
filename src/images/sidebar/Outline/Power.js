import React from 'react'

const Power = () => (
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fillRule="evenodd" clipRule="evenodd" d="M10.8333 10C10.8333 10.4608 10.46 10.8333 9.99998 10.8333C9.53998 10.8333 9.16665 10.4608 9.16665 10V1.66666C9.16665 1.20583 9.53998 0.833328 9.99998 0.833328C10.46 0.833328 10.8333 1.20583 10.8333 1.66666V10ZM12.6991 2.95108C12.9099 2.54191 13.4132 2.38274 13.8216 2.59274C16.6041 4.03191 18.3332 6.86941 18.3332 10.0002C18.3332 14.5944 14.5949 18.3336 9.9999 18.3336C5.4049 18.3336 1.66656 14.5944 1.66656 10.0002C1.66656 6.86941 3.39573 4.03191 6.17907 2.59274C6.58657 2.38191 7.0899 2.54108 7.30157 2.95108C7.51323 3.35941 7.3524 3.86191 6.94407 4.07358C4.71657 5.22441 3.33323 7.49524 3.33323 10.0002C3.33323 13.6761 6.32407 16.6669 9.9999 16.6669C13.6757 16.6669 16.6666 13.6761 16.6666 10.0002C16.6666 7.49524 15.2832 5.22441 13.0566 4.07358C12.6474 3.86191 12.4874 3.35941 12.6991 2.95108Z" fill="currentColor"/>
<mask id="mask125" mask-type="alpha" maskUnits="userSpaceOnUse" x="1" y="0" width="18" height="19">
<path fillRule="evenodd" clipRule="evenodd" d="M10.8333 10C10.8333 10.4608 10.46 10.8333 9.99998 10.8333C9.53998 10.8333 9.16665 10.4608 9.16665 10V1.66666C9.16665 1.20583 9.53998 0.833328 9.99998 0.833328C10.46 0.833328 10.8333 1.20583 10.8333 1.66666V10ZM12.6991 2.95108C12.9099 2.54191 13.4132 2.38274 13.8216 2.59274C16.6041 4.03191 18.3332 6.86941 18.3332 10.0002C18.3332 14.5944 14.5949 18.3336 9.9999 18.3336C5.4049 18.3336 1.66656 14.5944 1.66656 10.0002C1.66656 6.86941 3.39573 4.03191 6.17907 2.59274C6.58657 2.38191 7.0899 2.54108 7.30157 2.95108C7.51323 3.35941 7.3524 3.86191 6.94407 4.07358C4.71657 5.22441 3.33323 7.49524 3.33323 10.0002C3.33323 13.6761 6.32407 16.6669 9.9999 16.6669C13.6757 16.6669 16.6666 13.6761 16.6666 10.0002C16.6666 7.49524 15.2832 5.22441 13.0566 4.07358C12.6474 3.86191 12.4874 3.35941 12.6991 2.95108Z" fill="white"/>
</mask>
<g mask="url(#mask125)">
<rect width="20" height="20" fill="currentColor"/>
</g>
</svg>
)

export default Power