const hostApi = "https://server.delightdiamonds.com";

// const hostApi = "http://************/delight-diamond-laravel/public";
const baseURLApi = `${hostApi}/admin/`;
const vendorUrl = `${hostApi}/vendor/`;
const merchantUrl = `${hostApi}/api/merchant/`;

const redirectUrl = process.env.NODE_ENV === "development" ? "http://localhost:3000/delight-diamonds-admin" : "https://server.delightdiamonds.com/delight-diamonds-admin";
export default {
  redirectUrl,
  hostApi,
  baseURLApi,
  vendorUrl,
  merchantUrl,
  remote: "",
  isBackend: process.env.REACT_APP_BACKEND,
  auth: {
    email: '<EMAIL>',
    password: 'password'
  },
  app: {
    sidebarColors: {
      first: '#3D3D3D',
      second: '#4B505F',
      third: '#483CB6',
      fourth: '#EFF2F5',
      fifth: '#20AE8C'
    },
    navbarColors: {
      first: '#ffffff',
      second: '#E2E7EC',
      third: '#C9D1FB',
      fourth: '#C1C3CF',
      fifth: '#0C2236',
      sixth: '#6FB0F9'
    },
    colors: {
      dark: "#002B49",
      light: "#FFFFFF",
      sea: "#004472",
      sky: "#E9EBEF",
      wave: "#D1E7F6",
      rain: "#CCDDE9",
      middle: "#D7DFE6",
      black: "#13191D",
      salad: "#21AE8C",
      seaWave: "#483CB6",
      grad: "#4B505F",
      blueSky: "#EFF2F5"
    },
    themeColors: {
      primary: "#AEA158",
      secondary: "#798892",
      success: "#26CD5F",
      info: "#10CFD0",
      warning: "#AEA158",
      danger: "#FF5574",
      inverse: "#30324C",
      default: "#AEA158"
    },
  }
};