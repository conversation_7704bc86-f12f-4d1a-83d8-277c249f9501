
// import * as dataFormat from 'components/Users/<USER>/UsersDataFormatters';
import React, { useEffect, useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Dropdown, DropdownMenu, DropdownToggle, Dropdown<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalFooter, } from 'reactstrap';
import { BootstrapTable, TableHeaderColumn, SearchField } from 'react-bootstrap-table';
import { Link, useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';
import { VendorCountryList, changeVendorListStatus, deleteVendorList, getVendorList } from '../../Services/actions/vendorListAction';
import { RESET_ADD_VENDOR_LIST, RESET_DELETE_VENDOR_LIST, RESET_UPDATE_VENDOR_LIST, RESET_VENDOR_LIST_STATUS } from '../../Services/Constant';
import Loader from '../Loader/Loader';

const VendorListTable = (props) => {

    const history = useHistory()

    const [state, setState] = useState([])
    const [loading, setLoading] = useState(true)

    const [details, setDetails] = useState(null)

    const [isDelete, setIsDelete] = useState(false)
    const [isDeleteId, setIsDeleteId] = useState(null)

    useEffect(() => {
        props.VendorCountryList()
        setLoading(true)
    }, [])




    useEffect(() => {
        if (props.vendorCountryListReducer.success) {
            const data = props.vendorCountryListReducer.data
            setDetails({ ...data })
            props.getVendorList()

        }
    }, [props.vendorCountryListReducer])



    useEffect(() => {
        if (props.VendorListReducer.success) {
            const data = props.VendorListReducer.data
            const _data = data.map(e => {
                return {
                    ...e,
                    business_type: JSON.parse(e.business_type),
                    buying_group: JSON.parse(e.buying_group)
                }
            })
            const list = _data.map((e, i) => {
                return {
                    srNo: i + 1,
                    username: e.username,
                    name: `${e.first_name} ${e.last_name}`,
                    phoneNumber: !!e.mobile && e.mobile !== '' ? e.mobile : 'N/A',
                    other_phone: !!e.other_phone && e.other_phone !== '' ? e.other_phone : 'N/A',
                    email: e.email,
                    city: e.city,
                    company: e.company,
                    country: !!details && details.country.find(country => country.id === e.country_id).name,
                    group_title: e.group_title,
                    job_title: e.job_title,
                    pincode: e.pincode,
                    state: e.state,
                    website: e.website,
                    business_type: e.business_type.map(type => !!details && details.BusinessType[parseInt(type)]),
                    buying_group: e.buying_group.map(type => !!details && details.ByuingGroup[parseInt(type)]),
                    Status: e.status,
                    address: e.address,
                    // about: e.about,
                    id: e.id,
                    username: e.username
                }
            })
            setState([...list])
        }
        setLoading(false)

    }, [props.VendorListReducer])

    const handleDelete = () => {
        props.deleteVendorList(isDeleteId)
    }

    useEffect(() => {
        if (props.deleteVendorListReducer.success) {
            setIsDelete(false)
            setIsDeleteId(null)
            props.getVendorList()
            props.resetDeleteList()
            toast.success("Vendor deleted successfully")
        }
    }, [props.deleteVendorListReducer])



    const statusChange = (cell, value) => {
        return (
            <div className={`d-flex justify-content-between`}>
                <Button
                    color={value.Status === 1 ? 'default' : 'secondary'}
                    className="btn btn-rounded-f" type='button'
                    onClick={() => handleStatus(value.Status, value.id)}
                >
                    {cell === 1 ? 'Active' : 'Deactive'}
                </Button>
            </div>
        )
    }

    const handleStatus = async (status, id) => {
        const details = {
            status: status === 1 ? 0 : 1,
        };
        await props.changeVendorListStatus({
            details: details,
            id: id
        })
        const list = [...state]
        const findItemIndex = state.findIndex(e => e.id === id)
        list[findItemIndex]['Status'] = list[findItemIndex]['Status'] === 1 ? 0 : 1

        setState([...list])



    }

    const websiteUrl = (cell, value) => {
        return (
            <div onClick={() => {
                window.open(cell, '_blank')
            }} style={{
                cursor: 'pointer',
                color: 'blue',
                textDecoration: 'underline'
            }}>{cell}</div>
        )
    }
    useEffect(() => {
        if (props.statusVendorListReducer.success) {
            // props.getVendorList()
            props.resetStatus()
        }

    }, [props.statusVendorListReducer])

    const actionFormatter = (cell, value) => {
        return (
            <div className={`d-flex justify-content-center`}>
                <Button
                    className="btn-action me-2"
                    onClick={() => history.push(`/vendor/${value.id}`)}
                >
                    <i className="fa-regular fa-eye"></i>
                </Button>
                <Button
                    className="btn-action me-2"
                    onClick={() => history.push(`/vendor/${value.id}/update`)}
                >
                    <i className="fa-regular fa-pencil-square-o"></i>
                </Button>
                <Button
                    className="btn-action"
                    onClick={() => {
                        setIsDelete(true)
                        setIsDeleteId(value.id)
                    }}
                >
                    <i className="fa-regular fa-trash-can"></i>
                </Button>
            </div>
        )
    }


    const renderSizePerPageDropDown = (props) => {
        const limits = [];
        props.sizePerPageList.forEach((limit) => {
            limits.push(<DropdownItem key={limit} onClick={() => props.changeSizePerPage(limit)}>{limit}</DropdownItem>);
        });
        return (
            <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
                <DropdownToggle color="default" caret>
                    {props.currSizePerPage}
                </DropdownToggle>
                <DropdownMenu>
                    {limits}
                </DropdownMenu>
            </Dropdown>
        );
    };

    const createCustomSearchField = (props) => {
        return (
            <SearchField
                className="mb-sm-5 me-1"
                placeholder='Search'
            />
        );
    }

    const options = {
        sizePerPage: 10,
        paginationSize: 5,
        searchField: createCustomSearchField,
        sizePerPageDropDown: renderSizePerPageDropDown,
    };


    useEffect(() => {
        if (props.addVendorListReducer.success) {
            props.getVendorList()
            props.resetAddVendorList()
        }
    }, [props.addVendorListReducer])

    useEffect(() => {
        if (props.updateVendorListReducer.success) {
            props.getVendorList()
            props.resetUpdateVendorList()
        }
    }, [props.updateVendorListReducer])


    return (
        <>
            {loading ? <Loader /> : <><div className="text-end mb-4">
                <Link to="/vendor/add" className="btn btn-primary">
                    Add Vendor
                </Link>
            </div>
                <div className='card main-card'>
                    <div className='card-body'>
                        <BootstrapTable bordered={false} data={state} version="4" pagination options={options} search tableContainerClass={`coustom-table stone-table table-striped table-hover`}
                        >
                            <TableHeaderColumn dataField="srNo" width='55px'>
                                <span className="fs-sm">Sr No.</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="username" width='150px' dataSort>
                                <span className="fs-sm">Username</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="name" width='150px' dataSort>
                                <span className="fs-sm">Name</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="phoneNumber" width='105px'>
                                <span className="fs-sm">Phone Number</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="other_phone" width='105px'>
                                <span className="fs-sm">Other Number</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="email" width='230px'>
                                <span className="fs-sm">E-mail</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="company" width='200px'>
                                <span className="fs-sm">Company</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="website" width='250px' dataFormat={websiteUrl.bind(this)}>
                                <span className="fs-sm">Website</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="job_title" width='200px'>
                                <span className="fs-sm">Job Title</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="group_title" width='200px'>
                                <span className="fs-sm">Group Title</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="country" width='150px'>
                                <span className="fs-sm">Country</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="state" width='150px'>
                                <span className="fs-sm">State</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="city" width='150px'>
                                <span className="fs-sm">City</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="pincode" >
                                <span className="fs-sm">Pincode</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="business_type" width='150px'>
                                <span className="fs-sm">Bussiness Type</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="buying_group" width='110px'>
                                <span className="fs-sm">Buying Group</span>
                            </TableHeaderColumn>

                            {/* <TableHeaderColumn dataField="business_type">
       <span className="fs-sm">Bussiness Type</span>
      </TableHeaderColumn> */}

                            <TableHeaderColumn dataField="Status" width='100px' dataFormat={statusChange.bind(this)}>
                                <span className="fs-sm">Status</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn isKey dataField="id" width='120px' dataFormat={actionFormatter.bind(this)}>
                                <span className="fs-sm text-center d-block">Actions</span>
                            </TableHeaderColumn>
                        </BootstrapTable>
                    </div>
                </div>
            </>}
            <Modal size="sm" isOpen={isDelete} toggle={() => setIsDelete(false)}>
                <ModalHeader toggle={() => {
                    setIsDelete(false)
                    setIsDeleteId(null)
                }}>Confirm delete</ModalHeader>
                <ModalBody className="bg-white">
                    Are you sure you want to delete this vendor?
                </ModalBody>
                <ModalFooter>
                    <Button color="secondary" onClick={() => {
                        setIsDelete(false)
                        setIsDeleteId(null)
                    }}>Cancel</Button>
                    <Button color="primary" onClick={handleDelete}>Delete</Button>
                </ModalFooter>
            </Modal>
        </>
    );

}

const mapStateToProp = (state) => ({
    VendorListReducer: state.vendorListReducer.VendorList,
    vendorCountryListReducer: state.vendorListReducer.vendorCountryList,
    deleteVendorListReducer: state.vendorListReducer.deleteVendorList,
    statusVendorListReducer: state.vendorListReducer.statusVendorList,
    addVendorListReducer: state.vendorListReducer.addVendorList,
    updateVendorListReducer: state.vendorListReducer.updateVendorList,
});

const mapDispatchToProps = (dispatch) => ({
    getVendorList: () => dispatch(getVendorList()),
    VendorCountryList: () => dispatch(VendorCountryList()),
    deleteVendorList: (id) => dispatch(deleteVendorList(id)),
    changeVendorListStatus: (details) => dispatch(changeVendorListStatus(details)),
    resetDeleteList: () => dispatch({ type: RESET_DELETE_VENDOR_LIST }),
    resetStatus: () => dispatch({ type: RESET_VENDOR_LIST_STATUS }),
    resetAddVendorList: () => dispatch({ type: RESET_ADD_VENDOR_LIST }),
    resetUpdateVendorList: () => dispatch({ type: RESET_UPDATE_VENDOR_LIST }),
});

export default connect(mapStateToProp, mapDispatchToProps)(VendorListTable);
