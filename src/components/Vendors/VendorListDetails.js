import React, { useEffect, useState } from 'react'
import { Col, Row } from 'react-bootstrap';
import { connect } from 'react-redux';
import { Link, useHistory, useParams } from 'react-router-dom'
import { VendorCountryList, getVEndorDiamondList, getVendorList } from '../../Services/actions/vendorListAction';
import { BootstrapTable, SearchField, TableHeaderColumn } from 'react-bootstrap-table';
import { Dropdown, DropdownItem, DropdownMenu, DropdownToggle } from 'reactstrap';

function VendorListDetails(props) {
 const history = useHistory();
 const params = useParams();


 const [loading, setLoading] = useState(true)
 const [vendorDetails, setVendorDetails] = useState(null)
 const [details, setDetails] = useState(null)
 const [diamondDetails, setDiamondDetails] = useState([])

 useEffect(() => {
  props.VendorCountryList()
  setLoading(true)
 }, [])




 useEffect(() => {
  if (props.vendorCountryListReducer.success) {
   const data = props.vendorCountryListReducer.data
   setDetails({ ...data })
   if (!props.VendorListReducer.success) {
    props.getVendorList()
   }
  }
 }, [props.vendorCountryListReducer])

 useEffect(() => {
  if (!!params.id) {
   props.getVEndorDiamondList({id: params.id, skip: 0})
  }
 }, [params.id])

 useEffect(() => {
  if (props.VendorDiamondListReducer.success) {
   const data = props.VendorDiamondListReducer.data
   const _data = data.map((e, i) => {
    return {
     srNo: i + 1,
     stone_id: !!e.stone_id && e.stone_id !== '' ? e.stone_id : 'N/A',
     cert_no: !!e.cert_no && e.cert_no !== '' ? e.cert_no : 'N/A',
     cert_type: !!e.cert_type && e.cert_type !== '' ? e.cert_type : 'N/A',
     diamond_type_name: !!e.diamond_type_name && e.diamond_type_name !== '' ? e.diamond_type_name : 'N/A',
     shape_name: !!e.shape_name && e.shape_name !== '' ? e.shape_name : 'N/A',
     carat: !!e.carat && e.carat !== '' ? e.carat : 'N/A',
     color_name: !!e.color_name && e.color_name !== '' ? e.color_name : 'N/A',
     colors_name: !!e.colors_name && e.colors_name !== '' ? e.colors_name : 'N/A',
     overtone_name: !!e.overtone_name && e.overtone_name !== '' ? e.overtone_name : 'N/A',
     intensity_name: !!e.intensity_name && e.intensity_name !== '' ? e.intensity_name : 'N/A',
     clarity_name: !!e.clarity_name && e.clarity_name !== '' ? e.clarity_name : 'N/A',
     cut_name: !!e.cut_name && e.cut_name !== '' ? e.cut_name : 'N/A',
     polish_name: !!e.polish_name && e.polish_name !== '' ? e.polish_name : 'N/A',
     symmetry_name: !!e.symmetry_name && e.symmetry_name !== '' ? e.symmetry_name : 'N/A',
     rate: !!e.rate && e.rate !== '' ? e.rate : 'N/A',
     amount: !!e.amount && e.amount !== '' ? e.amount : 'N/A',
     ratio: !!e.ratio && e.ratio !== '' ? e.ratio : 'N/A',
     pair: !!e.pair && e.pair !== '' ? e.pair : 'N/A',
     growth_type: !!e.growth_type && e.growth_type !== '' ? e.growth_type : 'N/A'
    }
   })
   setDiamondDetails(_data)
  }
 }, [props.VendorDiamondListReducer])


 useEffect(() => {
  if (props.VendorListReducer.success) {
   setLoading(false)
   if (!!params.id) {
    const data = props.VendorListReducer.data;
    const _data = data.map(e => {
     return {
      ...e,
      business_type: JSON.parse(e.business_type),
      buying_group: JSON.parse(e.buying_group)
     }
    })
    const __details = _data.find((e) => {
     return parseInt(e.id) === parseInt(params.id)
    });

    if (!!__details) {
     __details['business_type'] = __details.business_type.map(e => details?.BusinessType[parseInt(e)]).join(', ')
     __details['buying_group'] = __details.buying_group.map(e => details?.ByuingGroup[parseInt(e)]).join(', ')
     __details['country_id'] = details?.country.find(country => country.id === __details.country_id).name
     setVendorDetails(__details)
    } else {
     history.goBack()
    }
   } else {
    history.goBack()
   }
  }
 }, [props.VendorListReducer, params.id])


 const renderSizePerPageDropDown = (props) => {
  const limits = [];
  props.sizePerPageList.forEach((limit) => {
   limits.push(<DropdownItem key={limit} onClick={() => props.changeSizePerPage(limit)}>{limit}</DropdownItem>);
  });
  return (
   <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
    <DropdownToggle color="default" caret>
     {props.currSizePerPage}
    </DropdownToggle>
    <DropdownMenu>
     {limits}
    </DropdownMenu>
   </Dropdown>
  );
 };

 const createCustomSearchField = (props) => {
  return (
   <SearchField
    className="mb-sm-5 me-1"
    placeholder='Search'
   />
  );
 }

 const options = {
  sizePerPage: 10,
  paginationSize: 5,
  searchField: createCustomSearchField,
  sizePerPageDropDown: renderSizePerPageDropDown,
 };


 return (
  <>
   <div className="page-top-line">
    <h2 className="page-title">Vendor Details</h2>
   </div>
   <div className="card main-card">
    {loading && !vendorDetails ? <>Loading...</> :
     <div className="card-body">
      <h4 className="mb-5 fw-light">Vendor Details</h4>

      <Row className="col-lg-9 g-3">
       <div className="mb-4">
        <div>
         <b className="fw-bold">Username : </b>
         <span
         // style={{
         //  cursor: "pointer",
         //  textDecoration: "underline",
         // }}
         >
          {vendorDetails?.username !== "" ? vendorDetails?.username : "N/A"}
         </span>
        </div>
       </div>
       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">First Name  : </b>
         {vendorDetails?.first_name !== "" ? vendorDetails?.first_name : "N/A"}
        </div>
       </Col>
       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">Last Name : </b>
         {vendorDetails?.last_name !== "" ? vendorDetails?.last_name : "N/A"}
        </div>
       </Col>


       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">Phone Number  : </b>
         {vendorDetails?.mobile !== "" ? vendorDetails?.mobile : "N/A"}
        </div>
       </Col>
       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">Other Number : </b>
         {vendorDetails?.other_phone !== "" && !!vendorDetails?.other_phone ? vendorDetails?.other_phone : "N/A"}
        </div>
       </Col>


       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">Email  : </b>
         {vendorDetails?.email !== "" ? vendorDetails?.email : "N/A"}
        </div>
       </Col>
       <Col lg={6} md={6} className="mb-3">
        <div >
         <b className="fw-bold">Website : </b>
         <span onClick={() => { window.open(vendorDetails?.website, "_blank") }} style={{
          cursor: 'pointer',
          color: 'blue',
          textDecoration: 'underline'
         }}>
          {vendorDetails?.website !== "" ? vendorDetails?.website : "N/A"}
         </span>
        </div>
       </Col>


       <Col lg={12} md={12} className="mb-3">
        <div>
         <b className="fw-bold">Company  : </b>
         {vendorDetails?.company !== "" ? vendorDetails?.company : "N/A"}
        </div>
       </Col>
       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">Bussiness Type : </b>
         {vendorDetails?.business_type !== "" ? vendorDetails?.business_type : "N/A"}
        </div>
       </Col>
       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">Buying Group : </b>
         {vendorDetails?.buying_group !== "" ? vendorDetails?.buying_group : "N/A"}
        </div>
       </Col>

       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">Job Title  : </b>
         {vendorDetails?.job_title !== "" ? vendorDetails?.job_title : "N/A"}
        </div>
       </Col>
       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">Group Title : </b>
         {vendorDetails?.group_title !== "" ? vendorDetails?.group_title : "N/A"}
        </div>
       </Col>


       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">Country  : </b>
         {vendorDetails?.country_id !== "" ? vendorDetails?.country_id : "N/A"}
        </div>
       </Col>
       <Col lg={6} md={6} className="mb-3">
        <div>
         <b className="fw-bold">State : </b>
         {vendorDetails?.state !== "" ? vendorDetails?.state : "N/A"}
        </div>
       </Col>

       <Col lg={5} md={5} className="mb-3">
        <div>
         <b className="fw-bold">Address  : </b>
         {vendorDetails?.address !== "" ? vendorDetails?.address : "N/A"}
        </div>
       </Col>
       <Col lg={4} md={4} className="mb-3">
        <div>
         <b className="fw-bold">City : </b>
         {vendorDetails?.city !== "" ? vendorDetails?.city : "N/A"}
        </div>
       </Col>
       <Col lg={3} md={3} className="mb-3">
        <div>
         <b className="fw-bold">Pincode : </b>
         {vendorDetails?.pincode !== "" ? vendorDetails?.pincode : "N/A"}
        </div>
       </Col>

       <Col lg={12} md={12} className="mb-3">
        <div>
         <b className="fw-bold">About  : </b>
         {vendorDetails?.about !== "" ? vendorDetails?.about : "N/A"}
        </div>
       </Col>
      </Row>
     </div>}

   </div>
   <div className="page-top-line mt-3">
    {/* <h2 className="page-title">Vendor Diamond Details</h2> */}
   </div>
   <div className="card main-card">
    <div className="card-body">


     <div className='mt-3'>
      <h4 className="mb-5 fw-light">Vendor Diamond Details</h4>
      <div>
       <BootstrapTable bordered={false} data={diamondDetails} version="4" pagination options={options} search tableContainerClass={`coustom-table stone-table table-striped table-hover`}
       >
        <TableHeaderColumn dataField="srNo" width='55px' isKey={true}>
         <span className="fs-sm">Sr No.</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="stone_id" width='150px' dataSort>
         <span className="fs-sm">Stone ID</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="cert_no" width='150px' dataSort>
         <span className="fs-sm">Certificate No</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="cert_type" width='105px'>
         <span className="fs-sm">Certificate Type</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="diamond_type_name" width='105px'>
         <span className="fs-sm">Diamond Type</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="shape_name" width='230px'>
         <span className="fs-sm">Shape</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="carat" width='200px'>
         <span className="fs-sm">Carat</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="color_name" width='200px'>
         <span className="fs-sm">Color</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="colors_name" width='200px'>
         <span className="fs-sm">Fancy Color</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="overtone_name" width='200px'>
         <span className="fs-sm">Intensity</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="intensity_name" width='200px'>
         <span className="fs-sm">Overtone</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="clarity_name" width='250px'>
         <span className="fs-sm">Clarity</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="cut_name" width='200px'>
         <span className="fs-sm">Cut</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="polish_name" width='200px'>
         <span className="fs-sm">Polish</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="symmetry_name" width='150px'>
         <span className="fs-sm">Symmetry</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="rate" width='150px'>
         <span className="fs-sm">Rate</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="amount" width='150px'>
         <span className="fs-sm">Amount</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="ratio" width='150px' >
         <span className="fs-sm">Ratio</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="pair" width='150px'>
         <span className="fs-sm">Pair</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="growth_type" width='110px'>
         <span className="fs-sm">Growth Type</span>
        </TableHeaderColumn>

       </BootstrapTable>
      </div>
     </div>
    </div>
   </div>
  </>
 )
}


const mapStateToProp = (state) => ({
 vendorCountryListReducer: state.vendorListReducer.vendorCountryList,
 addVendorListReducer: state.vendorListReducer.addVendorList,
 updateVendorListReducer: state.vendorListReducer.updateVendorList,
 VendorListReducer: state.vendorListReducer.VendorList,
 VendorDiamondListReducer: state.vendorListReducer.VendorDiamondList,
});

const mapDispatchToProps = (dispatch) => ({
 // VendorCountryList: (details) => dispatch(VendorCountryList(details)),
 getVendorList: () => dispatch(getVendorList()),
 VendorCountryList: () => dispatch(VendorCountryList()),
 getVEndorDiamondList: (details) => dispatch(getVEndorDiamondList(details)),
 // addVendorList: (details) => dispatch(addVendorList(details)),
 // updateVendorList: (details) => dispatch(updateVendorList(details)),
 // resetaddVendorList: (details) => dispatch({ type: RESET_ADD_VENDOR_LIST }),
 // resetupdateVendorList: (details) => dispatch({ type: RESET_UPDATE_VENDOR_LIST }),
});

export default connect(mapStateToProp, mapDispatchToProps)(VendorListDetails);

