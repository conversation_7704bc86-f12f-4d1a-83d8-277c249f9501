
import React, { useEffect, useState } from 'react'
import { connect } from 'react-redux';
import { useHistory, useParams } from 'react-router';
import { toast } from 'react-toastify';
import { VendorCountryList, addVendorList, getVendorList, updateVendorList } from '../../Services/actions/vendorListAction';
import { RESET_ADD_VENDOR_LIST } from '../../Services/Constant';
import { RESET_UPDATE_VENDOR_LIST } from '../../Services/Constant';
import { MultiSelect } from 'react-multi-select-component';
import Loader from '../Loader/Loader';

function AddVendor(props) {
  const history = useHistory();
  const params = useParams();
  const [bussinessTypeList, setBussinessTypeList] = useState([]);
  const [buyingGroupList, setBuyingGroupList] = useState([]);
  const [loading, setLoading] = useState(true)
  const [showPass, setShowPass] = useState(false)
  const [conShowPass, setConShowPass] = useState(false)
  const [countryList, setCountryList] = useState({})
  const initialState = {
    username: "",
    email: "",
    password: "",
    conPassword: "",
    first_name: "",
    last_name: "",
    company: "",
    website: "",
    other_phone: "",
    mobile: "",
    about: "",
    job_title: "",
    business_type: [],
    buying_group: [],
    group_title: "",
    address: "",
    city: "",
    state: "",
    pincode: "",
    country_id: "",
  }

  const initialError = {
    username: false,
    email: false,
    password: false,
    conPassword: false,
    other_phone: false,
    mobile: false,
    first_name: false,
    last_name: false,
    company: false,
    website: false,
    about: false,
    job_title: false,
    business_type: false,
    buying_group: false,
    group_title: false,
    address: false,
    city: false,
    state: false,
    pincode: false,
    country_id: false,
  }

  const [state, setState] = useState(initialState)

  const [error, setError] = useState(initialError)

  useEffect(() => {
    // debugger
    const data = props.VendorListReducer.data;
    if (!!params.id && props.VendorListReducer.success) {
      const _data = data.map(e => {
        return {
          ...e,
          business_type: JSON.parse(e.business_type),
          buying_group: JSON.parse(e.buying_group)
        }
      })
      const details = _data.find((e) => {
        return parseInt(e.id) === parseInt(params.id)
      });


      setState((prevState) => ({
        ...prevState,
        username: details.username,
        email: details.email,
        // password: details.,
        // conPassword: details.,
        first_name: details.first_name,
        last_name: details.last_name,
        company: details.company,
        website: details.website,
        other_phone: details.other_phone,
        mobile: details.mobile,
        about: details.about,
        job_title: details.job_title,
        business_type: bussinessTypeList.filter(e => details.business_type.includes(e.value)),
        buying_group: buyingGroupList.filter(e => details.buying_group.includes(e.value)),
        group_title: details.group_title,
        address: details.address,
        city: details.city,
        state: details.state,
        pincode: details.pincode,
        country_id: details.country_id,
      }));
      setLoading(false)
    } else {
      setLoading(false)
    }
  }, [params, props.VendorListReducer]);

  const handleInput = (e) => {
    const { name, value } = e.target
    const validEmailRegex = RegExp(
      /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i
    );
    const validUrlExpression = new RegExp(
      /(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~# &=]*)/g
    );
    const contactValidation = RegExp(/^(?!0+\s+,?$)\d{10}\s*,?$/);

    if (name === 'mobile' || name === 'other_phone') {
      if (RegExp(/^([0-9]{0,10})$/i).test(value)) {
        setState((prevState) => ({
          ...prevState,
          [name]: value,
        }));
      } else return;

    } else if (name === 'pincode') {
      if (RegExp(/^([0-9]{0,6})$/i).test(value)) {
        setState((prevState) => ({
          ...prevState,
          [name]: value,
        }));
      } else return;

    } else {
      setState(prevState => ({
        ...prevState,
        [name]: value
      }))
    }

    switch (name) {
      case "username":
        if (value === "") {
          error["username"] = "Required";
        } else {
          error["username"] = false;
        }
        break;

      case "email":
        if (value === "") {
          error["email"] = "Required";
        } else if (!validEmailRegex.test(value)) {
          error["email"] = "Invalid Email";
        } else {
          error["email"] = false;
        }
        break;

      case "password":
        if (value === "") {
          error["password"] = "Required";
        } else {
          error["password"] = false;
        }
        break;

      case "conPassword":
        if (value === "") {
          error["conPassword"] = "Required";
        } else if (value !== state.password) {
          error["conPassword"] = "Confirm password not matched";
        } else {
          error["conPassword"] = false;
        }
        break;

      case "first_name":
        if (value === "") {
          error["first_name"] = "Required";
        } else {
          error["first_name"] = false;
        }
        break;

      case "last_name":
        if (value === "") {
          error["last_name"] = "Required";
        } else {
          error["last_name"] = false;
        }
        break;

      case "company":
        if (value === "") {
          error["company"] = "Required";
        } else {
          error["company"] = false;
        }
        break;

      case "website":
        if (value === "") {
          error["website"] = "Required";
        } else if (!validUrlExpression.test(value)) {
          error["website"] = "Invalid website URL";
        } else {
          error["website"] = false;
        }
        break;

      case "about":
        if (value === "") {
          error["about"] = "Required";
        } else {
          error["about"] = false;
        }
        break;

      case "job_title":
        if (value === "") {
          error["job_title"] = "Required";
        } else {
          error["job_title"] = false;
        }
        break;

      case "group_title":
        if (value === "") {
          error["group_title"] = "Required";
        } else {
          error["group_title"] = false;
        }
        break;

      case "address":
        if (value === "") {
          error["address"] = "Required";
        } else {
          error["address"] = false;
        }
        break;

      case "city":
        if (value === "") {
          error["city"] = "Required";
        } else {
          error["city"] = false;
        }
        break;

      case "state":
        if (value === "") {
          error["state"] = "Required";
        } else {
          error["state"] = false;
        }
        break;

      case "mobile":
        if (value === "") {
          error["mobile"] = "Required";
        } else if (!contactValidation.test(value)) {
          error["mobile"] = "Enter valid mobile no.";
        } else {
          error["mobile"] = false;
        }
        break;

      case "other_phone":
        if (!contactValidation.test(value)) {
          error["other_phone"] = "Enter valid mobile no.";
        } else {
          error["other_phone"] = false;
        }
        break;

      case "pincode":
        if (value === "") {
          error["pincode"] = "Required";
        } else {
          error["pincode"] = false;
        }
        break;

      case "country_id":
        if (value === "") {
          error["country_id"] = "Required";
        } else {
          error["country_id"] = false;
        }
        break;
      default:
        break;
    }
  }

  useEffect(() => {
    props.VendorCountryList()
    setLoading(true)
  }, [])

  useEffect(() => {
    if (props.vendorCountryListReducer.success) {
      const data = props.vendorCountryListReducer.data
      const bussinessType = !!data?.BusinessType ? data?.BusinessType : {};
      const buingGroup = !!data?.ByuingGroup ? data?.ByuingGroup : {};
      setBussinessTypeList(
        Object.keys(bussinessType).map((e) => {
          return { label: bussinessType[e], value: e };
        })
      );
      setBuyingGroupList(
        Object.keys(buingGroup).map((e) => {
          return { label: buingGroup[e], value: e };
        })
      );
      setCountryList({ ...data })
      props.getVendorList()
    }
  }, [props.vendorCountryListReducer])

  const handleaddVendorList = () => {
    if (state.username === "") {
      setError((pre) => ({
        ...pre,
        username: "Required",
      }));
    }
    if (state.email === "") {
      setError((pre) => ({
        ...pre,
        email: "Required",
      }));
    }
    if (state.password === "") {
      setError((pre) => ({
        ...pre,
        password: "Required",
      }));
    }
    if (state.conPassword === "") {
      setError((pre) => ({
        ...pre,
        conPassword: "Required",
      }));
    }
    if (state.mobile === "") {
      setError((pre) => ({
        ...pre,
        mobile: "Required",
      }));
    }
    if (state.first_name === "") {
      setError((pre) => ({
        ...pre,
        first_name: "Required",
      }));
    }
    if (state.last_name === "") {
      setError((pre) => ({
        ...pre,
        last_name: "Required",
      }));
    }
    if (state.company === "") {
      setError((pre) => ({
        ...pre,
        company: "Required",
      }));
    }
    if (state.website === "") {
      setError((pre) => ({
        ...pre,
        website: "Required",
      }));
    }
    if (state.about === "") {
      setError((pre) => ({
        ...pre,
        about: "Required",
      }));
    }
    if (state.job_title === "") {
      setError((pre) => ({
        ...pre,
        job_title: "Required",
      }));
    }
    if (state.business_type.length === 0) {
      setError((pre) => ({
        ...pre,
        business_type: "Required",
      }));
    }
    if (state.buying_group.length === 0) {
      setError((pre) => ({
        ...pre,
        buying_group: "Required",
      }));
    }
    if (state.group_title === "") {
      setError((pre) => ({
        ...pre,
        group_title: "Required",
      }));
    }
    if (state.address === "") {
      setError((pre) => ({
        ...pre,
        address: "Required",
      }));
    }
    if (state.city === "") {
      setError((pre) => ({
        ...pre,
        city: "Required",
      }));
    }
    if (state.state === "") {
      setError((pre) => ({
        ...pre,
        state: "Required",
      }));
    }
    if (state.pincode === "") {
      setError((pre) => ({
        ...pre,
        pincode: "Required",
      }));
    }
    if (state.country_id === "") {
      setError((pre) => ({
        ...pre,
        country_id: "Required",
      }));
    }
    if (
      state.username !== "" &&
      state.email !== "" &&
      state.mobile !== "" &&
      state.first_name !== "" &&
      state.last_name !== "" &&
      state.company !== "" &&
      state.website !== "" &&
      state.about !== "" &&
      state.job_title !== "" &&
      state.business_type !== "" &&
      state.buying_group !== "" &&
      state.group_title !== "" &&
      state.address !== "" &&
      state.city !== "" &&
      state.state !== "" &&
      state.pincode !== "" &&
      state.country_id !== "" &&
      !error.email &&
      !error.mobile &&
      !error.other_phone
    ) {

      if (!!params.id) {
        const list = {
          ...state,
          business_type: state.business_type.map((e) => e.value),
          buying_group: state.buying_group.map((e) => e.value),
        }
        delete list.password
        delete list.conPassword
        props.updateVendorList({
          details: { ...list },
          id: params.id
        })
        setLoading(true)
      } else {
        if (state.password !== "" &&
          state.conPassword !== "" &&
          state.password === state.conPassword) {
          props.addVendorList({
            ...state,
            business_type: state.business_type.map((e) => e.value),
            buying_group: state.buying_group.map((e) => e.value),
          });
          setLoading(true)
        }
      }

    }
  }

  useEffect(() => {
    if (props.addVendorListReducer.success) {
      history.push('/vendor')
      toast.success("Vendor created successfully")
      props.resetaddVendorList()
      setLoading(false)
    } else if (props.addVendorListReducer.error) {
      const errorMsg = props.addVendorListReducer.msg
      if (!!errorMsg && !!errorMsg.username) {
        setError(prevState => ({
          ...prevState,
          username: `* ${errorMsg.username[0]}`
        }))
      }
      if (!!errorMsg && !!errorMsg.email) {
        setError(prevState => ({
          ...prevState,
          email: `* ${errorMsg.email[0]}`
        }))
      }
      props.resetaddVendorList()
      setLoading(false)
    }
  }, [props.addVendorListReducer])


  useEffect(() => {
    if (props.updateVendorListReducer.success) {
      history.push('/vendor')
      toast.success("Vendor updated successfully")
      props.resetupdateVendorList()
      setLoading(false)

    } else if (props.updateVendorListReducer.error) {
      const errorMsg = props.updateVendorListReducer.msg
      if (!!errorMsg && !!errorMsg.username) {
        setError(prevState => ({
          ...prevState,
          username: `* ${errorMsg.username[0]}`
        }))
      }
      if (!!errorMsg && !!errorMsg.email) {
        setError(prevState => ({
          ...prevState,
          email: `* ${errorMsg.email[0]}`
        }))
      }
      props.resetupdateVendorList()
      setLoading(false)

    }
  }, [props.updateVendorListReducer])

  const handleReset = () => {
    setState(initialState);
    setError(initialError);
  };

  const handleMultiSelectChange = (name, value) => {
    setState((pre) => ({
      ...pre,
      [name]: value,
    }));

    switch (name) {
      case "business_type":
        if (value.length === 0) {
          error["business_type"] = "Required";
        } else {
          error["business_type"] = false;
        }
        break;

      case "buying_group":
        if (value.length === 0) {
          error["buying_group"] = "Required";
        } else {
          error["buying_group"] = false;
        }
        break;
      default:
        break;
    }
  };

  return (
    <>
      {
        loading ? <><Loader /> </> : <>
          <div className="page-top-line">
            <h2 className="page-title">
              {!!params.id ? "Update" : "Add"} -{" "}
              <span className="fw-semi-bold"> User</span>
            </h2>
          </div>
          <div className="card main-card">
            <div className="card-body">
              <h4 className="mb-5 fw-light">
                {!!params.id ? "Update" : "Add"} User
              </h4>
              <div className='row'>
                <div className='col-lg-6 col-12'>

                  <div className='row gx-3 '>
                    <div className='col-md-6 col-12 mb-3'>
                      <label className="form-label">Username</label>
                      <input
                        placeholder="Enter username"
                        name="username"
                        type="text"
                        className="form-control"
                        value={state.username}
                        onChange={handleInput}
                      />
                      {
                        error.username && <div>
                          <span className="text-danger h6">{error.username}</span>
                        </div>
                      }
                    </div>
                    <div className='col-md-6 col-12 mb-3'>
                      <label className="form-label">Email</label>
                      <input
                        placeholder="Enter email"
                        name="email"
                        type="text"
                        className="form-control"
                        value={state.email}
                        onChange={handleInput}
                      />
                      {
                        error.email && <div>
                          <span className="text-danger h6">{error.email}</span>
                        </div>
                      }
                    </div>

                    {!params.id && <>
                      <div className='col-md-6 col-12 mb-3'>
                        <label className="form-label">Password</label>
                        <div className='position-relative'>
                          <input
                            placeholder="Enter password"
                            name="password"
                            type={showPass ? 'text' : 'password'}
                            className="form-control"
                            value={state.password}
                            onChange={handleInput}
                          />
                          <div className='position-absolute' style={{
                            top: '8px',
                            right: '5px',
                            cursor: 'pointer'
                          }} onClick={() => {
                            setShowPass(!showPass)
                          }}><i className={`fa-regular ${showPass ? 'fa-eye' : 'fa-eye-slash'}`}></i></div>
                        </div>
                        {
                          error.password && <div>
                            <span className="text-danger h6">{error.password}</span>
                          </div>
                        }
                      </div>
                      <div className='col-md-6 col-12 mb-3'>
                        <label className="form-label">Confirm Password</label>
                        <div className='position-relative'>
                          <input
                            placeholder="Enter confirm password"
                            name="conPassword"
                            type={conShowPass ? 'text' : 'password'}
                            className="form-control"
                            value={state.conPassword}
                            onChange={handleInput}
                          />
                          <div className='position-absolute' style={{
                            top: '8px',
                            right: '5px',
                            cursor: 'pointer'
                          }} onClick={() => {
                            setConShowPass(!conShowPass)
                          }}><i className={`fa-regular ${conShowPass ? 'fa-eye' : 'fa-eye-slash'}`}></i></div>
                        </div>
                        {
                          error.conPassword && <div>
                            <span className="text-danger h6">{error.conPassword}</span>
                          </div>
                        }
                      </div>
                    </>}

                    <div className='col-md-6 col-12 mb-3'>
                      <label className="form-label">First Name</label>
                      <input
                        placeholder="Enter first name"
                        name="first_name"
                        type="text"
                        className="form-control"
                        value={state.first_name}
                        onChange={handleInput}
                      />
                      {
                        error.first_name && <div>
                          <span className="text-danger h6">{error.first_name}</span>
                        </div>
                      }
                    </div>
                    <div className='col-md-6 col-12 mb-3'>
                      <label className="form-label">Last Name</label>
                      <input
                        placeholder="Enter last name"
                        name="last_name"
                        type="text"
                        className="form-control"
                        value={state.last_name}
                        onChange={handleInput}
                      />
                      {
                        error.last_name && <div>
                          <span className="text-danger h6">{error.last_name}</span>
                        </div>
                      }
                    </div>


                    <div className='col-md-6 col-12 mb-3'>
                      <label className="form-label">Compnay</label>
                      <input
                        placeholder="Enter compnay name"
                        name="company"
                        type="text"
                        className="form-control"
                        value={state.company}
                        onChange={handleInput}
                      />
                      {
                        error.company && <div>
                          <span className="text-danger h6">{error.company}</span>
                        </div>
                      }
                    </div>
                    <div className='col-md-6 col-12 mb-3'>
                      <label className="form-label">Website</label>
                      <input
                        placeholder="Enter website URL"
                        name="website"
                        type="text"
                        className="form-control"
                        value={state.website}
                        onChange={handleInput}
                      />
                      {
                        error.website && <div>
                          <span className="text-danger h6">{error.website}</span>
                        </div>
                      }
                    </div>
                    <div className='col-12 mb-3'>
                      <label className="form-label">Address</label>
                      <textarea
                        placeholder="Enter address"
                        name="address"
                        type="text"
                        className="form-control"
                        value={state.address}
                        onChange={handleInput}
                      ></textarea>
                      {
                        error.address && <div>
                          <span className="text-danger h6">{error.address}</span>
                        </div>
                      }
                    </div>
                    <div className='col-md-4 col-12 mb-3'>
                      <label className="form-label">City</label>
                      <input
                        placeholder="Enter city"
                        name="city"
                        type="text"
                        className="form-control"
                        value={state.city}
                        onChange={handleInput}
                      />
                      {
                        error.city && <div>
                          <span className="text-danger h6">{error.city}</span>
                        </div>
                      }
                    </div>
                    <div className='col-md-4 col-12 mb-3'>
                      <label className="form-label">State</label>
                      <input
                        placeholder="Enter state"
                        name="state"
                        type="text"
                        className="form-control"
                        value={state.state}
                        onChange={handleInput}
                      />
                      {
                        error.state && <div>
                          <span className="text-danger h6">{error.state}</span>
                        </div>
                      }
                    </div>
                    <div className='col-md-4 col-12'>
                      <label className="form-label">Country</label>
                      <select
                        className="form-select"
                        name="country_id"
                        value={state.country_id}
                        onChange={handleInput}
                      >
                        <option value="">Select Country</option>
                        {
                          Object.keys(countryList).length !== 0 && countryList.country.map((e, i) => {
                            return (
                              <option value={e.id} key={i}>{e.name}</option>
                            )
                          })
                        }
                      </select>
                      {error.country_id && (
                        <div>
                          <span className="text-danger h6">{error.country_id}</span>
                        </div>
                      )}
                    </div>
                    <div className='col-md-4 col-12 mb-3'>
                      <label className="form-label">Pincode</label>
                      <input
                        placeholder="Enter pincode"
                        name="pincode"
                        type="text"
                        className="form-control"
                        value={state.pincode}
                        onChange={handleInput}
                      />
                      {
                        error.pincode && <div>
                          <span className="text-danger h6">{error.pincode}</span>
                        </div>
                      }
                    </div>
                    <div className='col-md-4 col-12 mb-3'>
                      <label className="form-label">Mobile No.</label>
                      <input
                        placeholder="Enter mobile No"
                        name="mobile"
                        type="text"
                        className="form-control"
                        value={state.mobile}
                        onChange={handleInput}
                      />
                      {
                        error.mobile && <div>
                          <span className="text-danger h6">{error.mobile}</span>
                        </div>
                      }
                    </div>
                    <div className='col-md-4 col-12 mb-3'>
                      <label className="form-label">Other Mobile No.</label>
                      <input
                        placeholder="Enter other mobile No"
                        name="other_phone"
                        type="text"
                        className="form-control"
                        value={state.other_phone}
                        onChange={handleInput}
                      />
                      {error.other_phone && (
                        <div>
                          <span className="text-danger h6">{error.other_phone}</span>
                        </div>
                      )}
                    </div>

                    <div className='col-md-6 col-12 mb-3'>
                      <label className="form-label">Job Title</label>
                      <input
                        placeholder="Enter job tille"
                        name="job_title"
                        type="text"
                        className="form-control"
                        value={state.job_title}
                        onChange={handleInput}
                      />
                      {
                        error.job_title && <div>
                          <span className="text-danger h6">{error.job_title}</span>
                        </div>
                      }
                    </div>
                    <div className='col-md-6 col-12 mb-3'>
                      <label className="form-label">Group Title</label>
                      <input
                        placeholder="Enter group title"
                        name="group_title"
                        type="text"
                        className="form-control"
                        value={state.group_title}
                        onChange={handleInput}
                      />
                      {error.group_title && (
                        <div>
                          <span className="text-danger h6">{error.group_title}</span>
                        </div>
                      )}
                    </div>

                    <div className='col-md-6 col-12 mb-3'>
                      <label className="form-label">Bussiness type</label>
                      <MultiSelect
                        options={bussinessTypeList}
                        disableSearch
                        value={state.business_type}
                        onChange={(e) =>
                          handleMultiSelectChange("business_type", e)
                        }
                        labelledBy="business_type"
                        hasSelectAll={false}
                        className="multi-form-select"
                      />
                      {
                        error.business_type && <div>
                          <span className="text-danger h6">{error.business_type}</span>
                        </div>
                      }
                    </div>
                    <div className='col-md-6 col-12 mb-3'>
                      <label className="form-label">Buying Group</label>
                      <MultiSelect
                        options={buyingGroupList}

                        disableSearch
                        hasSelectAll={false}
                        value={state.buying_group}
                        onChange={(e) => handleMultiSelectChange("buying_group", e)}
                        className="multi-form-select"
                      />
                      {error.buying_group && (
                        <div>
                          <span className="text-danger h6">{error.buying_group}</span>
                        </div>
                      )}
                    </div>

                    <div className='col-12 mb-3'>
                      <label className="form-label">About</label>
                      <textarea
                        placeholder="Enter about "
                        name="about"
                        type="text"
                        className="form-control"
                        value={state.about}
                        onChange={handleInput}
                      ></textarea>
                      {
                        error.about && <div>
                          <span className="text-danger h6">{error.about}</span>
                        </div>
                      }
                    </div>
                  </div>
        
                </div>
              </div>

              <div className='mt-3'>

                <button
                  className="btn btn-primary"
                  onClick={handleaddVendorList}
                >
                  {!!params.id ? "Update" : "Submit"}
                </button>
                <button className="btn btn-secondary ms-3" onClick={handleReset}>
                  Reset
                </button>
              </div>



            </div>
          </div>
        </>
      }</>
  )
}

const mapStateToProp = (state) => ({
  vendorCountryListReducer: state.vendorListReducer.vendorCountryList,
  addVendorListReducer: state.vendorListReducer.addVendorList,
  updateVendorListReducer: state.vendorListReducer.updateVendorList,
  VendorListReducer: state.vendorListReducer.VendorList,
});

const mapDispatchToProps = (dispatch) => ({
  VendorCountryList: (details) => dispatch(VendorCountryList(details)),
  getVendorList: () => dispatch(getVendorList()),
  addVendorList: (details) => dispatch(addVendorList(details)),
  updateVendorList: (details) => dispatch(updateVendorList(details)),
  resetaddVendorList: (details) => dispatch({ type: RESET_ADD_VENDOR_LIST }),
  resetupdateVendorList: (details) => dispatch({ type: RESET_UPDATE_VENDOR_LIST }),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddVendor);

