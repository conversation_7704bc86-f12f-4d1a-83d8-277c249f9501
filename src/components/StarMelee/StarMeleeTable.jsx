import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import {
  changeStarMeleeStatus,
  deleteStarMelee,
  getStarMelee,
} from "../../Services/actions/starMeleeAction";
import {
  RESET_ADD_STAR_MELEE,
  RESET_DELETE_STAR_MELEE,
  RESET_STAR_MELEE_STATUS,
} from "../../Services/Constant";
import moment from "moment";
import { useHistory } from "react-router";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
} from "reactstrap";
import {
  BootstrapTable,
  SearchField,
  TableHeaderColumn,
} from "react-bootstrap-table";
import { toast } from "react-toastify";
import { Link } from "react-router-dom";
import Loader from "../Loader/Loader";
import { getAllMastersList } from "../../Services/actions/stockAction";

function StarMeleeTable(props) {
  const [loading, setLoading] = useState(true);
  const [state, setState] = useState([]);
  const history = useHistory();
  const [isDelete, setIsDelete] = useState(false);
  const [isDeleteId, setIsDeleteId] = useState(null);

  useEffect(() => {
    if (!props.StarMeleeReducer.success) {
      props.getStarMelee();
    }
  }, []);
  useEffect(() => {
    setLoading(props.StarMeleeReducer.loading);
    if (props.StarMeleeReducer.success) {
      const data = props.StarMeleeReducer.data;

      const details = data
        .sort((a, b) => moment(b.created_at).diff(moment(a.created_at)))
        .map((e, i) => {
          return {
            srNo: i + 1,
            size: !!e.size ? e.size : "N/A",
            shape: !!e.shape ? e.shape : "N/A",
            sieve: !!e.sieve ? e.sieve : "N/A",
            carat: !!e.carat ? e.carat : "N/A",
            def_vvs_vs: !!e.def_vvs_vs ? e.def_vvs_vs : "N/A",
            def_vs_si: !!e.def_vs_si ? e.def_vs_si : "N/A",
            fg_vvs_vs: !!e.fg_vvs_vs ? e.fg_vvs_vs : "N/A",
            fg_vs_si: !!e.fg_vs_si ? e.fg_vs_si : "N/A",
            blue_vvs_vs: !!e.blue_vvs_vs ? e.blue_vvs_vs : "N/A",
            pink_vvs_vs_si1: !!e.pink_vvs_vs_si1 ? e.pink_vvs_vs_si1 : "N/A",
            yellow_vvs_vs_si1: !!e.yellow_vvs_vs_si1
              ? e.yellow_vvs_vs_si1
              : "N/A",
            status: !!e.status ? e.status : "N/A",
            id: !!e.id ? e.id : "N/A",
          };
        });
      setState([...details]);
    }
  }, [props.StarMeleeReducer]);

  const handleDelete = () => {
    props.deleteStarMelee(isDeleteId);
  };

  useEffect(() => {
    if (props.addStarMeleeReducer.success) {
      props.getStarMelee();
      props.resetAddStarMelee();
    }
  }, [props.addStarMeleeReducer]);

  useEffect(() => {
    if (props.deleteStarMeleeReducer.success) {
      setIsDelete(false);
      setIsDeleteId(null);
      props.getStarMelee();
      props.resetDeleteStarMelee();
      toast.success("star melee inquiry deleted successfully...");
    }
  }, [props.deleteStarMeleeReducer]);

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <div className={`d-flex justify-content-center`}>
          <Button
            className="btn-action me-2"
            onClick={() => {
              history.push(`/star-melee/${value.id}`);
            }}
          >
            <i className="fa-regular fa-pen-to-square"></i>
          </Button>
          {/* <Button
            className="btn-action mx-2"
            onClick={() => {
              history.push(`/star-melee/details/${value.id}`);
            }}
          >
            <i className="fa-regular fa-eye"></i>
          </Button> */}
          <Button
            className="btn-action me-2"
            onClick={() => {
              setIsDelete(true);
              setIsDeleteId(value.id);
            }}
          >
            <i className="fa-regular fa-trash-can"></i>
          </Button>
        </div>
      </div>
    );
  };

  const handleStatus = async (status, id) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeStarMeleeStatus({
      details: details,
      id: id,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.statusStarMeleeReducer.success) {
      // props.getStarMelee();
      props.resetStatusStarMelee();
    }
  }, [props.statusStarMeleeReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.status, value.id)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const createCustomSearchField = (props) => {
    return <SearchField className="mb-sm-5 me-1" placeholder="Search" />;
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <div className="">
          <div className="text-end mb-4">
            <Link to="/star-melee/add" className="btn btn-primary">
              Add New
            </Link>
          </div>
          <div className="">
            <BootstrapTable
              bordered={true}
              data={state}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`table-responsive table-striped table-hover`}
            >
              <TableHeaderColumn
                dataField="srNo"
                width="100px"
                row="0"
                rowSpan="2"
              >
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                dataField="shape"
                row="0"
                rowSpan="2"
                dataSort
                width="150px"
              >
                <span className="fs-sm">Shape</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                dataField="size"
                row="0"
                rowSpan="2"
                dataSort
                width="150px"
              >
                <span className="fs-sm">Size in mm</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                dataField="sieve"
                row="0"
                rowSpan="2"
                dataSort
                width="150px"
              >
                <span className="fs-sm">SIEVE SIZES</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                dataField="carat"
                row="0"
                rowSpan="2"
                dataSort
                width="150px"
              >
                <span className="fs-sm">CARAT WEIGHT</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                width="150px"
                row="0"
                colSpan="2"
                thStyle={{ textAlign: "center" }}
              >
                <span className="fs-sm">DEF</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="def_vvs_vs"
                row="1"
                dataSort
                width="150px"
              >
                <span className="fs-sm">VVS-VS</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="def_vs_si"
                row="1"
                dataSort
                width="150px"
              >
                <span className="fs-sm">VS-SI</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                width="150px"
                row="0"
                colSpan="2"
                thStyle={{ textAlign: "center" }}
              >
                <span className="fs-sm">FG</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="fg_vvs_vs"
                row="1"
                dataSort
                width="150px"
              >
                <span className="fs-sm">VVS-VS</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="fg_vs_si"
                row="1"
                dataSort
                width="150px"
              >
                <span className="fs-sm">VS-SI</span>
              </TableHeaderColumn>
              <TableHeaderColumn width="150px" row="0" colSpan="1">
                <span className="fs-sm">BLUE </span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="blue_vvs_vs"
                row="1"
                dataSort
                width="150px"
              >
                <span className="fs-sm">VVS-VS</span>
              </TableHeaderColumn>
              <TableHeaderColumn width="150px" row="0" colSpan="1">
                <span className="fs-sm">PINK</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="pink_vvs_vs_si1"
                dataSort
                row="1"
                width="150px"
              >
                <span className="fs-sm"> VVS-VS-SI1</span>
              </TableHeaderColumn>
              <TableHeaderColumn width="150px" row="0" colSpan="1">
                <span className="fs-sm">YELLOW</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="yellow_vvs_vs_si1"
                dataSort
                row="1"
                width="150px"
              >
                <span className="fs-sm"> VVS-VS-SI1</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
                dataSort
                width="150px"
                row="0"
                rowSpan="2"
              >
                <span className="fs-sm">Status</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
                width="190px"
                row="0"
                rowSpan="2"
              >
                <span className="fs-sm">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          </div>
          <Modal size="sm" isOpen={isDelete} toggle={() => setIsDelete(false)}>
            <ModalHeader
              toggle={() => {
                setIsDelete(false);
                setIsDeleteId(null);
              }}
            >
              Confirm delete
            </ModalHeader>
            <ModalBody className="bg-white">
              Are you sure you want to delete this item?
            </ModalBody>
            <ModalFooter>
              <Button
                color="secondary"
                onClick={() => {
                  setIsDelete(false);
                  setIsDeleteId(null);
                }}
              >
                Cancel
              </Button>
              <Button color="primary" onClick={handleDelete}>
                Delete
              </Button>
            </ModalFooter>
          </Modal>
        </div>
      )}
    </>
  );
}

const mapStateToProp = (state) => ({
  StarMeleeReducer: state.starMeleeReducer.StarMelee,
  deleteStarMeleeReducer: state.starMeleeReducer.deleteStarMelee,
  addStarMeleeReducer: state.starMeleeReducer.addStarMelee,
  updateStarMeleeReducer: state.starMeleeReducer.updateStarMelee,
  statusStarMeleeReducer: state.starMeleeReducer.statusStarMelee,
});

const mapDispatchToProps = (dispatch) => ({
  getStarMelee: () => dispatch(getStarMelee()),
  deleteStarMelee: (id) => dispatch(deleteStarMelee(id)),
  changeStarMeleeStatus: (details) => dispatch(changeStarMeleeStatus(details)),
  resetDeleteStarMelee: (id) => dispatch({ type: RESET_DELETE_STAR_MELEE }),
  resetAddStarMelee: (id) => dispatch({ type: RESET_ADD_STAR_MELEE }),
  resetStatusStarMelee: (id) => dispatch({ type: RESET_STAR_MELEE_STATUS }),
});

export default connect(mapStateToProp, mapDispatchToProps)(StarMeleeTable);
