import React from "react";
import { Tab, Tabs } from "react-bootstrap";
import { useParams } from "react-router";
import AddSingleStarMelee from "./AddSingleStarMelee";
import StarMeleeCSV from "./StarMeleeCSV";

function AddStarMelee() {
  const params = useParams();

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          {!!params.id ? "Update" : "Add"} -{" "}
          <span className="fw-semi-bold">Star Melee</span>
        </h2>
      </div>
      <Tabs
        defaultActiveKey={!params.id ? "home" : "profile"}
        id="addStarMelee"
      >
        {!params.id && (
          <Tab eventKey="home" title="Upload CSV">
            <StarMeleeCSV />
          </Tab>
        )}
        <Tab
          eventKey="profile"
          title={`${!!params.id ? "Update" : "Add"} Product`}
        >
          <AddSingleStarMelee />
        </Tab>
      </Tabs>
    </>
  );
}

export default AddStarMelee;


