import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import {
  downloadStarmeleeFile,
  getStarMelee,
  uploadStarMeleeCsvFile,
} from "../../Services/actions/starMeleeAction";
import { RESET_UPLOAD_CSV_STAR_MELEE } from "../../Services/Constant";
import { Col, Row } from "reactstrap";
import { toast } from "react-toastify";
import { useHistory } from "react-router";

function StarMeleeCSV(props) {
  const history = useHistory();
  const [diamondType, setDiamondType] = useState("");
  useEffect(() => {
    if (props.uploadStarMeleeCsvFileReducer.success) {
      props.getStarMelee();
      props.resetCsvFile();
      toast.success("Star melee added successfully");
      history.push("/star-melee");
    }
  }, [props.uploadStarMeleeCsvFileReducer]);

  const handleDownload = () => {
    // const link = document.createElement("a");
    // link.href = "https://server.delightdiamonds.com/sample/STAR_MELEE.xlsx";
    // link.download = "STAR_MELEE.xlsx";
    // document.body.appendChild(link);
    // link.click();
    // document.body.removeChild(link);
    props.downloadStarmeleeFile();
  };
  return (
    <>
      <div className="d-flex justify-content-between align-items-top">
        <h4 className="mb-5 fw-light">Upload Star Melee</h4>

        <div className="position-relative d-inline-block">
          <div
            className="border p-2 "
            style={{
              background: "#C2B362",
              color: "#ffffff",
              borderRadius: "10px",
              cursor: "pointer",
            }}
            onClick={handleDownload}
          >
            Export Star-Melee data
          </div>
        </div>
      </div>
      <Row>
        <Col lg={5} md={8} className="mb-3">
          <label className="form-label">Diamond Type</label>
          <select
            className="form-select"
            value={diamondType}
            onChange={(e) => {
              setDiamondType(e.target.value);
            }}
          >
            <option value="">Select Type</option>
            <option value="Natural Diamond">Natural Diamond</option>
            <option value="Lab-Grown Diamond">Lab-Grown Diamond</option>
          </select>
          {/* {error.cert_type && (
              <h6 className="text-danger">{error.cert_type}</h6>
            )} */}
        </Col>
      </Row>
      <Row
        style={
          diamondType === ""
            ? {
                opacity: "0.5",
                pointerEvents: "none",
              }
            : {}
        }
      >
        <Col lg={5} md={8}>
          <div>
            <div className="mb-4">
              <label className="form-label">Upload CSV File</label>
              <input
                type="file"
                className="form-control"
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                onChange={(e) => {
                  props.uploadStarMeleeCsvFile({
                    excel: e.target.files[0],
                    diamond_type: diamondType == "Natural Diamond" ? 0 : 1,
                  });
                }}
              />
            </div>
          </div>
        </Col>
      </Row>
    </>
  );
}

const mapStateToProp = (state) => ({
  uploadStarMeleeCsvFileReducer: state.starMeleeReducer.uploadStarMeleeCsvFile,
});

const mapDispatchToProps = (dispatch) => ({
  getStarMelee: () => dispatch(getStarMelee()),
  downloadStarmeleeFile: () => dispatch(downloadStarmeleeFile()),
  uploadStarMeleeCsvFile: (details) =>
    dispatch(uploadStarMeleeCsvFile(details)),
  // getAllMastersList: () => dispatch(getAllMastersList()),
  // saveCsvFile: (details) => dispatch(saveCsvFile(details)),
  resetCsvFile: () => dispatch({ type: RESET_UPLOAD_CSV_STAR_MELEE }),
});

export default connect(mapStateToProp, mapDispatchToProps)(StarMeleeCSV);
