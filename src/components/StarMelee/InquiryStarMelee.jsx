import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { getStarMeleeInquiry } from "../../Services/actions/starMeleeAction";
import Loader from "../Loader/Loader";
import {
  BootstrapTable,
  SearchField,
  TableHeaderColumn,
} from "react-bootstrap-table";
import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
} from "reactstrap";

function InquiryStarMelee(props) {
  const [loading, setLoading] = useState(true);
  const [details, setDetails] = useState([]);

  useEffect(() => {
    setLoading(true);
    props.getStarMeleeInquiry();
  }, []);

  useEffect(() => {
    if (props.starMeleeInquiryReducer.success) {
      setLoading(false);
      const data = props.starMeleeInquiryReducer.data;
      const _details = data.map((e, i) => {
        return {
          srNo: i + 1,
          carat: !!e.carat && e.carat !== "" ? e.carat : "N/A",
          clarity: !!e.clarity && e.clarity !== "" ? e.clarity : "N/A",
          color: !!e.color && e.color !== "" ? e.color : "N/A",
          email: !!e.email && e.email !== "" ? e.email : "N/A",
          mobile: !!e.mobile && e.mobile !== "" ? e.mobile : "N/A",
          name: !!e.name && e.name !== "" ? e.name : "N/A",
          price: !!e.price && e.price !== "" ? e.price : "N/A",
          qty: !!e.qty && e.qty !== "" ? e.qty : "N/A",
          shape: !!e.shape && e.shape !== "" ? e.shape : "N/A",
          total: parseInt(e.price) * parseInt(e.qty),
        };
      });
      setDetails([..._details]);
    }
  }, [props.starMeleeInquiryReducer]);

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const createCustomSearchField = (props) => {
    return <SearchField className="mb-sm-5 me-1" placeholder="Search" />;
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <div className="">
          <div className="">
            <BootstrapTable
              bordered={false}
              data={details}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`table-responsive table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo" width="100px" isKey>
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="name" dataSort width="150px">
                <span className="fs-sm">Name</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="email" dataSort width="150px">
                <span className="fs-sm">Email</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="mobile" dataSort width="150px">
                <span className="fs-sm">Mobile</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="shape" dataSort width="150px">
                <span className="fs-sm">Shape</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="color" dataSort width="150px">
                <span className="fs-sm">Color</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="clarity" dataSort width="150px">
                <span className="fs-sm">Clarity</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="carat" dataSort width="150px">
                <span className="fs-sm">Carat</span>
              </TableHeaderColumn>
              
              
              <TableHeaderColumn dataField="price" dataSort width="150px">
                <span className="fs-sm">Price</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="qty" dataSort width="150px">
                <span className="fs-sm">Quantity</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="total" dataSort width="150px">
                <span className="fs-sm">Total</span>
              </TableHeaderColumn>
            </BootstrapTable>
          </div>
        </div>
      )}
    </>
  );
}

const mapStateToProp = (state) => ({
  starMeleeInquiryReducer: state.starMeleeReducer.starMeleeInquiry,
});

const mapDispatchToProps = (dispatch) => ({
  getStarMeleeInquiry: () => dispatch(getStarMeleeInquiry()),
});

export default connect(mapStateToProp, mapDispatchToProps)(InquiryStarMelee);
