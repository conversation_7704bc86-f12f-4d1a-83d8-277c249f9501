import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import {
  addStarMelee,
  getStarMelee,
  updateStarMelee,
} from "../../Services/actions/starMeleeAction";
import {
  RESET_ADD_STAR_MELEE,
  RESET_UPDATE_STAR_MELEE,
} from "../../Services/Constant";
import { useHistory, useParams } from "react-router";
import { Col, Row } from "reactstrap";
import { getAllMastersList } from "../../Services/actions/stockAction";
import { toast } from "react-toastify";

function AddSingleStarMelee(props) {
  const [details, setDetails] = useState(null);
  const [diamondType, setDiamondType] = useState("");
  const history = useHistory();
  const params = useParams();
  const initialState = {
    size: "",
    shape: "",
    sieve: "",
    carat: "",
    def_vvs_vs: "",
    def_vs_si: "",
    fg_vvs_vs: "",
    fg_vs_si: "",
    blue_vvs_vs: "",
    pink_vvs_vs_si1: "",
    yellow_vvs_vs_si1: "",
  };

  const initialError = {
    size: false,
    shape: false,
    carat: false,
  };
  const [state, setState] = useState(initialState);
  const [error, setError] = useState(initialError);

  useEffect(() => {
    if (!props.getAllMastersListReducer.success) {
      props.getAllMastersList();
    }
  }, []);

  useEffect(() => {
    if (props.getAllMastersListReducer.success) {
      const data = props.getAllMastersListReducer.data;
      setDetails(data);
    }
  }, [props.getAllMastersListReducer]);

  useEffect(() => {
    if (!props.StarMeleeReducer.success) {
      props.getStarMelee();
    }
  }, []);

  useEffect(() => {
    if (!!params.id && props.StarMeleeReducer.success) {
      const data = props.StarMeleeReducer.data;
      const _details = data.find((e) => {
        return e.id === parseInt(params.id);
      });
      setState(_details);
    }
  }, [props.StarMeleeReducer, params]);

  const handleInput = (e) => {
    const { name, value } = e.target;

    if (
      name === "carat" ||
      name === "def_vvs_vs" ||
      name === "def_vs_si" ||
      name === "fg_vvs_vs" ||
      name === "fg_vs_si" ||
      name === "blue_vvs_vs" ||
      name === "pink_vvs_vs_si1" ||
      name === "yellow_vvs_vs_si1"
    ) {
      if (RegExp(/^([0-9]{0,5}([.][0-9]{0,2})?)$/i).test(value)) {
        setState((prevState) => ({
          ...prevState,
          [name]: value,
        }));
      } else return;
    } else if (name === "size" || name === "sieve") {
      if (RegExp(/^([0-9]{0,3}([.][0-9]{0,2})?)$/i).test(value)) {
        setState((prevState) => ({
          ...prevState,
          [name]: value,
        }));
      } else return;
    } else {
      setState((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    }

    switch (name) {
      case "shape":
        if (value === "") {
          setError((prev) => ({
            ...prev,
            shape: "* select Shape",
          }));
        } else {
          setError((prev) => ({
            ...prev,
            shape: false,
          }));
        }
        break;
      case "size":
        if (value === "") {
          setError((prev) => ({
            ...prev,
            size: "* Enter size in mm",
          }));
        } else {
          setError((prev) => ({
            ...prev,
            size: false,
          }));
        }
        break;
      case "carat":
        if (value === "") {
          setError((prev) => ({
            ...prev,
            carat: "* Enter carat",
          }));
        } else {
          setError((prev) => ({
            ...prev,
            carat: false,
          }));
        }
        break;

      default:
        break;
    }
  };

  const handleReset = () => {
    setState(initialState);
    setError(initialError);
  };

  const handleSubmit = () => {
    if (state.shape === "") {
      setError((prev) => ({
        ...prev,
        shape: "* select Shape",
      }));
    }
    if (state.size === "") {
      setError((prev) => ({
        ...prev,
        size: "* Enter size in mm",
      }));
    }
    if (state.carat === "") {
      setError((prev) => ({
        ...prev,
        carat: "* Enter carat",
      }));
    }
    if (state.shape !== "" && state.size !== "" && state.carat !== "") {
      if (!!params.id) {
        props.updateStarMelee({
          details: state,
          id: params.id,
          diamond_type: diamondType == "Natural Diamond" ? 0 : 1,
        });
      } else {
        props.addStarMelee({
          ...state,
          diamond_type: diamondType == "Natural Diamond" ? 0 : 1,
        });
      }
    }
  };

  useEffect(() => {
    if (props.addStarMeleeReducer.success) {
      props.getStarMelee();
      props.resetAddStarMelee();
      toast.success("Star melee added successfully");
      history.push("/star-melee");
    }
  }, [props.addStarMeleeReducer]);

  useEffect(() => {
    if (props.updateStarMeleeReducer.success) {
      props.getStarMelee();
      props.resetUpdateStarMelee();
      toast.success("Star melee updated successfully");
      history.push("/star-melee");
    }
  }, [props.updateStarMeleeReducer]);

  return (
    <>
      <h4 className="mb-5 fw-light">
        {!!params.id ? "Update" : "Add"} Product
      </h4>

      {!!details && (
        <div>
          <Row>
            <Col lg={5} md={8} className="mb-3">
              <label className="form-label">Diamond Type</label>
              <select
                className="form-select"
                value={diamondType}
                onChange={(e) => {
                  setDiamondType(e.target.value);
                }}
              >
                <option value="">Select Type</option>
                <option value="Natural Diamond">Natural Diamond</option>
                <option value="Lab-Grown Diamond">Lab-Grown Diamond</option>
              </select>
            </Col>
          </Row>
          {diamondType !== "" && (
            <Row className="col-lg-6 gx-3">
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">Shape</label>
                <select
                  className="form-select"
                  name="shape"
                  value={state.shape}
                  onChange={handleInput}
                >
                  <option value={""}>Select Shape</option>
                  {details.Shape.map((e, i) => {
                    return (
                      <option value={e.name} key={i}>
                        {e.name}
                      </option>
                    );
                  })}
                </select>
                {error.shape && <h6 className="text-danger">{error.shape}</h6>}
              </Col>
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">Size in mm</label>
                <input
                  type="text"
                  placeholder="Enter Size"
                  className="form-control"
                  value={state?.size}
                  name={"size"}
                  onChange={handleInput}
                />
                {error.size && <h6 className="text-danger">{error.size}</h6>}
              </Col>
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">Sieve Sizes</label>
                <input
                  type="text"
                  placeholder="Enter sieve"
                  className="form-control"
                  value={state?.sieve}
                  name={"sieve"}
                  onChange={handleInput}
                />
              </Col>
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">Carat</label>
                <input
                  type="text"
                  placeholder="Enter carat"
                  className="form-control"
                  value={state?.carat}
                  name={"carat"}
                  onChange={handleInput}
                />
                {error.carat && <h6 className="text-danger">{error.carat}</h6>}
              </Col>
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">DEF VVS-VS</label>
                <input
                  type="text"
                  placeholder="Enter DEF VVS-VS"
                  className="form-control"
                  value={state?.def_vvs_vs}
                  name={"def_vvs_vs"}
                  onChange={handleInput}
                />
              </Col>
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">DEF VS-SI</label>
                <input
                  type="text"
                  placeholder="Enter DEF VS-SI"
                  className="form-control"
                  value={state?.def_vs_si}
                  name={"def_vs_si"}
                  onChange={handleInput}
                />
              </Col>
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">FG VVS-VS</label>
                <input
                  type="text"
                  placeholder="Enter FG VVS-VS"
                  className="form-control"
                  value={state?.fg_vvs_vs}
                  name={"fg_vvs_vs"}
                  onChange={handleInput}
                />
              </Col>
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">FG VS-SI</label>
                <input
                  type="text"
                  placeholder="Enter FG VS-SI"
                  className="form-control"
                  value={state?.fg_vs_si}
                  name={"fg_vs_si"}
                  onChange={handleInput}
                />
              </Col>
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">BLUE VVS-VS</label>
                <input
                  type="text"
                  placeholder="Enter BLUE VVS-VS"
                  className="form-control"
                  value={state?.blue_vvs_vs}
                  name={"blue_vvs_vs"}
                  onChange={handleInput}
                />
              </Col>
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">PINK VVS-VS-SI1</label>
                <input
                  type="text"
                  placeholder="Enter PINK VVS-VS-SI1"
                  className="form-control"
                  value={state?.pink_vvs_vs_si1}
                  name={"pink_vvs_vs_si1"}
                  onChange={handleInput}
                />
              </Col>
              <Col lg={6} md={6} className="mb-3">
                <label className="form-label">YELLOW VVS-VS-SI1</label>
                <input
                  type="text"
                  placeholder="Enter YELLOW VVS-VS-SI1"
                  className="form-control"
                  value={state?.yellow_vvs_vs_si1}
                  name={"yellow_vvs_vs_si1"}
                  onChange={handleInput}
                />
              </Col>
            </Row>
          )}
          {diamondType !== "" && (
            <div className="mt-3">
              <button className="btn btn-primary" onClick={handleSubmit}>
                {!!params.id ? "Update" : "Submit"}
              </button>
              <button className="btn btn-secondary ms-3" onClick={handleReset}>
                Reset
              </button>
            </div>
          )}
        </div>
      )}
    </>
  );
}

const mapStateToProp = (state) => ({
  StarMeleeReducer: state.starMeleeReducer.StarMelee,
  getAllMastersListReducer: state.stockReducer.getAllMastersList,
  addStarMeleeReducer: state.starMeleeReducer.addStarMelee,
  updateStarMeleeReducer: state.starMeleeReducer.updateStarMelee,
});

const mapDispatchToProps = (dispatch) => ({
  getAllMastersList: () => dispatch(getAllMastersList()),
  addStarMelee: (details) => dispatch(addStarMelee(details)),
  getStarMelee: () => dispatch(getStarMelee()),
  updateStarMelee: (details) => dispatch(updateStarMelee(details)),
  resetUpdateStarMelee: (id) => dispatch({ type: RESET_UPDATE_STAR_MELEE }),
  resetAddStarMelee: (id) => dispatch({ type: RESET_ADD_STAR_MELEE }),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddSingleStarMelee);
