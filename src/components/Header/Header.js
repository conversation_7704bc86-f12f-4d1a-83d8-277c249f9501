import React from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { withRouter } from 'react-router';
import {
  Navbar,
  Nav,
  Dropdown,
  NavItem,
  NavLink,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
  UncontrolledTooltip,
  InputGroup,
  Input,
  Form,
  FormGroup,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
} from 'reactstrap';
import cx from 'classnames';
import { NavbarTypes } from '../../Services/reducers/layout';
import Notifications from '../Notifications';
import { logoutUser } from '../../Services/actions/auth';
import { STATUS } from 'react-joyride';
import { toggleSidebar, openSidebar, closeSidebar, changeActiveSidebarItem } from '../../Services/actions/navigation';

import adminDefault from '../../images/chat/chat2.png';
import Menu from '../../images/sidebar/basil/Menu';
// import Exchange from '../../images/sidebar/basil/Exchange';
// import Cross from '../../images/sidebar/basil/Cross';
import Settings from '../../images/sidebar/basil/Settings';
import Search from '../../images/sidebar/basil/Search';
import UserDefault from '../../images/sidebar/basil/UserDefault';
import EnvelopeBlack from '../../images/sidebar/basil/EnvelopeBlack';
import PowerButton from '../../images/sidebar/basil/PowerButton';
import CalendarIcon from '../../images/sidebar/Outline/Calendar';

import s from './Header.module.scss'; // eslint-disable-line css-modules/no-unused-class

class Header extends React.Component {
  static propTypes = {
    sidebarOpened: PropTypes.bool.isRequired,
    sidebarStatic: PropTypes.bool.isRequired,
    dispatch: PropTypes.func.isRequired,
    location: PropTypes.shape({
      pathname: PropTypes.string,
    }).isRequired,
  };

  constructor(props) {
    super(props);

    this.toggleMenu = this.toggleMenu.bind(this);
    this.switchSidebar = this.switchSidebar.bind(this);
    this.toggleNotifications = this.toggleNotifications.bind(this);
    this.toggleSidebar = this.toggleSidebar.bind(this);
    this.doLogout = this.doLogout.bind(this);
    this.handleLogoutClick = this.handleLogoutClick.bind(this);

    this.state = {
      menuOpen: false,
      notificationsOpen: false,
      notificationsTabSelected: 1,
      focus: false,
      showNewMessage: false,
      hideMessage: true,
      run: false,
      isLogout: false,
    };
  }

  componentDidMount() {
    if (window.location.href.includes('main')) {
      this.setState({ run: true })
    }
  }

  handleJoyrideCallback = (CallBackProps) => {
    const { status } = CallBackProps;

    if (([STATUS.FINISHED, STATUS.SKIPPED]).includes(status)) {
      this.setState({ run: false });
    }

  };

  start = () => {
    this.setState({
      run: true,
    });
  };

  toggleFocus = () => {
    this.setState({ focus: !this.state.focus })
  }

  toggleNotifications() {
    this.setState({
      notificationsOpen: !this.state.notificationsOpen,
    });
  }
  handleLogoutClick() {
    this.setState(prev => ({
      ...prev,
      isLogout: true
    }))
  }
  doLogout() {
    this.props.dispatch(logoutUser());
  }

  // collapse/uncolappse
  switchSidebar() {
    if (this.props.sidebarOpened) {
      this.props.dispatch(closeSidebar());
      this.props.dispatch(changeActiveSidebarItem(null));
    } else {
      const paths = this.props.location.pathname.split('/');
      paths.pop();
      this.props.dispatch(openSidebar());
      this.props.dispatch(changeActiveSidebarItem(paths.join('/')));
    }
  }

  // static/non-static
  toggleSidebar() {
    this.props.dispatch(toggleSidebar());
    if (this.props.sidebarStatic) {
      localStorage.setItem('staticSidebar', 'false');
      this.props.dispatch(changeActiveSidebarItem(null));
    } else {
      localStorage.setItem('staticSidebar', 'true');
      const paths = this.props.location.pathname.split('/');
      paths.pop();
      this.props.dispatch(changeActiveSidebarItem(paths.join('/')));
    }
  }

  toggleMenu() {
    this.setState({
      menuOpen: !this.state.menuOpen,
    });
  }
  render() {
    const { focus } = this.state;
    const { openUsersList } = this.props;
    const navbarType = localStorage.getItem("navbarType") || 'static'

    const user = this.props.currentUser;
    const avatar = '/images/people/avtar.jpg';


    const firstUserLetter = user && (user.firstName || user.email)[0].toUpperCase();


    return (
      <Navbar className={`${s.root} d-print-none ${navbarType === NavbarTypes.FLOATING ? s.navbarFloatingType : ''}`} style={{ zIndex: !openUsersList ? 100 : 0 }}>
        <div className="d-flex flex-row justify-content-md-start flex-grow-1 align-content-center align-self-start">
          <Nav className="my-auto">
            <NavItem>
              <NavLink className={`d-md-down-none ${s.toggleSidebar}`} id="toggleSidebar" onClick={this.toggleSidebar}>
                <span className={s.headerSvgFlipColor}>
                  <Menu />
                </span>
              </NavLink>
              <UncontrolledTooltip placement="bottom" target="toggleSidebar">
                Turn on/off<br />sidebar<br />collapsing
              </UncontrolledTooltip>
              <NavLink className="fs-lg d-md-none" onClick={this.switchSidebar}>
                <span
                  className={`rounded rounded-lg d-md-none d-sm-down-block`}>
                  <span
                    className={s.headerSvgFlipColor}
                    style={{ fontSize: 30 }}
                  >
                    <Menu />
                  </span>
                </span>
                <span className={`ms-3 d-sm-down-none ${s.headerSvgFlipColor}`}>
                  <Menu />
                </span>
              </NavLink>
            </NavItem>
          </Nav>

          <Form className={`${s.headerSearchInput} d-sm-down-none`} inline>
            <FormGroup>
              <InputGroup onFocus={this.toggleFocus} onBlur={this.toggleFocus} className={
                cx('input-group-no-border', { 'focus': !!focus })
              }>
                <div className={`${s.headerSvgFlipColor} input-group-prepend-icon`}><Search /></div>
                <Input id="search-input" placeholder="Search Dashboard" className={cx({ 'focus': !!focus })} />
              </InputGroup>
            </FormGroup>
          </Form>

          <NavLink className={`${s.navbarBrand} d-md-none ${s.headerSvgFlipColor}`}>
            <i className="fa fa-circle text-primary me-n-sm" />
            <i className="fa fa-circle text-danger" />
            &nbsp;
            Delight Diamonds
            &nbsp;
            <i className="fa fa-circle text-danger me-n-sm" />
            <i className="fa fa-circle text-primary" />
          </NavLink>
        </div>

        <div>
          <Nav className="ms-auto">
            <Dropdown nav isOpen={this.state.notificationsOpen} toggle={this.toggleNotifications} id="basic-nav-dropdown" className={`${s.notificationsMenu}`}>
              <DropdownToggle nav caret className={s.headerSvgFlipColor}>
                <span className={`${s.avatar} rounded-circle float-start me-3`}>
                  {avatar ? (
                    <img src={avatar} onError={e => e.target.src = adminDefault} alt="..." title={user && (user.firstName || user.email)} />
                  ) : user && user.role === 'admin' ? (
                    <img src={adminDefault} onError={e => e.target.src = adminDefault} alt="..." title={user && (user.firstName || user.email)} />
                  ) : <span title={user && (user.firstName || user.email)}>{firstUserLetter}</span>
                  }
                </span>
                <span className={`small m-2 d-sm-down-none ${s.headerTitle} ${this.props.sidebarStatic ? s.adminEmail : ''}`}>{user ? (user.firstName || user.email) : "Philip smith"}</span>
                <span className="m-1 circle bg-light-red text-white fw-semi-bold d-sm-down-none">13</span>
              </DropdownToggle>
              {/* <DropdownMenu end className={`${s.notificationsWrapper} py-0 animated animated-fast fadeInUp`}>
                <Notifications />
              </DropdownMenu> */}
            </Dropdown>
            <Dropdown nav isOpen={this.state.menuOpen} toggle={this.toggleMenu} className="tutorial-dropdown pr-4">
              <DropdownToggle nav className={`${s.mobileCog}`}>
                <span className={`${s.headerSvgFlipColor}`}>
                  <Settings />
                </span>
              </DropdownToggle>
              <DropdownMenu end className={`${s.headerDropdownLinks} super-colors`}>
                <DropdownItem href="#"><span className={s.headerDropdownIcon}><UserDefault /></span> My Account</DropdownItem>
                <DropdownItem divider />
                <DropdownItem href="#"><span className={s.headerDropdownIcon}><CalendarIcon /></span>Calendar</DropdownItem>
                <DropdownItem href="#"><span className={s.headerDropdownIcon}><EnvelopeBlack /></span>Inbox &nbsp;&nbsp;</DropdownItem>
                <DropdownItem divider />
                <DropdownItem onClick={this.handleLogoutClick}><span className={s.headerDropdownIcon}><PowerButton /></span> Log Out</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </Nav>
        </div>
        <Modal size="sm" isOpen={this.state.isLogout} backdrop >
          <ModalHeader
            toggle={() => {
              this.setState({
                isLogout: false
              })
            }}
          >
            Confirm Logout
          </ModalHeader>
          <ModalBody className="bg-white">
            Are you sure you want to logout?
          </ModalBody>
          <ModalFooter>
            <Button
              color="secondary"
              onClick={() => {
                this.setState({
                  isLogout: false
                })
              }}
            >
              Cancel
            </Button>
            <Button color="primary" onClick={this.doLogout}>
              Logout
            </Button>
          </ModalFooter>
        </Modal>
      </Navbar>
    );
  }
}

function mapStateToProps(store) {
  return {
    sidebarOpened: store.navigation.sidebarOpened,
    sidebarStatic: store.navigation.sidebarStatic,
    navbarType: store.layout.navbarType,
    navbarColor: store.layout.navbarColor,
    openUsersList: store.chat.openUsersList,
    currentUser: store.auth.currentUser,
    logOut: store.auth
  };
}

export default withRouter(connect(mapStateToProps)(Header));
