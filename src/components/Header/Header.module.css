/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.root {
  z-index: 100;
  background: var(--navbar-bg);
  box-shadow: var(--navbar-shadow);
  height: 60px;
  transition: background-color 0.2s ease, margin 0.2s ease;
}
.root.navbarFloatingType {
  margin: 1rem 40px 0;
  border-radius: 0.3rem;
}
@media (max-width: 575.98px) {
  .root.navbarFloatingType {
    margin-left: 15px;
    margin-right: 15px;
  }
}
.root a {
  color: #495057;
}
@media (max-width: 575.98px) {
  .root {
    padding: 7px 0;
  }
}
.root :global .input-group {
  overflow: hidden;
}
.root :global .input-group-prepend-icon {
  background-color: var(--input-bg-color);
  transition: background-color ease-in-out 0.15s;
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px;
}
.root :global .form-control {
  background: var(--input-bg-color);
  color: var(--input-color);
}
.root :global .form-control::-moz-placeholder {
  color: var(--input-color);
}
.root :global .form-control:-ms-input-placeholder {
  color: var(--input-color);
}
.root :global .form-control::placeholder {
  color: var(--input-color);
}
.root :global .form-control.focus {
  background-color: var(--input-bg);
}
.root :global .focus .input-group-prepend {
  background-color: var(--input-bg);
}

.logo {
  font-size: 16px;
}

@media (max-width: 575.98px) {
  .mobileCog {
    padding: 0.5rem 0 !important;
  }
}

.headerTitle {
  color: var(--input-color);
}

.navbarForm {
  padding: 6px 0 6px 1rem;
  margin-left: 10px;
  display: inline-block;
  top: 2px;
  width: auto;
}
.navbarForm .inputAddon {
  position: relative;
  display: inline;
  border: none;
  background-color: #fff;
  transition: background-color ease-in-out 0.15s;
}
.navbarForm input {
  border: none;
  padding: 0.6rem 0.85rem 0.6rem 0;
  display: inline !important;
  width: 250px !important;
  top: 2px;
}

.chatNotification {
  position: absolute;
  right: 35px;
  top: 50px;
  z-index: 20;
  margin-top: 3px;
  padding: 5px 0;
  cursor: pointer;
  opacity: 0;
  pointer-events: none;
}
.chatNotification::before {
  content: " ";
  position: absolute;
  top: 0;
  right: 18px;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid #3c484f;
}
.chatNotification .chatNotificationInner {
  min-width: 120px;
  padding: 8px;
  font-size: 12px;
  border-radius: 0.25rem;
  text-decoration: none;
  background-color: #3c484f;
  color: #fff;
}
.chatNotification .text {
  margin-top: 5px;
  margin-bottom: 0;
  color: #798892;
}
.chatNotification .title {
  margin: 0;
  font-weight: 600;
  line-height: 28px;
  font-size: 0.875rem;
}
.chatNotification .title span {
  margin-right: 7px;
}

.navbarBrand {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 700;
  font-size: 1.25rem;
  pointer-events: none;
}
.navbarBrand i {
  font-size: 10px;
}

.notificationsMenu :global .dropdown-menu {
  left: auto !important;
  right: 0 !important;
  top: 20px !important;
}
@media (max-width: 575.98px) {
  .notificationsMenu :global .dropdown-menu {
    right: -40px !important;
  }
}

.notificationsWrapper {
  width: -webkit-min-content;
  width: -moz-min-content;
  width: min-content;
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  height: 40px;
  width: 40px;
  background: #EBB834;
  font-weight: 600;
  font-size: 18px;
}
.avatar img {
  height: 100%;
}

.headerSvgFlipColor {
  color: var(--navbar-icon-bg) !important;
}
.headerSvgFlipColor :global .bg-primary {
  background-color: var(--navbar-icon-bg) !important;
}

.headerDropdownLinks {
  margin: 15px !important;
  color: inherit;
}
.headerDropdownLinks > a, .headerDropdownLinks > button {
  display: flex;
  padding: 18px 22px;
}

.headerDropdownIcon {
  display: flex;
  margin-right: 8px;
  color: var(--sidebar-icon-bg);
}

.toggleSidebar {
  margin-left: 30px;
}

.chatNotificationInit {
  opacity: 1;
  pointer-events: initial;
}

@media (max-width: 1020px) {
  .headerSearchInput {
    margin-left: 0 !important;
  }
}
.headerSearchInput {
  margin-left: 100px;
  padding-top: 10px;
}

@media (max-width: 950px) {
  .adminEmail {
    display: none;
  }
  .headerSearchInput {
    width: 180px;
  }
}
.navbar > .nav {
  height: auto;
}/*# sourceMappingURL=Header.module.css.map */