import moment from "moment";
import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory, useParams } from "react-router";
import { toast } from "react-toastify";
import { addSlot, updateSlot } from "../../Services/actions/slotAction";

const AddSlot = (props) => {
  const history = useHistory();
  const params = useParams();
  const [state, setState] = useState({
    day: "",
    from: moment().format("HH:mm"),
    to: moment().format("HH:mm"),
  });

  const dayList = [
    {
      id: 0,
      value: "Sunday",
    },
    {
      id: 1,
      value: "Monday",
    },
    {
      id: 2,
      value: "Tuesday",
    },
    {
      id: 4,
      value: "Wednesday",
    },
    {
      id: 5,
      value: "Thursday",
    },
    {
      id: 6,
      value: "Friday",
    },
    {
      id: 7,
      value: "Saturday",
    },
  ];

  const [error, setError] = useState({
    day: false,
    from: false,
    to: false,
  });

  useEffect(() => {
    if (!!params.id && props.getSlotReducer.success) {
      const data = props.getSlotReducer.data;
      const details = data.find((e) => {
        return parseInt(e.id) === parseInt(params.id);
      });

      setState({
        day: details.day,
        from: moment(details.from_time, "HH:mm:ss").format("HH:mm"),
        to: moment(details.to_time, "HH:mm:ss").format("HH:mm"),
      });
    }
  }, [params, props.getSlotReducer]);

  const handleInput = (e) => {
    const { name, value } = e.target;

    setState((pre) => ({
      ...pre,
      [name]: value,
    }));

    switch (name) {
      case "day":
        if (value === "") {
          setError((pre) => ({
            ...pre,
            day: "* Please select time",
          }));
        } else {
          setError((pre) => ({
            ...pre,
            day: false,
          }));
        }
        break;

      case "from":
        if (value === "") {
          setError((pre) => ({
            ...pre,
            from: "* Please select time",
          }));
        } else {
          setError((pre) => ({
            ...pre,
            from: false,
          }));
        }
        break;

      case "to":
        if (value === "") {
          setError((pre) => ({
            ...pre,
            to: "* Please select time",
          }));
        } else if (moment(value, "hh:mm") <= moment(state.from, "hh:mm")) {
          setError((pre) => ({
            ...pre,
            to: "* Time not greter than From time",
          }));
        } else {
          setError((pre) => ({
            ...pre,
            to: false,
          }));
        }
        break;

      default:
        break;
    }
  };

  const handleAddSlot = () => {
    if (state.day === "") {
      setError((pre) => ({
        ...pre,
        day: "* Please select day",
      }));
    } else if (state.from === "") {
      setError((pre) => ({
        ...pre,
        from: "* Please select time",
      }));
    } else if (state.to === "") {
      setError((pre) => ({
        ...pre,
        to: "* Please select time",
      }));
    }

    if (state.day !== "" && !error.day && !error.from && !error.to) {
      if (!!params.id) {
        props.updateSlot({
          details: {
            day: state.day,
            from_time: moment(state.from, "HH:mm").format("HH:mm:ss"),
            to_time: moment(state.to, "HH:mm").format("HH:mm:ss"),
          },
          id: params.id,
        });
      } else {
        props.addSlotList({
          day: state.day,
          from_time: moment(state.from, "HH:mm").format("HH:mm:ss"),
          to_time: moment(state.to, "HH:mm").format("HH:mm:ss"),
        });
      }
    }
  };

  useEffect(() => {
    if (props.addSlotReducer.success) {
      toast.success("Slot added successfull....");
      history.push("/request");
    }
  }, [props.addSlotReducer]);

  useEffect(() => {
    if (props.updateSlotReducer.success) {
      toast.success("Slot updated successfull....");
      history.push("/request");
    }
  }, [props.updateSlotReducer]);

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Add - <span className="fw-semi-bold">Slot</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">Add Slot</h4>

          <div className="col-ld-3 col-md-6">
            <div className="mb-4">
              <label className="form-label">Date</label>
              <select
                className="form-select"
                value={state.day}
                name="day"
                onChange={handleInput}
              >
                <option value={""}>Select Day</option>
                {dayList.map((e, i) => {
                  return (
                    <option key={i} value={e.id}>
                      {e.value}
                    </option>
                  );
                })}
              </select>
              {error.day && <h6 className="text-danger">{error.day}</h6>}
            </div>
            <div className="row">
              <div className="col-md-6 mb-4">
                <label className="form-label">From</label>
                <input
                  name="from"
                  value={state.from}
                  placeholder=""
                  type="time"
                  className="form-control"
                  onChange={handleInput}
                />
                {error.from && <h6 className="text-danger">{error.from}</h6>}
              </div>
              <div className="col-md-6 mb-4">
                <label className="form-label">To</label>
                <input
                  name="to"
                  placeholder=""
                  value={state.to}
                  type="time"
                  className="form-control"
                  onChange={handleInput}
                />
                {error.to && <h6 className="text-danger">{error.to}</h6>}
              </div>
            </div>
            <button className="btn btn-primary" onClick={handleAddSlot}>
              {!!params.id ? "Update" : "Submit"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

const mapStateToProp = (state) => ({
  addSlotReducer: state.slotReducer.addSlot,
  getSlotReducer: state.slotReducer.getSlot,
  updateSlotReducer: state.slotReducer.updateSlot,
});

const mapDispatchToProps = (dispatch) => ({
  addSlotList: (details) => dispatch(addSlot(details)),
  updateSlot: (details) => dispatch(updateSlot(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddSlot);
