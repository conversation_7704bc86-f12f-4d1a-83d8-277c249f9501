import React, { useEffect, useState } from "react";
import { Dropdown, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Mo<PERSON>Footer } from "react-bootstrap";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { toast } from "react-toastify";
import {
  Button,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  ModalHeader,
} from "reactstrap";
import {
  changeStatusViewRequestList,
  deleteViewRequestList,
  getViewRequestList,
} from "../../Services/actions/slotAction";
import {
  RESET_REQUEST_LIST_DELETE,
  RESET_REQUEST_LIST_STATUS,
} from "../../Services/Constant";
import Loader from "../Loader/Loader";
import styles from "./requestStyle.module.css";

const ViewRequest = (props) => {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedId, setSelectedId] = useState([]);
  const [isDelete, setIsDelete] = useState(false);
  useEffect(() => {
    if (!props.RequestviewListReducer.success) {
      props.getViewRequestList();
    }
  }, []);

  const dayList = [
    {
      id: 0,
      value: "Sunday",
    },
    {
      id: 1,
      value: "Monday",
    },
    {
      id: 2,
      value: "Tuesday",
    },
    {
      id: 4,
      value: "Wednesday",
    },
    {
      id: 5,
      value: "Thursday",
    },
    {
      id: 6,
      value: "Friday",
    },
    {
      id: 7,
      value: "Saturday",
    },
  ];

  useEffect(() => {
    setLoading(props.RequestviewListReducer.loading);
    if (props.RequestviewListReducer.success) {
      const data = props.RequestviewListReducer.data;
      const details = data.map((e, i) => {
        return {
          srNo: i + 1,
          stone_id: e?.product?.stone_id,
          cert_no: e?.product?.cert_no,
          cert_url: e?.product?.cert_url,
          diamond_type_name: e?.product?.diamond_type_name,
          day: !!e?.slot?.day
            ? dayList.find((day) => parseInt(day.id) === parseInt(e?.slot?.day))
                .value
            : "N/A",
          from_time: e?.slot?.from_time,
          to_time: e?.slot?.to_time,
          status: e?.status,
          id: e?.id,
        };
      });
      setState([...details]);
    }
  }, [props.RequestviewListReducer]);

  useEffect(() => {
    if (props.statusRequestListReducer.success) {
      // props.getViewRequestList();
      toast.success("Status changed Successfull....");
      props.resetStatusRequest();
    }
  }, [props.statusRequestListReducer]);
  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center`}>
        <Button
          className="btn-action me-2"
          onClick={() => window.open(value.cert_url, "_blank")}
        >
          <i className="fa-regular fa-eye"></i>
        </Button>
      </div>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };
  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.Status, value.id)}
        >
          {value.status === 1 ? "Accept" : "Reject"}
        </Button>
      </div>
    );
  };

  const handleStatus = async (status, id) => {
    const details = {
      status: parseInt(status) === 1 ? 0 : 1,
    };
    await props.changeStatusViewRequestList({
      details: details,
      id: id,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  const handleSelectRow = (type, item, value) => {
    if (type === "all") {
      //  select all
      if (value === true) {
        const listOfSelected = state.map((e) => {
          return e.id;
        });
        setSelectedId([...listOfSelected]);
      } else {
        setSelectedId([]);
      }
    } else {
      //  select
      if (value === true) {
        const selectedId = item.id;
        setSelectedId((pre) => [...pre, selectedId]);
      } else {
        const idList = [...selectedId];
        const index = idList.findIndex((e) => e === item.id);
        idList.splice(index, 1);
        setSelectedId([...idList]);
      }
    }
  };

  const handleDelete = () => {
    if (selectedId.length !== 0) {
      // props.deleteViewRequestList({
      //   request_id: JSON.stringify(selectedId),
      // });
      setIsDelete(true);
    } else {
      toast.error("select atleast one request to delete..");
    }
    //      delete
  };

  useEffect(() => {
    if (props.viewRequstDeleteReducer.success) {
      props.getViewRequestList();
      toast.success("View request deleted successfully…");
      props.resetDeleteRequest();
    }
  }, [props.viewRequstDeleteReducer]);
  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="text-end mb-3">
            {/* <Link to="/stock/add" className="btn btn-primary">Add New</Link> */}
            <div
              className="btn btn-primary py-2"
              onClick={() =>
                selectedId.length !== 0
                  ? handleDelete()
                  : toast.error("Select atleast one stone for delete...")
              }
            >
              Delete
            </div>
          </div>
          <BootstrapTable
            id={styles.view_request}
            bordered={false}
            tableHeaderClass={styles.headerClass}
            tableBodyClass={styles.bodyClass}
            data={state}
            version="4"
            pagination
            selectRow={{
              mode: "checkbox",
              onSelect: (value, item) => handleSelectRow("select", value, item),
              onSelectAll: (value, item) => handleSelectRow("all", item, value),
            }}
            options={options}
            tableContainerClass={`table-responsive table-striped table-hover`}
          >
            <TableHeaderColumn dataField="srNo">
              <span className="fs-sm">Sr No.</span>
            </TableHeaderColumn>

            <TableHeaderColumn dataField="stone_id" dataSort>
              <span className="fs-sm">Stone ID</span>
            </TableHeaderColumn>

            <TableHeaderColumn dataField="cert_no" dataSort>
              <span className="fs-sm">Certificate NO</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="diamond_type_name" dataSort>
              <span className="fs-sm">Diamond Type</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="day" dataSort>
              <span className="fs-sm">Day</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="from_time" dataSort>
              <span className="fs-sm">From</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="to_time" dataSort>
              <span className="fs-sm">To</span>
            </TableHeaderColumn>

            <TableHeaderColumn
              dataField="status"
              dataFormat={statusChange.bind(this)}
            >
              <span className="fs-sm">Status</span>
            </TableHeaderColumn>

            <TableHeaderColumn
              isKey
              dataField="id"
              dataFormat={actionFormatter.bind(this)}
            >
              <span className="fs-sm text-center d-block">Actions</span>
            </TableHeaderColumn>
          </BootstrapTable>

          <Modal
            size="sm"
            aria-labelledby="contained-modal-title-vcenter"
            centered
            isOpen={isDelete}
            toggle={() => {
              setIsDelete(false);
              setSelectedId([]);
            }}
          >
            <ModalHeader
              toggle={() => {
                setIsDelete(false);
                setSelectedId([]);
              }}
            >
              Confirm delete
            </ModalHeader>
            <ModalBody className="bg-white">
              Are you sure you want to delete this request?
            </ModalBody>
            <ModalFooter>
              <Button
                color="secondary"
                onClick={() => {
                  setIsDelete(false);
                  setSelectedId([]);
                }}
              >
                Cancel
              </Button>
              <Button
                color="primary"
                onClick={() => {
                  props.deleteViewRequestList({
                    request_id: JSON.stringify(selectedId),
                  });
                }}
              >
                Delete
              </Button>
            </ModalFooter>
          </Modal>
        </>
      )}
    </>
  );
};

const mapStateToProp = (state) => ({
  RequestviewListReducer: state.slotReducer.viewRequestList,
  statusRequestListReducer: state.slotReducer.viewRequstStatus,
  viewRequstDeleteReducer: state.slotReducer.viewRequstDelete,
});

const mapDispatchToProps = (dispatch) => ({
  getViewRequestList: () => dispatch(getViewRequestList()),
  changeStatusViewRequestList: (details) =>
    dispatch(changeStatusViewRequestList(details)),
  deleteViewRequestList: (details) => dispatch(deleteViewRequestList(details)),
  resetStatusRequest: () => dispatch({ type: RESET_REQUEST_LIST_STATUS }),
  resetDeleteRequest: () => dispatch({ type: RESET_REQUEST_LIST_DELETE }),
});

export default connect(mapStateToProp, mapDispatchToProps)(ViewRequest);
