import React from "react";
import { Tab, Tabs } from "react-bootstrap";
import SlotMgmt from "./SlotMgmt";
import ViewRequest from "./ViewReqest";

const RequestListing = () => {
  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          View - <span className="fw-semi-bold">Request</span>
        </h2>
      </div>
      <div className="card main-card border-0">
        <div className="card-body">
          <Tabs defaultActiveKey="timeSlot" id="viewRequest">
            <Tab eventKey="timeSlot" title="Manage Slots">
              <SlotMgmt />
            </Tab>
            <Tab eventKey="viewRequest" title="View Request">
              <ViewRequest />
            </Tab>
          </Tabs>
        </div>
      </div>
    </>
  );
};
export default RequestListing;
