import React, { useEffect, useState } from "react";
import { Dropdown, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "react-bootstrap";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect, useSelector } from "react-redux";
import { Link, useHistory } from "react-router-dom";
import { toast } from "react-toastify";
import {
  Button,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  Modal,
  ModalHeader,
} from "reactstrap";
import {
  changeSlotStatus,
  deleteSlot,
  getSlot,
} from "../../Services/actions/slotAction";
import {
  RESET_ADD_SLOT,
  RESET_DELETE_SLOT,
  RESET_SLOT_STATUS,
  RESET_UPDATE_SLOT,
} from "../../Services/Constant";
import Loader from "../Loader/Loader";

const SlotMgmt = (props) => {
  const [state, setState] = useState([]);
  const history = useHistory();

  const [loading, setLoading] = useState(true);

  const a = useSelector((a) => a.slotReducer);

  const [isDelete, setIsDelete] = useState(false);
  const [isDeleteId, setIsDeleteId] = useState(null);

  const dayList = [
    {
      id: 0,
      value: "Sunday",
    },
    {
      id: 1,
      value: "Monday",
    },
    {
      id: 2,
      value: "Tuesday",
    },
    {
      id: 4,
      value: "Wednesday",
    },
    {
      id: 5,
      value: "Thursday",
    },
    {
      id: 6,
      value: "Friday",
    },
    {
      id: 7,
      value: "Saturday",
    },
  ];

  useEffect(() => {
    if (!props.getSlotReducer.success) {
      props.getSlot();
    }
  }, []);

  useEffect(() => {
    setLoading(props.getSlotReducer.loading);
    if (props.getSlotReducer.success) {
      const data = props.getSlotReducer.data;
      const details = data.map((e, i) => {
        return {
          srNo: i + 1,
          day: dayList.find((day) => parseInt(day.id) === parseInt(e.day))
            .value,
          from: e.from_time,
          to: e.to_time,
          status: e.status,
          id: e.id,
        };
      });
      setState([...details]);
    }
  }, [props.getSlotReducer]);

  const handleDelete = () => {
    props.deleteSlot(isDeleteId);
  };

  useEffect(() => {
    if (props.addSlotReducer.success) {
      props.getSlot();
      props.resetAddSlot();
    }
  }, [props.addSlotReducer]);

  useEffect(() => {
    if (props.updateSlotReducer.success) {
      props.getSlot();
      props.resetUpdateSlot();
    }
  }, [props.updateSlotReducer]);

  useEffect(() => {
    if (props.deleteSlotReducer.success) {
      setIsDelete(false);
      setIsDeleteId(null);
      props.getSlot();
      props.resetDeleteSlot();
      toast.success("Slot deleted successfull...");
    } else if (props.deleteSlotReducer.error) {
      const msg = props.deleteSlotReducer.msg;
      setIsDelete(false);
      setIsDeleteId(null);
      props.resetDeleteSlot();
      toast.error(msg);
    }
  }, [props.deleteSlotReducer]);

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center`}>
        {/* <Button
          className="btn-action me-2"
          // onClick={() => dispatch(push(`/users/${cell}`))}
        >
          <i className="fa-regular fa-eye"></i>
        </Button> */}
        <Button
          className="btn-action mx-2"
          onClick={() => history.push(`/request/${value.id}`)}
        >
          <i className="fa-regular fa-pen-to-square"></i>
        </Button>
        <Button
          className="btn-action"
          onClick={() => {
            setIsDelete(true);
            setIsDeleteId(value.id);
          }}
        >
          <i className="fa-regular fa-trash-can"></i>
        </Button>
      </div>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };
  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex `}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.status, value.id)}
        >
          {cell === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const handleStatus = async (status, id) => {
    const details = {
      status: parseInt(status) === 1 ? 0 : 1,
    };
    await props.changeSlotStatus({
      details: details,
      id: id,
    });

    const list = [...state]
    const findItemIndex = state.findIndex(e => e.id === id)
    list[findItemIndex]['status'] = list[findItemIndex]['status'] === 1 ? 0 : 1

    setState([...list])
  };

  useEffect(() => {
    if (props.SlotStatusReducer.success) {
      // props.getSlot();
      props.resetChangeSlotStatus();
    }
  }, [props.SlotStatusReducer]);

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <div>
            <div className="text-end mb-3">
              {/* <Link to="/stock/add" className="btn btn-primary">Add New</Link> */}
              <Link
                to="/request/slot-add"
                className="btn btn-primary py-2"
              >
                Add Slot
              </Link>
            </div>
            <BootstrapTable
              bordered={false}
              data={state}
              version="4"
              pagination
              options={options}
              tableContainerClass={`table-responsive table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="day" dataSort>
                <span className="fs-sm">
                  <span className="fs-sm">Day</span>
                </span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="from">
                <span className="fs-sm">From</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="to">
                <span className="fs-sm">To</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">Status</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          </div>
          <Modal size="sm" isOpen={isDelete} toggle={() => setIsDelete(false)}>
            <ModalHeader
              toggle={() => {
                setIsDelete(false);
                setIsDeleteId(null);
              }}
            >
              Confirm delete
            </ModalHeader>
            <ModalBody className="bg-white">
              Are you sure you want to delete this slot?
            </ModalBody>
            <ModalFooter>
              <Button
                color="secondary"
                onClick={() => {
                  setIsDelete(false);
                  setIsDeleteId(null);
                }}
              >
                Cancel
              </Button>
              <Button color="primary" onClick={handleDelete}>
                Delete
              </Button>
            </ModalFooter>
          </Modal>
        </>
      )}
    </>
  );
};

const mapStateToProp = (state) => ({
  getSlotReducer: state.slotReducer.getSlot,
  addSlotReducer: state.slotReducer.addSlot,
  deleteSlotReducer: state.slotReducer.deleteSlot,
  updateSlotReducer: state.slotReducer.updateSlot,
  SlotStatusReducer: state.slotReducer.statusSlot,
});

const mapDispatchToProps = (dispatch) => ({
  getSlot: () => dispatch(getSlot()),
  deleteSlot: (id) => dispatch(deleteSlot(id)),
  changeSlotStatus: (details) => dispatch(changeSlotStatus(details)),
  resetDeleteSlot: (id) => dispatch({ type: RESET_DELETE_SLOT }),
  resetChangeSlotStatus: (id) => dispatch({ type: RESET_SLOT_STATUS }),
  resetUpdateSlot: () => dispatch({ type: RESET_UPDATE_SLOT }),
  resetAddSlot: (id) => dispatch({ type: RESET_ADD_SLOT }),
});

export default connect(mapStateToProp, mapDispatchToProps)(SlotMgmt);
