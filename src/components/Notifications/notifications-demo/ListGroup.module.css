/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.listGroup {
  display: block;
  height: 320px;
  overflow-y: scroll;
}
.listGroup .listGroupItem:first-child {
  border: none;
}

.listGroupItem {
  transition: background-color 0.15s ease-in-out;
  text-decoration: none;
  color: #495057;
  border-left: none;
  border-right: none;
  display: block;
}
.listGroupItem :global .progress {
  transition: background 0.15s ease-in-out;
}
.listGroupItem :global .progress:hover {
  background: #000;
}
.listGroupItem:hover {
  background-color: #f8f9fa;
}
.listGroupItem:hover :global .progress {
  background: #fff !important;
}
.listGroupItem:first-child {
  border-top: none;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.listGroupItem:last-child {
  border-bottom: none;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.notificationIcon {
  margin-right: 1rem;
  float: left;
}
.notificationIcon::after {
  display: block;
  clear: both;
  content: "";
}/*# sourceMappingURL=ListGroup.module.css.map */