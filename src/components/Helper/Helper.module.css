/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.themeHelper {
  width: 300px;
  position: fixed;
  right: -300px;
  top: 20px;
  bottom: 20px;
  z-index: 101;
  transition: right 0.3s ease-in-out;
}
.themeHelper .helperHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 30px 0;
}
@media (prefers-reduced-motion: reduce) {
  .themeHelper {
    transition: none;
  }
}
.themeHelper :global .abc-radio-secondary input[type=radio]:not(:checked) + label::before {
  background-color: #798892;
}
.themeHelper :global .abc-radio > label {
  padding-left: 25px;
}
.themeHelper :global .abc-radio > label:before, .themeHelper :global .abc-radio > label:after {
  outline: none !important;
  transition: all 0.2s ease-in-out;
  margin-left: 4px;
}
.themeHelper :global .abc-radio-warning input[type=radio]:not(:checked) + label::before {
  background-color: #EBB834;
}
.themeHelper .themeSwitcher {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.themeHelper .themeSwitcher .theme {
  position: relative;
}
.themeHelper .themeSwitcher .theme, .themeHelper .themeSwitcher .theme > label {
  width: 100%;
  height: -webkit-max-content;
  height: -moz-max-content;
  height: max-content;
}
.themeHelper .themeSwitcher .theme > input {
  position: absolute;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  pointer-events: none;
  opacity: 0;
}
.themeHelper .themeSwitcher .theme > label {
  margin: 0;
  border: 1px solid #c1ccd3;
  padding: 3px;
  border-radius: 0.3rem;
  transition: background-color 0.2s ease-in-out;
  cursor: pointer;
  display: block;
}
.themeHelper .themeSwitcher .theme > label:hover {
  background-color: #e9ecef;
}
.themeHelper .themeSwitcher .theme > input:checked + label {
  background-color: #d6dee5;
}
.themeHelper .themeSwitcher .theme .themeImage {
  width: 100%;
}
.themeHelper.themeHelperOpened {
  right: 0;
}
.themeHelper .themeHelperBtn {
  position: absolute;
  top: 7%;
  right: unset;
  margin-left: 20px;
  width: 57.5px;
  transform: translateX(-76px);
  margin-top: -15px;
  cursor: pointer;
  z-index: 0;
  border-radius: 50% 0 0 50%;
  padding: 0.8rem 0.5rem 0.8rem 1rem;
}
.themeHelper .themeHelperBtn, .themeHelper .themeHelperBtn:not(.active) {
  box-shadow: -1px 0 8px rgba(0, 0, 0, 0.2) !important;
}
.themeHelper .themeHelperBtn i {
  -webkit-animation-duration: 6500ms;
          animation-duration: 6500ms;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}
.themeHelper .themeHelperBtn i:first-of-type {
  -webkit-animation-name: spin;
          animation-name: spin;
  margin-right: -1.15rem;
  vertical-align: text-bottom;
}
.themeHelper .themeHelperBtn i:last-of-type {
  -webkit-animation-name: spin-reverse;
          animation-name: spin-reverse;
  vertical-align: 0.875rem;
}
.themeHelper .themeHelperSpinner {
  font-size: 1.75rem;
  line-height: initial;
}
.themeHelper .themeHelperHeader {
  padding-top: 0;
}
.themeHelper .themeHelperHeader h6 {
  margin: auto;
}
.themeHelper .themeHelperContent {
  box-shadow: -1px 0 8px rgba(0, 0, 0, 0.2);
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  overflow-y: scroll;
  height: 100%;
}
.themeHelper .themeHelperContent::-webkit-scrollbar {
  width: 1em;
}
.themeHelper .themeHelperContent::-webkit-scrollbar-track {
  box-shadow: none;
}
.themeHelper .themeHelperContent::-webkit-scrollbar-thumb {
  background-color: #333;
  outline: 1px solid #000;
}
.themeHelper .themeHelperSharing {
  font-size: 1.25rem;
  cursor: pointer;
}
.themeHelper :global .glyphicon {
  vertical-align: top;
}
@-webkit-keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(360deg);
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(360deg);
  }
}
@-webkit-keyframes spin-reverse {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(-360deg);
  }
}
@keyframes spin-reverse {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(-360deg);
  }
}/*# sourceMappingURL=Helper.module.css.map */