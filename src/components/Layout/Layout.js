import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Switch, Route, withRouter, Redirect } from 'react-router';
import { TransitionGroup, CSSTransition } from 'react-transition-group';
import Hammer from 'rc-hammerjs';

import Profile from '../../pages/profile';
import ExtraCalendar from '../../pages/extra/calendar';
import ExtraInvoice from '../../pages/extra/invoice';
import ExtraSearch from '../../pages/extra/search';
import Dashboard from '../../pages/dashboard';
import UserListPage from '../Users/<USER>/UsersListPage';
import { SidebarTypes } from '../../Services/reducers/layout';
import Header from '../Header';
import Sidebar from '../Sidebar';

import { openSidebar, closeSidebar, toggleSidebar } from '../../Services/actions/navigation';
import s from './Layout.module.scss';
import { DashboardThemes } from '../../Services/reducers/layout';
import StockListing from '../Stock/StockListing';
import SalesListing from '../Sales/SalesListing';
// import AddStock from '../Stock/AddStock';
import ShapeList from '../Master/Shape/ShapeList';
import ClarityList from '../Master/Clarity/ClarityLIst';
import ColorList from '../Master/Color/ColorList';
import SizeList from '../Master/Size/SizeList';
import AddClarity from '../Master/Clarity/AddClarity';
import RequestListing from '../ViewRequest/RequestListing';
import CartListing from '../Cart/CartListing';
import AddSlot from '../ViewRequest/AddSlot';
import HoldedList from '../Hold/HoldedList';
import ConfirmedList from '../ConfirmedDiamonds/ConfirmedList';
import Policy from '../Policy/Policy';
import AddPolicy from '../Policy/AddPolicy';
import BreadcrumbHistory from '../BreadcrumbHistory/BreadcrumbHistory';
import AddColor from '../Master/Color/AddColor';
import AddSize from '../Master/Size/AddSize';
import AddShape from '../Master/Shape/AddShape';
import FancyColorList from '../Master/FancyColor/FancyColorList';
import AddFancyColor from '../Master/FancyColor/AddFancyColor';
import FinishList from '../Master/Finish/FinishList';
import AddFinish from '../Master/Finish/AddFinish';
import FluorescenceList from '../Master/Fluorescence/FluorescenceList';
import AddFluorescence from '../Master/Fluorescence/AddFluorescence';
import AddUserList from '../Users/<USER>/AddUserList';
import UserListDetails from '../Users/<USER>/UserListDetails';
import StockDetails from '../Stock/StockDetails';
import MarketingList from '../../pages/Marketing/MarketingList';
import AddMarketing from '../../pages/Marketing/AddMarketing';
import InquiryListing from '../Inquiry/InquiryListing';
import AppoimentList from '../Appoiment/AppoimentList';
import DemandList from '../DemandList/DemandList';
import StoneDetails from '../StoneDetails/StoneDetails';
import AddNewStock from '../Stock/AddNewStock';

class Layout extends React.Component {
  static propTypes = {
    sidebarStatic: PropTypes.bool,
    sidebarOpened: PropTypes.bool,
    dashboardTheme: PropTypes.string,
    dispatch: PropTypes.func.isRequired,
  };

  static defaultProps = {
    sidebarStatic: false,
    sidebarOpened: true,
    dashboardTheme: DashboardThemes.DARK
  };
  constructor(props) {
    super(props);
    this.handleSwipe = this.handleSwipe.bind(this);
  }

  componentDidMount() {
    this.handleResize();
    window.addEventListener('resize', this.handleResize.bind(this));
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.handleResize.bind(this));
  }

  handleResize() {
    if (window.innerWidth <= 768 && this.props.sidebarStatic) {
      this.props.dispatch(toggleSidebar());
    }
  }

  handleSwipe(e) {
    if ('ontouchstart' in window) {
      if (e.direction === 4) {
        this.props.dispatch(openSidebar());
        return;
      }

      if (e.direction === 2 && this.props.sidebarOpened) {
        this.props.dispatch(closeSidebar());
        return;
      }
    }
  }

  render() {
    return (
      <div
        className={[
          s.root,
          this.props.sidebarStatic ? `${s.sidebarStatic}` : '',
          !this.props.sidebarOpened ? s.sidebarClose : '',
          'delight-dashboard',
          `dashboard-${(localStorage.getItem("sidebarType") === SidebarTypes.TRANSPARENT) ? "light" : localStorage.getItem("dashboardTheme")}`,
          `header-${localStorage.getItem("navbarColor") ? localStorage.getItem("navbarColor").replace('#', '') : 'FFFFFF'}`
        ].join(' ')}
      >
        <Sidebar />
        <div className={s.wrap}>
          <Header />
          {/* <Helper /> */}
          <Hammer onSwipe={this.handleSwipe}>
            <main className={s.content}>
              <BreadcrumbHistory url={this.props.location.pathname} />
              {/* <TransitionGroup>
                <CSSTransition
                  key={this.props.location.key}
                  classNames="fade"
                  timeout={200}
                >
        
                </CSSTransition>
              </TransitionGroup> */}
              <CSSTransition
                key={this.props.location.key}
                classNames="fade"
                timeout={200}
              >
                <Switch>
                  {/* <Route path="/app/main" exact render={() => <Redirect to="/app/main/analytics" />} /> */}
                  <Route path="/dashboard" exact component={Dashboard} />

                  {/* user list */}
                  <Route path="/users" exact component={UserListPage} />
                  <Route path="/users/add" exact component={AddUserList} />
                  <Route path="/users/:id/update" exact component={AddUserList} />
                  <Route path="/users/:id" exact component={UserListDetails} />
                  {/* **** */}

                  {/*  stock list */}
                  <Route path="/stock" exact component={StockListing} />
                  <Route path="/stock/add" exact component={AddNewStock} />
                  <Route path="/stock/:id/update" exact component={AddNewStock} />
                  <Route path="/stock/:id" exact component={StockDetails} />
                  {/* **** */}
                  <Route path="/sales" exact component={SalesListing} />

                  <Route path="/stone/:id" exact component={StoneDetails} />



                  {/* master */}
                  <Route path="/clarity" exact component={ClarityList} />
                  <Route path="/clarity/add" exact component={AddClarity} />
                  <Route path="/clarity/:id" exact component={AddClarity} />
                  <Route path="/color" exact component={ColorList} />
                  <Route path="/color/add" exact component={AddColor} />
                  <Route path="/color/:id" exact component={AddColor} />
                  <Route path="/size" exact component={SizeList} />
                  <Route path="/size/add" exact component={AddSize} />
                  <Route path="/size/:id" exact component={AddSize} />
                  <Route path="/shape" exact component={ShapeList} />
                  <Route path="/shape/add" exact component={AddShape} />
                  <Route path="/shape/:id" exact component={AddShape} />
                  <Route path="/fancy-color" exact component={FancyColorList} />
                  <Route path="/fancy-color/add" exact component={AddFancyColor} />
                  <Route path="/fancy-color/:id" exact component={AddFancyColor} />
                  <Route path="/finish" exact component={FinishList} />
                  <Route path="/finish/add" exact component={AddFinish} />
                  <Route path="/finish/:id" exact component={AddFinish} />
                  <Route path="/fluorescence" exact component={FluorescenceList} />
                  <Route path="/fluorescence/add" exact component={AddFluorescence} />
                  <Route path="/fluorescence/:id" exact component={AddFluorescence} />


                  {/* **** */}

                  {/* policy */}
                  <Route path="/policy" exact component={Policy} />
                  <Route path="/policy/add" exact component={AddPolicy} />
                  {/* **** */}

                  {/* Marketing */}
                  <Route path="/marketing" exact component={MarketingList} />
                  <Route path="/marketing/add" exact component={AddMarketing} />
                  {/* **** */}



                  {/* request */}
                  <Route path="/request" exact component={RequestListing} />
                  <Route path="/request/slot-add" exact component={AddSlot} />
                  <Route path="/request/:id" exact component={AddSlot} />
                  {/* *** */}




                  <Route path="/cart" exact component={CartListing} />
                  <Route path="/hold" exact component={HoldedList} />
                  <Route path="/confirm" exact component={ConfirmedList} />
                  <Route path="/inquiry" exact component={InquiryListing} />

                  {/*  appoiment and demand */}
                  <Route path="/appointment" exact component={AppoimentList} />
                  <Route path="/demand-list" exact component={DemandList} />
                  {/* ***** */}


                  {/* <Route path="" exact render={() => <Redirect to="/users" />} /> */}
                  <Route path="/profile" exact component={Profile} />
                  <Route path="/extra" exact render={() => <Redirect to="/extra/calendar" />} />
                  <Route path="/extra/calendar" exact component={ExtraCalendar} />
                  <Route path="/extra/invoice" exact component={ExtraInvoice} />
                  <Route path="/extra/search" exact component={ExtraSearch} />
                </Switch>
              </CSSTransition>
              <footer className={s.contentFooter}>
                Delight Diamonds
              </footer>
            </main>
          </Hammer>
        </div>
      </div>
    );
  }
}

function mapStateToProps(store) {
  return {
    sidebarOpened: store.navigation.sidebarOpened,
    sidebarStatic: store.navigation.sidebarStatic,
    dashboardTheme: store.layout.dashboardTheme,
    navbarColor: store.layout.navbarColor,
    sidebarType: store.layout.sidebarType,
    currentUser: store.auth.currentUser,
  };
}

export default withRouter(connect(mapStateToProps)(Layout));
