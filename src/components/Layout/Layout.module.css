/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.root {
  height: 100%;
  position: relative;
  left: 0;
  transition: left 0.3s ease-in-out;
}

.wrap {
  position: relative;
  min-height: 100vh;
  display: flex;
  margin-left: 50px;
  flex-direction: column;
  left: 180px;
  right: 0;
  transition: left 0.3s ease-in-out, margin-left 0.3s ease-in-out;
}
@media (max-width: 575.98px) {
  .wrap {
    margin-left: 0;
    left: 230px;
  }
}

.sidebarClose div.wrap {
  left: 0;
}

.sidebarStatic .wrap {
  left: 0;
  margin-left: 230px;
}

.content {
  position: relative;
  flex-grow: 1;
  padding: 40px 40px 60px;
  background-color: #F9FBFD;
}
@media (max-width: 575.98px) {
  .content {
    padding: 20px 15px 70px;
  }
}
@media (min-width: 576px) {
  .content {
    -webkit-user-select: auto !important;
       -moz-user-select: auto !important;
        -ms-user-select: auto !important;
            user-select: auto !important;
  }
}

.contentFooter {
  position: absolute;
  bottom: 15px;
}/*# sourceMappingURL=Layout.module.css.map */