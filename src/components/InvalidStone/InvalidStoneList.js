import React, { useState } from 'react'
import InvalidStoneTable from './InvalidStoneTable'

function InvalidStoneList() {

  const [diamondType, setDiamodType] = useState('')

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">Invalid Stone - <span className="fw-semi-bold">List</span></h2>
      </div>
      <div className='card main-card'>
        <div className='card-body'>
          <div className='col-xl-3 col-md-5 col-12'>
            <label className="form-label">Diamond Type</label>
            <select
              className="form-select"
              value={diamondType}
              name="diamondType"
              onChange={(e) => {
                setDiamodType(e.target.value)
              }}>
              <option value={""}>Select Diamond Type</option>
              <option value={"1"}>Natural</option>
              <option value={"2"}>Lab Grown</option>
            </select>
          </div>
          {
            diamondType !== '' && <InvalidStoneTable diamondType={diamondType} />
          }
        </div>
      </div>
    </>
  )
}
export default InvalidStoneList