import moment from "moment";
import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { Button, Dropdown, DropdownItem, DropdownMenu, DropdownToggle, <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalHeader } from "reactstrap";
import { changeDemandListStatus, deleteDemandList, getDemandList } from "../../Services/actions/demandAction";
import { getAllMastersList } from "../../Services/actions/stockAction";
import { RESET_DELETE_DEMAND, RESET_DEMAND_STATUS, } from "../../Services/Constant";
import Loader from "../Loader/Loader";
import styles from "./demandStyle.module.css";
import { toast } from "react-toastify";

function DemandList(props) {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [details, setDetails] = useState(null);
  const [isDelete, setIsDelete] = useState(false);
  const [isDeleteId, setIsDeleteId] = useState(null);
  useEffect(() => {
    if (!props.getDemandListReducer.success) {
      props.getDemandList();
    }
  }, []);

  useEffect(() => {
    if (!props.getAllMastersListReducer.success) {
      props.getAllMastersList();
    }
  }, []);
  useEffect(() => {
    setLoading(props.getAllMastersListReducer.loading);
    if (props.getAllMastersListReducer.success) {
      const data = props.getAllMastersListReducer.data;
      setDetails(data);
    }
  }, [props.getAllMastersListReducer]);

  useEffect(() => {
    setLoading(props.getDemandListReducer.loading);
    if (props.getDemandListReducer.success) {
      const data = props.getDemandListReducer.data.map((e) => {
        return {
          ...e,
          demand: JSON.parse(e.demand),
        };
      });

      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          demand_name: e.name,
          qty: e.quantity ?? "N/A",
          demand_date: moment(e.created_at).format("DD/MM/yyyy HH:mm:ss A"),
          stone_id: e.demand.stone_id ?? "N/A",
          DiamondType:
            !e.demand.diamond_type || e.demand.diamond_type.length === 0
              ? "N/A"
              : e.demand.diamond_type,
          Shape:
            !e.demand.shape_id || e.demand.shape_id.length === 0
              ? "N/A"
              : JSON.parse(e.demand.shape_id),
          Size:
            !e.demand.size_id || e.demand.size_id.length === 0
              ? "N/A"
              : JSON.parse(e.demand.size_id),
          Color:
            !e.demand.color_id || e.demand.color_id.length === 0
              ? "N/A"
              : JSON.parse(e.demand.color_id),
          Clarity:
            !e.demand.clarity_id || e.demand.clarity_id.length === 0
              ? "N/A"
              : JSON.parse(e.demand.clarity_id),
          Cut:
            !e.demand.cut_id || e.demand.cut_id.length === 0
              ? "N/A"
              : JSON.parse(e.demand.cut_id),
          Polish:
            !e.demand.polish_id || e.demand.polish_id.length === 0
              ? "N/A"
              : JSON.parse(e.demand.polish_id),
          Symmentry:
            !e.demand.symmetry_id || e.demand.symmetry_id.length === 0
              ? "N/A"
              : JSON.parse(e.demand.symmetry_id),
          status: e.status,
          id: e.id,
        };
      });
      setState([...list]);
    }
  }, [props.getDemandListReducer]);

  const handleDeleteDemandList = () => {
    props.deleteDemandList(isDeleteId);
  };

  useEffect(() => {
    if (props.deleteDemandListReducer.success) {
      setIsDelete(false);
      setIsDeleteId(null);
      props.getDemandList();
      props.resetDeleteDemandList();
      toast.success("demand deleted successfully");
    }
  }, [props.deleteDemandListReducer]);

  const handleStatus = async (status, id) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeDemandListStatus({
      id: id,
      details: details,
    });
    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;
    setState([...list]);
  };

  useEffect(() => {
    if (props.statusDemandListReducer.success) {
      // props.getDemandList();
      props.resetDemandListStatus();
    }
  }, [props.statusDemandListReducer]);

  const getMasterValue = (value, MasterName, type) => {
    if (!!value && value !== "N/A") {
      if (MasterName === "DiamondType") {
        const list = value.map((e) => {
          const _value = Object.keys(details[MasterName]).map((type) => {
            if (type === e) {
              return details[MasterName][type];
            }
          });
          return _value;
        });

        return list.toString();
      } else {
        const listOfValue = typeof value === "number" ? [`${value}`] : value;
        const _list = details[MasterName].filter((e) => {
          return listOfValue.includes(e.id.toString());
        });
        return _list
          .map((e) => {
            return e.name;
          })
          .toString();
      }
    } else {
      return "N/A";
    }
  };

  const actionFormatter = (cell, value) => {
    return (
        <button
        className="btn-action"
          onClick={() => {
            setIsDelete(true);
            setIsDeleteId(value.id);
          }}
        >
          <i className="fa-regular fa-trash-can" />
        </button>
      
    );
  };

  const getValueFromId = (type, cell, value) => {
    return getMasterValue(cell, type);
  };

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.status, value.id)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Demand - <span className="fw-semi-bold">Listing</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          {loading ? (
            <Loader />
          ) : (
            <>
              {!!details && (
                <BootstrapTable
                  id={styles.view_request}
                  bordered={false}
                  // tableHeaderClass={styles.headerClass}
                  // tableBodyClass={styles.bodyClass}
                  data={state}
                  version="4"
                  pagination
                  // selectRow={{
                  //   mode: "checkbox",
                  //   onSelect: (value, item) =>
                  //     handleSelectRow("select", value, item),
                  //   onSelectAll: (value, item) =>
                  //     handleSelectRow("all", item, value),
                  // }}
                  options={options}
                  search
                  scrollTop={"Bottom"}
                  tableContainerClass={`coustom-table stone-table table-striped table-hover`}
                >
                  <TableHeaderColumn dataField="srNo" width="55px">
                    <span className="fs-sm">Sr No.</span>
                  </TableHeaderColumn>

                  <TableHeaderColumn dataField="demand_name" dataSort width="120px">
                    <span className="fs-sm">Diamond Name</span>
                  </TableHeaderColumn>

                  <TableHeaderColumn dataField="qty" width="55px" dataAlign="center" dataSort>
                    <span className="fs-sm">Qty</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn dataField="demand_date" width="175px">
                    <span className="fs-sm">Demand Date</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn dataField="stone_id" >
                    <span className="fs-sm">Stone ID</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn
                    dataField="DiamondType"
                    width="120px"
                    dataFormat={getValueFromId.bind(this, "DiamondType")}
                  >
                    <span className="fs-sm">Diamond Type</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn
                    dataField="Shape"
                    width="150px"
                    dataFormat={getValueFromId.bind(this, "Shape")}
                  >
                    <span className="fs-sm">Shape</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn
                    dataField="Size"
                    width="75px"
                    dataFormat={getValueFromId.bind(this, "Size")}
                  >
                    <span className="fs-sm">Size</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn
                    dataField="Color"
                    width="75px"
                    dataFormat={getValueFromId.bind(this, "Color")}
                  >
                    <span className="fs-sm">Color</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn
                    dataField="Clarity"
                    dataFormat={getValueFromId.bind(this, "Clarity")}
                  >
                    <span className="fs-sm">Clarity</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn
                    dataField="Cut"
                    dataFormat={getValueFromId.bind(this, "Finish")}
                  >
                    <span className="fs-sm">Cut</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn
                    dataField="Polish"
                    dataFormat={getValueFromId.bind(this, "Finish")}
                  >
                    <span className="fs-sm">Polish</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn
                    dataField="Symmentry"
                    dataFormat={getValueFromId.bind(this, "Finish")}
                  >
                    <span className="fs-sm">Symmentry</span>
                  </TableHeaderColumn>
                  <TableHeaderColumn
                    dataField="status"
                    dataFormat={statusChange.bind(this)}
                    width="100px"
                    >
                    <span className="fs-sm">status</span>
                  </TableHeaderColumn>

                  <TableHeaderColumn
                    isKey
                    dataField="id"
                    dataFormat={actionFormatter.bind(this)}
                    dataAlign="center"
                  >
                    <span className="fs-sm text-center d-block">Actions</span>
                  </TableHeaderColumn>
                </BootstrapTable>
              )}
            </>
          )}
        </div>
      </div>
      <Modal size="sm" isOpen={isDelete} toggle={() => setIsDelete(false)}>
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setIsDeleteId(null);
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this demand?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setIsDeleteId(null);
            }}
          >
            Cancel
          </Button>
          <Button color="primary" onClick={handleDeleteDemandList}>
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
}

const mapStateToProp = (state) => ({
  getDemandListReducer: state.demandReducer.getDemandList,
  getAllMastersListReducer: state.stockReducer.getAllMastersList,
  deleteDemandListReducer: state.demandReducer.deleteDemandList,
  statusDemandListReducer: state.demandReducer.statusDemandList,
});

const mapDispatchToProps = (dispatch) => ({
  getDemandList: () => dispatch(getDemandList()),
  getAllMastersList: () => dispatch(getAllMastersList()),
  deleteDemandList: (id) => dispatch(deleteDemandList(id)),
  changeDemandListStatus: (details) =>
    dispatch(changeDemandListStatus(details)),
  resetDeleteDemandList: (id) => dispatch({ type: RESET_DELETE_DEMAND }),
  resetDemandListStatus: (id) => dispatch({ type: RESET_DEMAND_STATUS }),
});

export default connect(mapStateToProp, mapDispatchToProps)(DemandList);
