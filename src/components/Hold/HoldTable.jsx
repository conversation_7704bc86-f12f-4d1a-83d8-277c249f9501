import React, { useEffect, useState } from "react";
import {
  BootstrapTable,
  SearchField,
  TableHeaderColumn,
} from "react-bootstrap-table";
import { connect } from "react-redux";
import { useHistory } from "react-router";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  Modal,
  ModalBody,
  Modal<PERSON>ooter,
  ModalHeader,
} from "reactstrap";
import {
  RESET_DELETE_HOLD_STONE,
  RESET_GET_HOLD_DIAMOND_USER,
} from "../../Services/Constant";
import {
  deleteHoldStone,
  getHoldDiamondUser,
} from "../../Services/actions/holdDiamondList";
import { toast } from "react-toastify";

const HoldTable = (props) => {
  const history = useHistory();
  const [state, setState] = useState([]);
  const [selectedId, setSelectedId] = useState([]);

  const [isDelete, setIsDelete] = useState(false);
  const [deletedId, setDeletedId] = useState([]);

  // useEffect(() => {
  //   if (props.userId) {
  //     props.getHoldDiamondUser(props.userId);
  //   }
  // }, [props.userId]);

  useEffect(() => {
    props.selectedItem(selectedId.length);
    const Amount = selectedId
      .map((e) => {
        if (e.amount !== "N/A") {
          return e.amount;
        } else return 0;
      })
      .reduce((a, b) => a + b, 0);
    const rapRate = selectedId
      .map((e) => {
        if (e.rapRate !== "N/A") {
          return e.rapRate;
        } else return 0;
      })
      .reduce((a, b) => a + b, 0);
    const disc =
      selectedId
        .map((e) => {
          if (e.disc !== "N/A") {
            return e.disc;
          } else return 0;
        })
        .reduce((a, b) => a + b, 0) / selectedId.length;
    props.setSelectedAmount(Amount);
    props.setSelectedRapAmount(rapRate);
    props.setSelectedDisc(isNaN(disc) ? 0 : disc);
  }, [selectedId, props.userId]);

  useEffect(() => {
    if (props.getHoldDiamondUserListReducer.success) {
      const data = props.getHoldDiamondUserListReducer.data;
      const details = data.map((e, i) => {
        return {
          srNo: i + 1,
          stone_id: !!e?.product ? e?.product.stone_id : "N/A",
          cert: !!e?.product ? e?.product.cert : "N/A",
          cert_no: !!e?.product ? e?.product.cert_no : "N/A",
          cert_url: !!e?.product ? e?.product.cert_url : "N/A",
          diamond_type_name: !!e?.product
            ? e?.product.diamond_type_name
            : "N/A",
          image: !!e?.product ? e?.product.image : "N/A",
          video: !!e?.product ? e?.product.video : "N/A",
          amount: !!e?.product ? e?.product.amount : "N/A",
          rapRate: !!e?.product ? e?.product.rapo_rate : "N/A",
          disc: !!e?.product ? e?.product.discount : "N/A",
          shape: !!e?.product ? e?.product.shape_name : "N/A",
          product_id: e?.product_id,
          id: e.id,
        };
      });

      setState([...details]);
      // setSelectedId([]);
      props.resetHoldDiamondUser();
    }
  }, [props.getHoldDiamondUserListReducer]);

  const handleSelectRow = (type, item, value) => {
    if (type === "all") {
      //  select all
      if (value === true) {
        const listOfSelected = state.map((e) => {
          return e;
        });
        const idList = state.map((e) => {
          return e.id;
        });
        setSelectedId([...listOfSelected]);
        setDeletedId([...idList]);
      } else {
        setSelectedId([]);
        setDeletedId([]);
      }
    } else {
      //  select
      if (value === true) {
        setSelectedId((pre) => [...pre, item]);
        setDeletedId((pre) => [...pre, item.id]);
      } else {
        const idList = [...selectedId];
        const deleteList = [...deletedId];
        const index = idList.findIndex((e) => e.id === item.id);
        const _index = deleteList.findIndex((e) => e === item.id);
        idList.splice(index, 1);
        deleteList.splice(_index, 1);
        setSelectedId([...idList]);
        setDeletedId([...deleteList]);
      }
    }
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const createCustomSearchField = (props) => {
    return <SearchField className="mb-sm-5 me-1" placeholder="Search" />;
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center`}>
        <div
          onClick={() => window.open(value.image, "_blank")}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-image"></i>
        </div>
        <div
          onClick={() => window.open(value.video, "_blank")}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-file-video"></i>
        </div>
        <div
          onClick={() => window.open(value.cert_url, "_blank")}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-file"></i>
        </div>
        <div
          // preview
          onClick={() => history.push(`/stone/${value.product_id}`)}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-eye"></i>
        </div>

        <div
          // preview
          onClick={() => {
            setIsDelete(true);
            setDeletedId([value.id]);
          }}
          className="mx-2 text-danger"
          style={{ cursor: "pointer" }}
        >
          <i className="fa fa-trash"></i>
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (props.deleteHoldStoneReducer.success) {
      setIsDelete(false);
      setSelectedId([]);
      setDeletedId([]);
      toast.success("Delete stone successfully...");
      props.resetDeleteHold();
      props.getHoldDiamondUser(props.userId);
    }
  }, [props.deleteHoldStoneReducer]);

  return (
    <>
      {selectedId.length !== 0 && (
        <div className="text-end mb-3">
          {/* <Link to="/stock/add" className="btn btn-primary">Add New</Link> */}
          <div
            className="btn btn-primary py-2"
            onClick={() => setIsDelete(true)}
          >
            Delete
          </div>
        </div>
      )}
      <BootstrapTable
        bordered={false}
        data={state}
        version="4"
        pagination
        selectRow={{
          mode: "checkbox",
          onSelect: (value, item) => handleSelectRow("select", value, item),
          onSelectAll: (value, item) => handleSelectRow("all", item, value),
          selected: selectedId.map((e) => {
            return e.id;
          }),
        }}
        options={options}
        search
        scrollTop={"Bottom"}
        tableContainerClass={`table-responsive table-striped table-hover`}
      >
        <TableHeaderColumn dataField="srNo">
          <span className="fs-sm">Sr No.</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="stone_id" dataSort>
          <span className="fs-sm">Stone ID</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="cert" dataSort>
          <span className="fs-sm">Certificate</span>
        </TableHeaderColumn>
        <TableHeaderColumn dataField="cert_no" dataSort>
          <span className="fs-sm">Certificate NO</span>
        </TableHeaderColumn>
        <TableHeaderColumn dataField="diamond_type_name" dataSort>
          <span className="fs-sm">Diamond Type</span>
        </TableHeaderColumn>
        <TableHeaderColumn dataField="shape" dataSort>
          <span className="fs-sm">Shape</span>
        </TableHeaderColumn>

        <TableHeaderColumn
          isKey
          dataField="id"
          dataFormat={actionFormatter.bind(this)}
        >
          <span className="fs-sm text-center d-block">Actions</span>
        </TableHeaderColumn>
      </BootstrapTable>

      <Modal
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        isOpen={isDelete}
        toggle={() => {
          setIsDelete(false);
          setSelectedId([]);
          setDeletedId([]);
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setSelectedId([]);
            setDeletedId([]);
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this stones?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setSelectedId([]);
              setDeletedId([]);
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              props.deleteHoldStone({
                hold_id: JSON.stringify(deletedId),
              });
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

const mapStateToProp = (state) => ({
  getHoldDiamondUserListReducer:
    state.holdDiamondReducer.getHoldDiamondUserList,
  deleteHoldStoneReducer: state.holdDiamondReducer.deleteHoldStone,
});

const mapDispatchToProps = (dispatch) => ({
  getHoldDiamondUser: (details) => dispatch(getHoldDiamondUser(details)),
  deleteHoldStone: (details) => dispatch(deleteHoldStone(details)),
  resetHoldDiamondUser: () => dispatch({ type: RESET_GET_HOLD_DIAMOND_USER }),
  resetDeleteHold: () => dispatch({ type: RESET_DELETE_HOLD_STONE }),
});

export default connect(mapStateToProp, mapDispatchToProps)(HoldTable);
