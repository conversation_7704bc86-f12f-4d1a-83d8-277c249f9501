// import * as dataFormat from 'components/Users/<USER>/UsersDataFormatters';
import React, { useEffect, useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Dropdown, DropdownMenu, DropdownToggle, Dropdown<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalFooter, } from 'reactstrap';
import { BootstrapTable, TableHeaderColumn, SearchField } from 'react-bootstrap-table';
import { Link, useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';
import Loader from '../Loader/Loader';
import { MerchantCountryList, changeMerchantListStatus, deleteMerchantList, getMerchantList } from '../../Services/actions/merchantListAction';
import { RESET_DELETE_MERCHANT_LIST } from '../../Services/Constant';
import { RESET_MERCHANT_LIST_STATUS } from '../../Services/Constant';
import { RESET_ADD_MERCHANT_LIST } from '../../Services/Constant';
import { RESET_UPDATE_MERCHANT_LIST } from '../../Services/Constant';

const MerchantListTable = (props) => {

    const history = useHistory()

    const [state, setState] = useState([])
    const [loading, setLoading] = useState(true)

    const [details, setDetails] = useState(null)

    const [isDelete, setIsDelete] = useState(false)
    const [isDeleteId, setIsDeleteId] = useState(null)

    useEffect(() => {
        props.MerchantCountryList()
        setLoading(true)
    }, [])




    useEffect(() => {
        if (props.MerchantCountryListReducer.success) {
            const data = props.MerchantCountryListReducer.data
            setDetails({ ...data })
            props.getMerchantList()

        }
    }, [props.MerchantCountryListReducer])



    useEffect(() => {
        if (props.MerchantListReducer.success) {
            const data = props.MerchantListReducer.data
            const _data = data.map(e => {
                return {
                    ...e,
                    business_type: JSON.parse(e.business_type),
                    buying_group: JSON.parse(e.buying_group)
                }
            })
            const list = _data.map((e, i) => {
                return {
                    srNo: i + 1,
                    username: e.username,
                    name: `${e.first_name} ${e.last_name}`,
                    phoneNumber: !!e.mobile && e.mobile !== '' ? e.mobile : 'N/A',
                    other_phone: !!e.other_phone && e.other_phone !== '' ? e.other_phone : 'N/A',
                    email: e.email,
                    city: e.city,
                    company: e.company,
                    country: !!details && details.country.find(country => country.id === e.country_id).name,
                    group_title: e.group_title,
                    job_title: e.job_title,
                    pincode: e.pincode,
                    state: e.state,
                    website: e.website,
                    business_type: e.business_type.map(type => !!details && details.BusinessType[parseInt(type)]),
                    buying_group: e.buying_group.map(type => !!details && details.ByuingGroup[parseInt(type)]),
                    Status: e.status,
                    address: e.address,
                    // about: e.about,
                    id: e.id,
                    username: e.username
                }

            })
            setState([...list])
            setLoading(false)
        }

    }, [props.MerchantListReducer])

    const handleDelete = () => {
        props.deleteMerchantList(isDeleteId)
    }

    useEffect(() => {
        if (props.deleteMerchantListReducer.success) {
            setIsDelete(false)
            setIsDeleteId(null)
            props.getMerchantList()
            props.resetDeleteList()
            toast.success("Merchant deleted successfully")
        }
    }, [props.deleteMerchantListReducer])



    const statusChange = (cell, value) => {
        return (
            <div className={`d-flex justify-content-between`}>
                <Button
                    color={value.Status === 1 ? 'default' : 'secondary'}
                    className="btn btn-rounded-f" type='button'
                    onClick={() => handleStatus(value.Status, value.id)}
                >
                    {cell === 1 ? 'Active' : 'Deactive'}
                </Button>
            </div>
        )
    }

    const handleStatus = async (status, id) => {
        const details = {
            status: status === 1 ? 0 : 1,
        };
        await props.changeMerchantListStatus({
            details: details,
            id: id
        })
        const list = [...state]
        const findItemIndex = state.findIndex(e => e.id === id)
        list[findItemIndex]['Status'] = list[findItemIndex]['Status'] === 1 ? 0 : 1

        setState([...list])



    }

    const websiteUrl = (cell, value) => {
        return (
            <div onClick={() => {
                window.open(cell, '_blank')
            }} style={{
                cursor: 'pointer',
                color: 'blue',
                textDecoration: 'underline'
            }}>{cell}</div>
        )
    }
    useEffect(() => {
        if (props.statusMerchantListReducer.success) {
            // props.getMerchantList()
            props.resetStatus()
        }

    }, [props.statusMerchantListReducer])

    const actionFormatter = (cell, value) => {
        return (
            <div className={`d-flex justify-content-center`}>
                <Button
                    className="btn-action me-2"
                    onClick={() => history.push(`/merchant/${value.id}`)}
                >
                    <i className="fa-regular fa-pencil-square-o"></i>
                </Button>
                <Button
                    className="btn-action"
                    onClick={() => {
                        setIsDelete(true)
                        setIsDeleteId(value.id)
                    }}
                >
                    <i className="fa-regular fa-trash-can"></i>
                </Button>
            </div>
        )
    }


    const renderSizePerPageDropDown = (props) => {
        const limits = [];
        props.sizePerPageList.forEach((limit) => {
            limits.push(<DropdownItem key={limit} onClick={() => props.changeSizePerPage(limit)}>{limit}</DropdownItem>);
        });
        return (
            <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
                <DropdownToggle color="default" caret>
                    {props.currSizePerPage}
                </DropdownToggle>
                <DropdownMenu>
                    {limits}
                </DropdownMenu>
            </Dropdown>
        );
    };

    const createCustomSearchField = (props) => {
        return (
            <SearchField
                className="mb-sm-5 me-1"
                placeholder='Search'
            />
        );
    }

    const options = {
        sizePerPage: 10,
        paginationSize: 5,
        searchField: createCustomSearchField,
        sizePerPageDropDown: renderSizePerPageDropDown,
    };


    useEffect(() => {
        if (props.addMerchantListReducer.success) {
            props.getMerchantList()
            props.resetAddMerchantList()
        }
    }, [props.addMerchantListReducer])

    useEffect(() => {
        if (props.updateMerchantListReducer.success) {
            props.getMerchantList()
            props.resetUpdateMerchantList()
        }
    }, [props.updateMerchantListReducer])


    return (
        <>
            {loading ? <Loader /> : <><div className="text-end mb-4">
                <Link to="/merchant/add" className="btn btn-primary">
                    Add Merchant
                </Link>
            </div>
                <div className='card main-card'>
                    <div className='card-body'>
                        <BootstrapTable bordered={false} data={state} version="4" pagination options={options} search tableContainerClass={`coustom-table stone-table table-striped table-hover`}
                        >
                            <TableHeaderColumn dataField="srNo" width='55px'>
                                <span className="fs-sm">Sr No.</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="username" width='120px' dataSort>
                                <span className="fs-sm">Username</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="name" width='120px' dataSort>
                                <span className="fs-sm">Name</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="phoneNumber" width='110px'>
                                <span className="fs-sm">Phone Number</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="other_phone" width='110px'>
                                <span className="fs-sm">Other Number</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="email" width='230px'>
                                <span className="fs-sm">E-mail</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="company" width='200px'>
                                <span className="fs-sm">Company</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="website" width='200px' dataFormat={websiteUrl.bind(this)}>
                                <span className="fs-sm">Website</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="job_title" width='200px'>
                                <span className="fs-sm">Job Title</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="group_title" width='200px'>
                                <span className="fs-sm">Group Title</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="country" width='150px'>
                                <span className="fs-sm">Country</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="state" width='150px'>
                                <span className="fs-sm">State</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="city" width='175px'>
                                <span className="fs-sm">City</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="pincode">
                                <span className="fs-sm">Pincode</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="business_type" width='250px'>
                                <span className="fs-sm">Bussiness Type</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn dataField="buying_group" >
                                <span className="fs-sm">Buying Group</span>
                            </TableHeaderColumn>

                            {/* <TableHeaderColumn dataField="business_type">
       <span className="fs-sm">Bussiness Type</span>
      </TableHeaderColumn> */}

                            <TableHeaderColumn dataField="Status" width='115px' dataFormat={statusChange.bind(this)}>
                                <span className="fs-sm">Status</span>
                            </TableHeaderColumn>

                            <TableHeaderColumn isKey dataField="id" width='115px' dataFormat={actionFormatter.bind(this)}>
                                <span className="fs-sm text-center d-block">Actions</span>
                            </TableHeaderColumn>
                        </BootstrapTable>
                    </div>
                </div>
            </>}
            <Modal size="sm" isOpen={isDelete} toggle={() => setIsDelete(false)}>
                <ModalHeader toggle={() => {
                    setIsDelete(false)
                    setIsDeleteId(null)
                }}>Confirm delete</ModalHeader>
                <ModalBody className="bg-white">
                    Are you sure you want to delete this merchant?
                </ModalBody>
                <ModalFooter>
                    <Button color="secondary" onClick={() => {
                        setIsDelete(false)
                        setIsDeleteId(null)
                    }}>Cancel</Button>
                    <Button color="primary" onClick={handleDelete}>Delete</Button>
                </ModalFooter>
            </Modal>
        </>
    );

}

const mapStateToProp = (state) => ({
    MerchantListReducer: state.merchantReducer.MerchantList,
    MerchantCountryListReducer: state.merchantReducer.MerchantCountryList,
    deleteMerchantListReducer: state.merchantReducer.deleteMerchantList,
    statusMerchantListReducer: state.merchantReducer.statusMerchantList,
    addMerchantListReducer: state.merchantReducer.addMerchantList,
    updateMerchantListReducer: state.merchantReducer.updateMerchantList,
});

const mapDispatchToProps = (dispatch) => ({
    getMerchantList: () => dispatch(getMerchantList()),
    MerchantCountryList: () => dispatch(MerchantCountryList()),
    deleteMerchantList: (id) => dispatch(deleteMerchantList(id)),
    changeMerchantListStatus: (details) => dispatch(changeMerchantListStatus(details)),
    resetDeleteList: () => dispatch({ type: RESET_DELETE_MERCHANT_LIST }),
    resetStatus: () => dispatch({ type: RESET_MERCHANT_LIST_STATUS }),
    resetAddMerchantList: () => dispatch({ type: RESET_ADD_MERCHANT_LIST }),
    resetUpdateMerchantList: () => dispatch({ type: RESET_UPDATE_MERCHANT_LIST }),
});

export default connect(mapStateToProp, mapDispatchToProps)(MerchantListTable);
