import React, { useEffect, useState } from 'react'
import { connect } from 'react-redux';
import { useHistory, useParams } from 'react-router';
import { Link } from 'react-router-dom';
import { getCountryList } from '../../../Services/actions/usersListActions';

function UserListDetails(props) {
    const params = useParams();
    const history = useHistory();

    const [details, setDetails] = useState(null)
    const [countryName, setCountryName] = useState('')

    useEffect(() => {
        if (!params.id || !params) {
            history.push('/users')
        } else {
            props.getCountryList()
            if (!!params.id && props.getUserListReducer.success) {
                const data = props.getUserListReducer.data;
                const _details = data.find((e) => {
                    return parseInt(e.id) === parseInt(params.id);
                });
                setDetails(_details)
            }
        }
    }, [params])

    useEffect(() => {
        if (props.getCountryReducer.success && !!details) {
            const data = props.getCountryReducer.data
            const name = data.find((e) => e.id === details.country_id).name
            setCountryName(name)
        }
    }, [props.getCountryReducer, details])

    return (
        <>
            <div className="text-end mb-4">
                <Link to={`/users/${params.id}/update`} className="btn btn-primary">
                    Update Details
                </Link>
            </div>
            <div className="page-top-line">
                <h2 className="page-title">
                    User Details
                </h2>
            </div>
            <div className="card main-card">
                <div className="card-body">
                    <h4 className="mb-5 fw-light">
                        User Details
                    </h4>
                    <div className='row'>
                        {!!details && <>
                            <div className="col-ld-3 col-md-6">
                                <div className="mb-4">
                                    <div><b className='fw-bold'>Fullname : </b>{details.fullname !== '' ? details.fullname : 'N/A'}</div>

                                </div>
                                <div className="mb-4">
                                    <div><b className='fw-bold'>Username : </b>{details.username !== '' ? details.username : 'N/A'}</div>
                                </div>
                                <div className="mb-4">
                                    <div><b className='fw-bold'>Email : </b>{details.email !== '' ? details.email : 'N/A'}</div>
                                </div>
                                {/*  country code */}
                                <div className="mb-4">
                                    <div><b className='fw-bold'>Country : </b>{countryName !== '' ? countryName : 'N/A'}</div>
                                </div>
                                {/* **** */}
                                <div className="mb-4">
                                    <div><b className='fw-bold'>Company : </b>{details.company !== '' ? details.company : 'N/A'}</div>
                                </div>
                                {/* mobile whatsapp */}
                                <div className='row'>
                                    <div className='col-md-6 col-12'>
                                        <div className="mb-4">
                                            <div><b className='fw-bold'>Mobile : </b>{(!!details.mobile && details.mobile !== '') ? details.mobile : 'N/A'}</div>
                                        </div>
                                    </div>
                                    <div className='col-md-6 col-12'>
                                        <div className="mb-4">
                                            <div><b className='fw-bold'>Whatsapp No. : </b>{(!!details.whatsapp && details.whatsapp !== '') ? details.whatsapp : 'N/A'}</div>
                                        </div>
                                    </div>
                                </div>

                                {/*  wechat skype */}
                                <div className='row'>
                                    <div className='col-md-6 col-12'>
                                        <div className="mb-4">
                                            <div><b className='fw-bold'>Wechat : </b>{(!!details.wechat && details.wechat !== '') ? details.wechat : 'N/A'}</div>
                                        </div>
                                    </div>
                                    <div className='col-md-6 col-12'>
                                        <div className="mb-4">
                                            <div><b className='fw-bold'>Skype : </b>{(!!details.skype && details.skype !== '') ? details.skype : 'N/A'}</div>
                                        </div>
                                    </div>
                                </div>
                                <button className="btn btn-primary" onClick={() => history.push('/users')} >
                                    Back
                                </button>
                            </div>
                            <div className='col-md-6'>
                                <b className='fw-bold'>Business Proof :</b>
                                {details.document !== '' && details.document !== null ?
                                    <object data={details.document_url} type="application/pdf" width="100%" height="400px">
                                        <p>Alternative text - include a link <a href="myfile.pdf">to the PDF!</a></p>
                                    </object> : " N/A"
                                }
                            </div>
                        </>}
                    </div>
                </div>
            </div>
        </>
    )
}

const mapStateToProp = (state) => ({
    getUserListReducer: state.usersListReducers.userList,
    getCountryReducer: state.CountryReducer.countryDetails,
});

const mapDispatchToProps = (dispatch) => ({
    getCountryList: (details) => dispatch(getCountryList(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(UserListDetails);
