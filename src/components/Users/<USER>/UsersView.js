import React, { Component } from 'react';
import Loader from 'components/Loader';
import ImagesViewItem from 'components/FormItems/items/ImagesViewItem';
import TextViewItem from 'components/FormItems/items/TextViewItem';
import Widget from 'components/Widget';
import { Col, Row } from 'react-bootstrap';

const UsersView = (props) => {
  const loading = props.loading
  const record = props.record
  return (
    <>
      {loading || !record && <Loader />}
      <Widget title={<h4>{'View User'}</h4>}>
        <Row>
          <Col lg={6}>
            <TextViewItem
              label={'username'}
              value="JohnDoe"
            />

            <TextViewItem
              label={'Email'}
              value="<EMAIL>"
            />

            <TextViewItem
              label={'Name'}
              value="John doe"
            />

            <TextViewItem
              label={'Country'}
              value="India"
            />

            <TextViewItem
              label={'Mobile No'}
              value="9086541274"
            />
            <TextViewItem
              label={'WhatsApp'}
              value="9086541274"
            />
            <TextViewItem
              label={'Wechat'}
              value="9086541274"
            />
            <TextViewItem
              label={'Skype'}
              value="9086541274"
            />

          </Col>
          <Col lg={6}>
            {/* <ImagesViewItem
              label={'Avatar'}
              value="https://images.pexels.com/photos/10376259/pexels-photo-10376259.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            /> */}
            {/* <object data="http://localhost:3000/?#/users/98cea92c-84e5-4c0d-ac21-9998f4b23883" type="application/pdf">
              <embed src="http://localhost:3000/?#/users/98cea92c-84e5-4c0d-ac21-9998f4b23883" type="application/pdf" />
            </object> */}
          </Col>
        </Row>
      </Widget>
    </>
  )
}

export default UsersView;
