import React, { useEffect, useState } from 'react'
import { connect } from 'react-redux';
import { useHistory, useParams } from 'react-router';
import { addUserList, getCountryList, getUserList, updateUserList } from '../../../Services/actions/usersListActions';
import { toast } from 'react-toastify';
import { RESET_ADD_USER_LIST, RESET_UPDATE_USER_LIST } from '../../../Services/Constant';

function AddUserList(props) {

    const history = useHistory();
    const params = useParams();



    const validEmailRegex = RegExp(/^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i);
    const validUserNameRegex = RegExp(/^\S*$/i);


    const [countryList, setCountryList] = useState([])
    const [showPass, setShowPass] = useState(false)
    const [upload_document, setUpload_document] = useState(null);
    const initialState = {
        fullname: '',
        username: '',
        email: '',
        password: '',
        country_id: '',
        company: '',
        mobile: '',
        whatsapp: '',
        wechat: '',
        skype: '',
        proof: '',
    }

    const initialError = {
        fullname: false,
        username: false,
        email: false,
        password: false,
        country_id: false,
        company: false,
        mobile: false,
        social: false,
        proof: false
    }

    const [state, setState] = useState(initialState)
    const [error, setError] = useState(initialError)
    useEffect(() => {
        if (!props.getUserListReducer.success) {
            props.getUserList()
        }
    }, [])

    useEffect(() => {
        if (!!params.id && props.getUserListReducer.success) {
            const data = props.getUserListReducer.data;
            const details = data.find((e) => {
                return parseInt(e.id) === parseInt(params.id)
            });
            setState((prevState) => ({
                ...prevState,
                fullname: details.fullname,
                username: details.username,
                email: details.email,
                password: details.password,
                company: details.company,
                country_id: details.country_id,
                mobile: details.mobile,
                whatsapp: details.whatsapp,
                skype: details.skype,
                wechat: details.wechat,
            }));
        }
    }, [params, props.getUserListReducer]);




    const handleInput = (e) => {
        const { name, value } = e.target
        if (name === 'mobile' || name === 'whatsapp') {
            if (RegExp(/^([0-9]{0,10})$/i).test(value)) {
                setState(prevState => ({
                    ...prevState,
                    [name]: value
                }))

            } else return

        } else {
            setState(prevState => ({
                ...prevState,
                [name]: value
            }))
        }

        switch (name) {
            case 'fullname':
                if (value === '') {
                    setError(prevState => ({
                        ...prevState,
                        fullname: '* Please Enter fullname'
                    }))
                } else {
                    setError(prevState => ({
                        ...prevState,
                        fullname: false
                    }))
                } break;
            case 'username':
                if (value === '') {
                    setError(prevState => ({
                        ...prevState,
                        username: '* Please Enter Username'
                    }))
                } else if (!validUserNameRegex.test(value)) {
                    setError(prevState => ({
                        ...prevState,
                        username: '* Username not allow space'
                    }))
                } else {
                    setError(prevState => ({
                        ...prevState,
                        username: false
                    }))
                } break;
            case 'email':
                if (value === '') {
                    setError(prevState => ({
                        ...prevState,
                        email: '* Please Enter email'
                    }))
                } else if (!validEmailRegex.test(value)) {
                    setError(prevState => ({
                        ...prevState,
                        email: '* Please Enter valid email'
                    }))
                } else {
                    setError(prevState => ({
                        ...prevState,
                        email: false
                    }))
                } break;
            case 'password':
                if (value === '') {
                    setError(prevState => ({
                        ...prevState,
                        password: '* Please Enter password'
                    }))
                } else {
                    setError(prevState => ({
                        ...prevState,
                        password: false
                    }))
                } break;
            case 'company':
                if (value === '') {
                    setError(prevState => ({
                        ...prevState,
                        company: '* Please Enter Company'
                    }))
                } else {
                    setError(prevState => ({
                        ...prevState,
                        company: false
                    }))
                } break;
            case 'country_id':
                if (value === '') {
                    setError(prevState => ({
                        ...prevState,
                        country_id: '* Please Select Country'
                    }))
                } else {
                    setError(prevState => ({
                        ...prevState,
                        country_id: false
                    }))
                } break;
            case 'mobile':
                if (value === '') {
                    setError(prevState => ({
                        ...prevState,
                        mobile: '* Please Enter mobile number'
                    }))
                } else {
                    setError(prevState => ({
                        ...prevState,
                        mobile: false
                    }))
                    setState((prevState) => ({
                        ...prevState,
                        mobile: value.replace(/[^0-9 ]/gi, ""),
                    }));
                } break;
            case 'whatsapp':
                if (value !== '') {
                    setError(prevState => ({
                        ...prevState,
                        social: false
                    }))
                } break;
            case 'wechat':
                if (value !== '') {
                    setError(prevState => ({
                        ...prevState,
                        social: false
                    }))
                } break;
            case 'skype':
                if (value !== '') {
                    setError(prevState => ({
                        ...prevState,
                        social: false
                    }))
                } break;
            case "proof":
                if (value === "") {
                    setError((prevState) => ({
                        ...prevState,
                        proof: "Required",
                    }));
                } else if (
                    !(
                        e.target.files[0]?.type === "application/msword" ||
                        e.target.files[0]?.type === "application/pdf"
                    )
                ) {
                    setError((prevState) => ({
                        ...prevState,
                        proof: "Only Accept word and pdf file",
                    }));
                } else {
                    setError((prevState) => ({
                        ...prevState,
                        proof: false,
                    }));
                    setUpload_document(e.target.files[0]);

                }
                break;
            default: break;
        }
    }

    useEffect(() => {
        props.getCountryList()
    }, [])

    useEffect(() => {
        if (props.getCountryReducer.success) {
            const data = props.getCountryReducer.data
            setCountryList([...data])
        }
    }, [props.getCountryReducer])


    const handleAddUserList = () => {

        if (state.fullname === '') {
            setError(prevState => ({
                ...prevState,
                fullname: '* Please Enter Fullname'
            }))
        } if (state.username === '') {
            setError(prevState => ({
                ...prevState,
                username: '* Please Enter Username'
            }))
        }
        if (state.email === '') {
            setError(prevState => ({
                ...prevState,
                email: '* Please Enter Email'
            }))
        } if (state.password === '') {
            setError(prevState => ({
                ...prevState,
                password: '* Please Enter Password'
            }))
        } if (state.company === '') {
            setError(prevState => ({
                ...prevState,
                company: '* Please Enter Company'
            }))
        }
        if (state.proof === '') {
            setError(prevState => ({
                ...prevState,
                proof: '* Please Upload Business Proof'
            }))
        }
        if (state.country_id === '') {
            setError(prevState => ({
                ...prevState,
                country_id: '* Please Select Country'
            }))
        } if (state.mobile === '') {
            setError(prevState => ({
                ...prevState,
                mobile: '* Please Enter Mobile Number'
            }))
        }

        if (state.whatsapp === "" && state.wechat === "" && state.skype === "") {
            setError(prevState => ({
                ...prevState,
                social: '* Fill atleast one social account'
            }))
        }
        if (
            state.fullname !== '' &&
            state.username !== '' &&
            state.email !== '' &&
            state.password !== '' &&
            state.company !== '' &&
            state.country_id !== '' &&
            state.mobile !== '' &&
            state.proof !== '' &&
            (state.whatsapp !== '' ||
                state.wechat !== '' ||
                state.skype !== '') &&
            !error.fullname &&
            !error.username &&
            !error.email &&
            !error.password &&
            !error.company &&
            !error.mobile &&
            !error.country_id &&
            !error.social &&
            !error.proof
        ) {
            const details = {
                fullname: state.fullname,
                username: state.username,
                email: state.email,
                password: state.password,
                company: state.company,
                country_id: state.country_id,
                mobile: state.mobile,
                whatsapp: state.whatsapp,
                skype: state.skype,
                wechat: state.wechat,
                document: upload_document
                
            }
            if (!!params.id) {
                //  update
                props.updateUserList({
                    details: details,
                    id: params.id
                })
            } else {
                //  add
                props.addUserList(details)
            }
        }
    }

    useEffect(() => {
        if (props.addUserListReducer.success) {
            history.push('/users')
            toast.success("User created successfully")
            props.resetAddUserList()
        } else if (props.addUserListReducer.error) {
            const errorMsg = props.addUserListReducer.msg
            if (!!errorMsg && !!errorMsg.username) {
                setError(prevState => ({
                    ...prevState,
                    username: `* ${errorMsg.username[0]}`
                }))
            }
            if (!!errorMsg && !!errorMsg.email) {
                setError(prevState => ({
                    ...prevState,
                    email: `* ${errorMsg.email[0]}`
                }))
            }
            props.resetAddUserList()
        }
    }, [props.addUserListReducer])


    useEffect(() => {
        if (props.updateUserListReducer.success) {
            history.push('/users')
            toast.success("User updated successfully")
            props.resetUpdateUserList()
        } else if (props.updateUserListReducer.error) {
            const errorMsg = props.updateUserListReducer.msg
            if (!!errorMsg && !!errorMsg.username) {
                setError(prevState => ({
                    ...prevState,
                    username: `* ${errorMsg.username[0]}`
                }))
            }
            if (!!errorMsg && !!errorMsg.email) {
                setError(prevState => ({
                    ...prevState,
                    email: `* ${errorMsg.email[0]}`
                }))
            }
            props.resetUpdateUserList()
        }
    }, [props.updateUserListReducer])


    const handleReset = () => {
        setState(initialState);
        setError(initialError);
    };

    return (
        <>
            <div className="page-top-line">
                <h2 className="page-title">
                    {!!params.id ? "Update" : "Add"} -{" "}
                    <span className="fw-semi-bold"> User</span>
                </h2>
            </div>
            <div className="card main-card">
                <div className="card-body">
                    <h4 className="mb-5 fw-light">
                        {!!params.id ? "Update" : "Add"} User
                    </h4>

                    <div className="col-md-6">
                        <div className='row gx-3'>
                            <div className="mb-4 col-md-6">
                                <label className="form-label">Fullname*</label>
                                <input
                                    placeholder="Enter Fullname"
                                    name="fullname"
                                    type="text"
                                    className="form-control"
                                    value={state.fullname}
                                    onChange={handleInput}
                                />
                                {error.fullname && (
                                    <div>
                                        <span className="text-danger h6">{error.fullname}</span>
                                    </div>
                                )}
                            </div>
                            <div className="mb-4 col-md-6">
                                <label className="form-label">Username*</label>
                                <input
                                    placeholder="Enter Username"
                                    name="username"
                                    type="text"
                                    className="form-control"
                                    value={state.username}
                                    onChange={handleInput}
                                />
                                {error.username && (
                                    <div>
                                        <span className="text-danger h6">{error.username}</span>
                                    </div>
                                )}
                            </div>
                            <div className="mb-4 col-md-6">
                                <label className="form-label">Email*</label>
                                <input
                                    placeholder="Enter Email"
                                    name="email"
                                    type="text"
                                    className="form-control"
                                    value={state.email}
                                    onChange={handleInput}
                                />
                                {error.email && (
                                    <div>
                                        <span className="text-danger h6">{error.email}</span>
                                    </div>
                                )}
                            </div>
                            {!params.id && <div className="mb-4 col-md-6">
                                <label className="form-label">Password*</label>
                                <div className='position-relative'>
                                    <input
                                        placeholder="Enter Password"
                                        name="password"
                                        type={showPass ? 'text' : 'password'}
                                        className="form-control"
                                        value={state.password}
                                        onChange={handleInput}
                                        style={{ paddingRight: '50px +' }}
                                    />
                                    <div className='position-absolute' style={{
                                        top: '8px',
                                        right: '25px',
                                        cursor: 'pointer'
                                    }} onClick={() => {
                                        setShowPass(!showPass)
                                    }}><i className={`fa-regular ${showPass ? 'fa-eye' : 'fa-eye-slash'}`}></i></div>
                                </div>
                                {error.password && (
                                    <div>
                                        <span className="text-danger h6">{error.password}</span>
                                    </div>
                                )}
                            </div>}

                            {/*  country code */}
                            <div className="mb-4 col-md-6">
                                <label className="form-label">Country</label>
                                <select
                                    className="form-select"
                                    name="country_id"
                                    value={state.country_id}
                                    onChange={handleInput}
                                >
                                    <option value="">Select Country</option>
                                    {
                                        countryList.map((e, i) => {
                                            return (
                                                <option value={e.id} key={i}>{e.name}</option>
                                            )
                                        })
                                    }
                                </select>
                                {error.country_id && (
                                    <div>
                                        <span className="text-danger h6">{error.country_id}</span>
                                    </div>
                                )}
                            </div>
                            <div className="mb-4 col-md-6">
                                <label className="form-label">Company</label>
                                <input
                                    placeholder="Enter company"
                                    name="company"
                                    type="text"
                                    className="form-control"
                                    value={state.company}
                                    onChange={handleInput}
                                />
                                {error.company && (
                                    <div>
                                        <span className="text-danger h6">{error.company}</span>
                                    </div>
                                )}
                            </div>
                            {/* mobile whatsapp */}
                            <div className="mb-4 col-md-6">
                                <label className="form-label">Mobile*</label>
                                <input
                                    placeholder="Enter Mobile"
                                    name="mobile"
                                    type="text"
                                    className="form-control"
                                    value={state.mobile}
                                    maxLength={10}
                                    onChange={handleInput}
                                />
                                {error.mobile && (
                                    <div>
                                        <span className="text-danger h6">{error.mobile}</span>
                                    </div>
                                )}
                            </div>
                            <div className='col-12'>
                                <h5 className='my-3 fw-bold'>Social Account</h5>
                            </div>
                            {/*  Whatsapp */}
                            <div className="mb-4 col-md-6">
                                <label className="form-label">Whatsapp No.</label>
                                <input
                                    placeholder="Enter whatsapp number"
                                    name="whatsapp"
                                    type="text"
                                    className="form-control"
                                    value={state.whatsapp}
                                    maxLength="10"
                                    onChange={handleInput}
                                />
                            </div>
                            {/*  wechat skype */}
                            <div className="mb-4 col-md-6">
                                <label className="form-label">Wechat</label>
                                <input
                                    placeholder="Enter wechat"
                                    name="wechat"
                                    type="text"
                                    className="form-control"
                                    value={state.wechat}
                                    onChange={handleInput}
                                />
                                {/* {error.wechat && (
                                        <div>
                                            <span className="text-danger h6">{error.wechat}</span>
                                        </div>
                                    )} */}
                            </div>
                            <div className="mb-4 col-md-6">
                                <label className="form-label">Skype</label>
                                <input
                                    placeholder="Enter skype"
                                    name="skype"
                                    type="text"
                                    className="form-control"
                                    value={state.skype}
                                    onChange={handleInput}
                                />
                                {/* {error.skype && (
                                        <div>
                                            <span className="text-danger h6">{error.skype}</span>
                                        </div>
                                    )} */}
                            </div>
                            {error.social && (
                                <div>
                                    <span className="text-danger h6">{error.social}</span>
                                </div>
                            )}
                            <div className='col-12'>
                                <h5 className='my-3 fw-bold'>Upload Document</h5>
                            </div>
                            <div className="mb-4 col-md-12">
                                <label className="form-label">Business Proof Document</label>
                                <input
                                    placeholder="Enter skype"
                                    onChange={handleInput}
                                    className='form-control'
                                    accept="application/msword, application/pdf"
                                    type="file"
                                    name="proof"
                                    id="proof"
                                />
                                {error.proof && (
                                        <div>
                                            <span className="text-danger h6">{error.proof}</span>
                                        </div>
                                    )}
                            </div>
                            <div className='mt-3'>
                                <button
                                    className="btn btn-primary"
                                    onClick={handleAddUserList}
                                >
                                    {!!params.id ? "Update" : "Submit"}
                                </button>
                                <button className="btn btn-secondary ms-3" onClick={handleReset}>
                                    Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}

const mapStateToProp = (state) => ({
    getCountryReducer: state.CountryReducer.countryDetails,
    addUserListReducer: state.usersListReducers.addUserList,
    updateUserListReducer: state.usersListReducers.updateUserList,
    getUserListReducer: state.usersListReducers.userList,
});

const mapDispatchToProps = (dispatch) => ({
    getCountryList: (details) => dispatch(getCountryList(details)),
    getUserList: () => dispatch(getUserList()),
    addUserList: (details) => dispatch(addUserList(details)),
    updateUserList: (details) => dispatch(updateUserList(details)),
    resetAddUserList: (details) => dispatch({ type: RESET_ADD_USER_LIST }),
    resetUpdateUserList: (details) => dispatch({ type: RESET_UPDATE_USER_LIST }),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddUserList);
