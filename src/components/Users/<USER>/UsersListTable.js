// import * as dataFormat from 'components/Users/<USER>/UsersDataFormatters';
import actions, { changeUserListStatus, deleteUserList, getUserList } from '../../../Services/actions/usersListActions';
import React, { useEffect, useState } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Dropdown, DropdownMenu, DropdownToggle, DropdownItem, Button, Modal, ModalHeader, ModalBody, ModalFooter, } from 'reactstrap';
import { BootstrapTable, TableHeaderColumn, SearchField } from 'react-bootstrap-table';
import s from '../Users.module.scss';
import { RESET_ADD_USER_LIST, RESET_DELETE_USER_LIST, RESET_UPDATE_USER_LIST, RESET_USER_LIST_STATUS } from '../../../Services/Constant';
import { Link, useHistory } from 'react-router-dom';
import Loader from '../../Loader/Loader';
import { toast } from 'react-toastify';

const UsersListTable = (props) => {

  const history = useHistory()

  const [state, setState] = useState([])
  const [loading, setLoading] = useState(true)

  const [isDelete, setIsDelete] = useState(false)
  const [isDeleteId, setIsDeleteId] = useState(null)


  useEffect(() => {
    props.getUserList()
  }, [])



  useEffect(() => {
    setLoading(props.getuserListReducer.loading)
    if (props.getuserListReducer.success) {
      const data = props.getuserListReducer.data
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          firstName: e.fullname,
          phoneNumber: e.mobile,
          email: e.email,
          Status: e.status,
          id: e.id,
          username: e.username,
          company: e.company,
          country: e.country
        }
      })
      setState([...list])
    }

  }, [props.getuserListReducer])

  const handleDelete = () => {
    props.deleteUserList(isDeleteId)
  }

  useEffect(() => {
    if (props.deleteuserListReducer.success) {
      setIsDelete(false)
      setIsDeleteId(null)
      props.getUserList()
      props.resetDeleteList()
      toast.success("User deleted successfully")
    }
  }, [props.deleteuserListReducer])



  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.Status === 1 ? 'default' : 'secondary'}
          className="btn btn-rounded-f" type='button'
          onClick={() => handleStatus(value.Status, value.id)}
        >
          {cell === 1 ? 'Active' : 'Deactive'}
        </Button>
      </div>
    )
  }

  const handleStatus = async (status, id) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeUserListStatus({
      details: details,
      id: id
    })
    const list = [...state]
    const findItemIndex = state.findIndex(e => e.id === id)
    list[findItemIndex]['Status'] = list[findItemIndex]['Status'] === 1 ? 0 : 1
    setState([...list])
  }

  useEffect(() => {
    if (props.statususerListReducer.success) {
      // props.getUserList()
      props.resetStatus()
    }

  }, [props.statususerListReducer])

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center`}>
        <Button
          className="btn-action me-2"
          onClick={() => history.push(`/users/${value.id}/update`)}
        >
          <i className="fa-regular fa-pencil-square-o"></i>
        </Button>
        <Button
          className="btn-action me-2"
          onClick={() => history.push(`/users/${value.id}`)}
        >
          <i className="fa-regular fa-eye"></i>
        </Button>
        <Button
          className="btn-action"
          onClick={() => {
            setIsDelete(true)
            setIsDeleteId(value.id)
          }}
        >
          <i className="fa-regular fa-trash-can"></i>
        </Button>
      </div>
    )
  }


  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(<DropdownItem key={limit} onClick={() => props.changeSizePerPage(limit)}>{limit}</DropdownItem>);
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>
          {limits}
        </DropdownMenu>
      </Dropdown>
    );
  };

  const createCustomSearchField = (props) => {
    return (
      <SearchField
        className="mb-sm-5 me-1"
        placeholder='Search'
      />
    );
  }

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };


  useEffect(() => {
    if (props.addUserListReducer.success) {
      props.getUserList()
      props.resetAddUserList()
    }
  }, [props.addUserListReducer])

  useEffect(() => {
    if (props.updateUserListReducer.success) {
      props.getUserList()
      props.resetUpdateUserList()
    }
  }, [props.updateUserListReducer])

  return (
    <>
      {loading ? <Loader /> : <><div className="text-end mb-4">
        <Link to="/users/add" className="btn btn-primary">
          Add User
        </Link>
      </div>
        <div className='card main-card'>
          <div className='card-body'>
                        {/* bordered={false} data={state} version="4" pagination options={options} search tableContainerClass={`coustom-table stone-table table-striped table-hover`} */}
            <BootstrapTable bordered={false} data={state} version="4" pagination options={options} search tableContainerClass={` table-striped table-hover`}>
              <TableHeaderColumn dataField="srNo" width='75px'>
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="firstName" dataSort>
                <span className="fs-sm">Name</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="username" dataSort>
                <span className="fs-sm">Username</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="phoneNumber">
                <span className="fs-sm">Phone Number</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="email">
                <span className="fs-sm">E-mail</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="company">
                <span className="fs-sm">Company</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="Status" dataFormat={statusChange.bind(this)}>
                <span className="fs-sm">Status</span>
              </TableHeaderColumn>
              <TableHeaderColumn isKey dataField="id" dataFormat={actionFormatter.bind(this)}>
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          </div>
        </div>
      </>}
      <Modal size="sm" isOpen={isDelete} toggle={() => setIsDelete(false)}>
        <ModalHeader toggle={() => {
          setIsDelete(false)
          setIsDeleteId(null)
        }}>Confirm delete</ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this user?<br /><br />
          you can delete once you can not recover data
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" onClick={() => {
            setIsDelete(false)
            setIsDeleteId(null)
          }}>Cancel</Button>
          <Button color="primary" onClick={handleDelete}>Delete</Button>
        </ModalFooter>
      </Modal>
    </>
  );
}

const mapStateToProp = (state) => ({
  getuserListReducer: state.usersListReducers.userList,
  deleteuserListReducer: state.usersListReducers.deleteUserList,
  statususerListReducer: state.usersListReducers.statusUserList,
  addUserListReducer: state.usersListReducers.addUserList,
  updateUserListReducer: state.usersListReducers.updateUserList,
});

const mapDispatchToProps = (dispatch) => ({
  getUserList: () => dispatch(getUserList()),
  deleteUserList: (id) => dispatch(deleteUserList(id)),
  changeUserListStatus: (details) => dispatch(changeUserListStatus(details)),
  resetDeleteList: () => dispatch({ type: RESET_DELETE_USER_LIST }),
  resetStatus: () => dispatch({ type: RESET_USER_LIST_STATUS }),
  resetAddUserList: () => dispatch({ type: RESET_ADD_USER_LIST }),
  resetUpdateUserList: () => dispatch({ type: RESET_UPDATE_USER_LIST }),
});

export default connect(mapStateToProp, mapDispatchToProps)(UsersListTable);
