import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory, useParams } from "react-router";
import { Col, Row } from "reactstrap";
import { getStockList } from "../../Services/actions/stockAction";
import Loader from "../Loader/Loader";

function StoneDetails(props) {
  const params = useParams();
  const history = useHistory();
  const [details, setDetails] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!params.id || !params) {
      history.push("/stock");
    } else {
      setLoading(props.StockListReducer.loading);
      if (!!params.id && props.StockListReducer.success) {
        const data = props.StockListReducer.data;
        const _details = data.find((e) => {
          return e.id === params.id;
        });
        setDetails(_details);
      }
    }
  }, [props.StockListReducer, params]);

  useEffect(() => {
    if (!props.StockListReducer.success) {
      props.getStockList();
    }
  }, []);
  return (
    <>
      {/* <div className="text-end mb-4">
        <Link
          to={`/stock/${params.id}/update`}
          className="btn btn-primary"
        >
          Update Stock
        </Link>
      </div> */}
      <div className="page-top-line">
        <h2 className="page-title">Stone Details</h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">Stone Details</h4>

          {loading ? (
            <Loader />
          ) : (
            <>
              {!!details && (
                <Row className="col-lg-8 g-3">
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Stone ID : </b>
                      {!!details.stone_id && details.stone_id !== ""
                        ? details.stone_id
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Certificate No : </b>
                      {!!details.cert_no && details.cert_no !== ""
                        ? details.cert_no
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Certificate Type : </b>
                      {!!details.cert && details.cert !== ""
                        ? details.cert
                        : "N/A"}
                    </div>
                  </Col>
                  <div></div>
                  <div className="mb-4">
                    <div>
                      <b className="fw-bold">Certificate : </b>
                      <span0
                        style={{
                          cursor: "pointer",
                          textDecoration: "underline",
                        }}
                        onClick={() => {
                          window.open(details.cert_url, "_blank");
                        }}
                      >
                        {!!details.cert_url && details.cert_url !== ""
                          ? details.cert_url
                          : "N/A"}
                      </span0>
                    </div>
                  </div>
                  <div className="mb-4">
                    <div>
                      <b className="fw-bold">Image Url : </b>
                      <span
                        style={{
                          cursor: "pointer",
                          textDecoration: "underline",
                        }}
                        onClick={() => {
                          window.open(details.image, "_blank");
                        }}
                      >
                        {!!details.image && details.image !== ""
                          ? details.image
                          : "N/A"}
                      </span>
                    </div>
                  </div>
                  <div className="mb-4">
                    <div>
                      <b className="fw-bold">Video Url : </b>
                      <span
                        style={{
                          cursor: "pointer",
                          textDecoration: "underline",
                        }}
                        onClick={() => {
                          window.open(details.video, "_blank");
                        }}
                      >
                        {!!details.video && details.video !== ""
                          ? details.video
                          : "N/A"}
                      </span>
                    </div>
                  </div>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Country : </b>
                      {!!details.country && details.country !== ""
                        ? details.country
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">City : </b>
                      {!!details.city && details.city !== ""
                        ? details.city
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Diamond Type : </b>
                      {!!details.diamond_type_name &&
                      details.diamond_type_name !== ""
                        ? details.diamond_type_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Size : </b>
                      {!!details.size_name && details.size_name !== ""
                        ? details.size_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Carat : </b>
                      {!!details.carat && details.carat !== ""
                        ? details.carat
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Shape : </b>
                      {!!details.shape_name && details.shape_name !== ""
                        ? details.shape_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Color : </b>
                      {!!details.color_name && details.color_name !== ""
                        ? details.color_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Fancy Color : </b>
                      {!!details.colors_name && details.colors_name !== ""
                        ? details.colors_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Overtone : </b>
                      {!!details.overtone_name && details.overtone_name !== ""
                        ? details.overtone_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Clarity : </b>
                      {!!details.clarity_name && details.clarity_name !== ""
                        ? details.clarity_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Cut : </b>
                      {!!details.cut_name && details.cut_name !== ""
                        ? details.cut_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Polish : </b>
                      {!!details.polish_name && details.polish_name !== ""
                        ? details.polish_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Stmmentry : </b>
                      {!!details.symmetry_name && details.symmetry_name !== ""
                        ? details.symmetry_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Fluorescence : </b>
                      {!!details.fluorescence_name &&
                      details.fluorescence_name !== ""
                        ? details.fluorescence_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">BGM : </b>
                      {!!details.bgm_name && details.bgm_name !== ""
                        ? details.bgm_name
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Rapo Rate : </b>
                      {!!details.rapo_rate && details.rapo_rate !== ""
                        ? details.rapo_rate
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Discount : </b>
                      {!!details.discount && details.discount !== ""
                        ? details.discount
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Rate : </b>
                      {!!details.rate && details.rate !== ""
                        ? details.rate
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Rapo Amount : </b>
                      {!!details.rapo_amount && details.rapo_amount !== ""
                        ? details.rapo_amount
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Amount : </b>
                      {!!details.amount && details.amount !== ""
                        ? details.amount
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Table : </b>
                      {!!details.table && details.table !== ""
                        ? details.table
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Table PER : </b>
                      {!!details.table_per && details.table_per !== ""
                        ? details.table_per
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Depth : </b>
                      {!!details.depth && details.depth !== ""
                        ? details.depth
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Depth PER : </b>
                      {!!details.depth_per && details.depth_per !== ""
                        ? details.depth_per
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Lenght : </b>
                      {!!details.length && details.length !== ""
                        ? details.length
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Width : </b>
                      {!!details.width && details.width !== ""
                        ? details.width
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={4} md={4} className="mb-3">
                    <div>
                      <b className="fw-bold">Height : </b>
                      {!!details.height && details.height !== ""
                        ? details.height
                        : "N/A"}
                    </div>
                  </Col>
                  <Col lg={6} md={6} className="mb-3">
                    <div>
                      <b className="fw-bold">Ration : </b>
                      {!!details.ratio && details.ratio !== ""
                        ? details.ratio
                        : "N/A"}
                    </div>
                  </Col>
                </Row>
              )}
            </>
          )}
          <button className="btn btn-primary" onClick={() => history.goBack()}>
            Back
          </button>
        </div>
      </div>
    </>
  );
}

const mapStateToProp = (state) => ({
  StockListReducer: state.stockReducer.StockList,
});

const mapDispatchToProps = (dispatch) => ({
  getStockList: () => dispatch(getStockList()),
});

export default connect(mapStateToProp, mapDispatchToProps)(StoneDetails);
