import React from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
// import { dismissAlert } from '../../Services/actions/alerts';
import s from './Sidebar.module.scss';
import LinksGroup from './LinksGroup/LinksGroup';
// import { openSidebar, closeSidebar, changeActiveSidebarItem } from '../../Services/actions/navigation';
import isScreen from '../../core/screenHelper';
// import { logoutUser } from '../../Services/actions/auth';

import Home from '../../images/sidebar/basil/Home';
import User from '../../images/sidebar/basil/User';
import Apps from '../../images/sidebar/basil/Apps';
import Asana from '../../images/sidebar/basil/Asana';
import Rows from '../../images/sidebar/basil/Rows';
import Exchange from '../../images/sidebar/basil/Exchange';
import ShoppingCart from '../../images/sidebar/basil/ShoppingCart';
import { dismissAlert } from '../../Services/actions/alerts';
import { changeActiveSidebarItem, closeSidebar, openSidebar } from '../../Services/actions/navigation';
import { logoutUser } from '../../Services/actions/auth';
import Document from '../../images/sidebar/basil/Document';
import Stack from '../../images/sidebar/basil/Stack';
import Layout from '../../images/sidebar/basil/Layout';

class Sidebar extends React.Component {
  static propTypes = {
    sidebarStatic: PropTypes.bool,
    sidebarOpened: PropTypes.bool,
    dispatch: PropTypes.func.isRequired,
    activeItem: PropTypes.string,
    location: PropTypes.shape({
      pathname: PropTypes.string,
    }).isRequired,
  };

  static defaultProps = {
    sidebarStatic: true,
    sidebarOpened: true,
    activeItem: '',
  };

  constructor(props) {
    super(props);
    this.onMouseEnter = this.onMouseEnter.bind(this);
    this.onMouseLeave = this.onMouseLeave.bind(this);
    this.doLogout = this.doLogout.bind(this);
  }

  onMouseEnter() {
    if (!this.props.sidebarStatic && (isScreen('lg') || isScreen('xl'))) {
      const paths = this.props.location.pathname.split('/');
      paths.pop();
      this.props.dispatch(openSidebar());
      this.props.dispatch(changeActiveSidebarItem(paths.join('/')));
    }
  }

  onMouseLeave() {
    if (!this.props.sidebarStatic && (isScreen('lg') || isScreen('xl'))) {
      this.props.dispatch(closeSidebar());
      this.props.dispatch(changeActiveSidebarItem(null));
    }
  }

  dismissAlert(id) {
    this.props.dispatch(dismissAlert(id));
  }

  doLogout() {
    this.props.dispatch(logoutUser());
  }
  render() {
    return (
      <div className={`${(!this.props.sidebarOpened && !this.props.sidebarStatic) ? s.sidebarClose : ''} ${s.sidebarWrapper}`}>
        <nav
          onMouseEnter={this.onMouseEnter} onMouseLeave={this.onMouseLeave}
          className={s.root}
        >
          <header className={s.logo}>
            {/* <span className={s.logoStyle}>Delight <span className={s.logoPart}>Diamonds</span></span> */}
            <img src={'/images/logo.jpeg'} className="img-fluid logo" />
          </header>
          <ul className={s.nav}>
            <LinksGroup
              header="Dashboard"
              isHeader
              iconName="flaticon-home"
              iconElement={<Home />}
              link="/dashboard"
            />
            <LinksGroup
              header="User Management"
              isHeader
              iconName="flaticon-user"
              iconElement={<User />}
              link="/users"
            />
            <LinksGroup
              header="Stock Management"
              isHeader
              iconName="flaticon-network"
              iconElement={<Apps />}
              link="/stock"
            />
            <LinksGroup
              header="Invalid Stone"
              isHeader
              iconName="flaticon-network"
              iconElement={<Asana />}
              link="/invalid-stone"
            />
            <LinksGroup
              header="Upload History"
              isHeader
              iconElement={<Document />}
              link="/upload-history"
            />
            <LinksGroup
              header="Cart"
              isHeader
              iconName="flaticon-network"
              iconElement={<ShoppingCart />}
              link="/cart"
            />
            <LinksGroup
              header="Hold Diamonds"
              isHeader
              iconName="flaticon-network"
              iconElement={<ShoppingCart />}
              link="/hold"
            />
            <LinksGroup
              header="Confirm Diamond"
              isHeader
              iconName="flaticon-network"
              iconElement={<ShoppingCart />}
              link="/confirm"
            />
            <LinksGroup
              header="Inquiry List"
              isHeader
              iconName="flaticon-network"
              iconElement={<Stack />}
              link="/inquiry"
            />
            <LinksGroup
              header="View Request"
              isHeader
              iconName="flaticon-network"
              iconElement={<Asana />}
              link="/request"
            />
            <LinksGroup
              header="Appointment List"
              isHeader
              iconName="flaticon-network"
              iconElement={<Layout />}
              link="/appointment"
            />
            <LinksGroup
              header="Demand List"
              isHeader
              iconName="flaticon-network"
              iconElement={<Asana />}
              link="/demand-list"
            />
            {/* <LinksGroup
            header="Sales Management"
            isHeader
            iconName="flaticon-controls"
            iconElement={<ChartPieAlt/>}
            link="/sales"
          /> */}
            <LinksGroup
              header="Vendor List"
              isHeader
              iconName="flaticon-user"
              iconElement={<User />}
              link="/vendor"
            />
            <LinksGroup
              header="Merchant List"
              isHeader
              iconName="flaticon-user"
              iconElement={<User />}
              link="/merchant"
            />
            <LinksGroup
              onActiveSidebarItemChange={activeItem => this.props.dispatch(changeActiveSidebarItem(activeItem))}
              activeItem={this.props.activeItem}
              header="Master"
              isHeader
              iconElement={<Asana />}
              conName="flaticon-layers"
              link="/master"
              index=""
              exact={false}
              childrenLinks={[
                {
                  header: 'Clarity', link: '/clarity',
                },
                {
                  header: 'Color', link: '/color',
                },
                {
                  header: 'Size', link: '/size',
                },
                {
                  header: 'Shape', link: '/shape',
                },
                {
                  header: 'Fancy Color', link: '/fancy-color',
                },
                {
                  header: 'Finish', link: '/finish',
                },
                {
                  header: 'Fluorescence', link: '/fluorescence',
                },
              ]}
            />
            <LinksGroup
              header="Star melee"
              isHeader
              iconName="flaticon-network"
              iconElement={<Asana />}
              link="/star-melee"
            />
            <LinksGroup
              header="Star melee Inquiry"
              isHeader
              iconName="flaticon-network"
              iconElement={<Asana />}
              link="/star-melee-inquiry"
            />
            <LinksGroup
              header="Policies"
              isHeader
              iconName="flaticon-equal-1"
              iconElement={<Rows />}
              link="/policy"
            />
            <LinksGroup
              header="Marketing"
              isHeader
              iconElement={<Exchange />}
              link="/marketing"
            />
          </ul>
        </nav >
      </div>
    );
  }
}

function mapStateToProps(store) {
  return {
    sidebarOpened: store.navigation.sidebarOpened,
    sidebarStatic: store.navigation.sidebarStatic,
    alertsList: store.alerts.alertsList,
    activeItem: store.navigation.activeItem,
    navbarType: store.navigation.navbarType,
    sidebarColor: store.layout.sidebarColor,
  };
}

export default withRouter(connect(mapStateToProps)(Sidebar));
