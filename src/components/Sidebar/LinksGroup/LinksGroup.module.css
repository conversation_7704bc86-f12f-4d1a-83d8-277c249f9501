/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.headerLink {
  overflow-x: hidden;
}
@media (min-width: 992px) and (min-height: 670px), (max-width: 767px) {
  .headerLink {
    font-size: 13px;
  }
}
.headerLink a {
  display: block;
  color: var(--sidebar-color);
  transition: all 0.35s ease;
  text-decoration: none;
  cursor: pointer;
}
.headerLink a:hover {
  text-decoration: none;
  color: inherit;
}
.headerLink:last-child > a {
  border-bottom: 1px solid transparent;
}
.headerLink > a {
  position: relative;
  align-items: center;
  padding-left: 50px;
  line-height: 35px;
  border-top: 1px solid transparent;
}
.headerLink > a:hover {
  background-color: var(--sidebar-item-hover-bg-color);
}
.headerLink > a > i {
  margin-right: 7px;
}
@media (min-width: 992px) and (min-height: 670px), (max-width: 767px) {
  .headerLink > a {
    line-height: 55px;
  }
}
.headerLink .icon {
  font-size: 1.1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.35s ease;
  color: var(--sidebar-color);
  background-color: transparent;
  position: absolute;
  top: 3px;
  left: 11px;
  width: 28px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  border-radius: 0;
  opacity: 0.7;
}
@media (min-width: 992px) and (min-height: 670px), (max-width: 767px) {
  .headerLink .icon {
    top: 12px;
  }
}
.headerLink .badge {
  float: right;
  line-height: 8px;
  margin-top: 9px;
  margin-right: 15px;
  padding: 0.5em 0.5em;
  font-size: 0.875em;
  color: var(--sidebar-badge-color);
}
@media (min-width: 992px) and (min-height: 670px), (max-width: 767px) {
  .headerLink .badge {
    margin-top: 16px;
  }
}
.headerLink .headerNode {
  color: var(--sidebar-hightlight-two);
}
.headerLink .headerUpdate {
  color: var(--sidebar-hightlight);
}
.headerLink a.headerLinkActive {
  color: var(--sidebar-item-active-color);
  font-weight: 700;
  white-space: nowrap;
}
.headerLink a.headerLinkActive:hover {
  color: var(--sidebar-icon-bg);
}
.headerLink a.headerLinkActive .icon {
  border-radius: 50%;
  background-color: var(--sidebar-icon-bg);
  opacity: 1;
  display: flex;
  justify-content: center;
  transform: rotate(360deg);
  align-items: center;
  color: var(--sidebar-bg-color);
}
.headerLink a.headerLinkActive .icon i {
  color: var(--sidebar-bg-color);
}

.headerLabel {
  font-weight: 600;
  color: var(--sidebar-item-active-color);
}

.collapsed .caret {
  transform: rotate(-90deg);
}

.caret {
  display: flex;
  align-items: center;
  margin-left: auto;
  font-size: 19px;
  margin-right: 15px;
  color: var(--sidebar-color);
  transition: transform 0.3s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .caret {
    transition: none;
  }
}

.panel {
  border: none;
  box-shadow: none;
  margin: 0;
  border-radius: 0;
}
.panel a.headerLinkActive {
  font-weight: 600;
  color: var(--sidebar-color);
}
.panel a.headerLinkActive:hover {
  color: var(--sidebar-color);
}
.panel ul {
  background: var(--sidebar-action-bg);
  padding: 1rem;
}
.panel ul li {
  list-style: none;
}
.panel ul a {
  padding: 10px 20px 10px 26px;
  font-size: 0.9rem;
}
.panel ul a:hover {
  background-color: var(--sidebar-item-hover-bg-color);
}/*# sourceMappingURL=LinksGroup.module.css.map */