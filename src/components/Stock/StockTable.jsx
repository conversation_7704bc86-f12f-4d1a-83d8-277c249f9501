// import * as dataFormat from 'components/Users/<USER>/UsersDataFormatters';
import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import {
  Dropdown,
  DropdownMenu,
  DropdownToggle,
  Dropdown<PERSON>tem,
  Button,
  Modal,
  Modal<PERSON><PERSON>er,
  <PERSON>dalBody,
  Modal<PERSON>ooter,
} from "reactstrap";
import {
  BootstrapTable,
  TableHeaderColumn,
  SearchField,
} from "react-bootstrap-table";
import {
  changeStockListStatus,
  deleteStockList,
  getStockList,
} from "../../Services/actions/stockAction";
import {
  RESET_ADD_STOCK_LIST,
  RESET_DELETE_STOCK_LIST,
  RESET_SAVE_CSV,
  RESET_STOCK_LIST_STATUS,
} from "../../Services/Constant";
import { useHistory } from "react-router";
import Loader from "../Loader/Loader";
import { toast } from "react-toastify";
import moment from "moment";

const StockTable = (props) => {
  const [state, setState] = useState([]);
  const history = useHistory();
  const [loading, setLoading] = useState(true);
  const [isDelete, setIsDelete] = useState(false);
  const [isDeleteId, setIsDeleteId] = useState(null);

  useEffect(() => {
    props.getStockList();
  }, []);

  useEffect(() => {
    if (props.StockListReducer.success) {
      setLoading(props.StockListReducer.loading);
      const data = props.StockListReducer.data;

      const details = data
        .filter((e) => e.diamond_type === 1)
        .sort((a, b) => moment(b.created_at).diff(moment(a.created_at)))
        .map((e, i) => {
          return {
            srNo: i + 1,
            stone_id: e.stone_id,
            cert_no: e.cert_no,
            cert_url: e.cert_url,
            cert: e.cert,
            diam_type: e.diamond_type_name,
            shape: e.shape_name,
            color: e.color_name,
            carat: e.carat,
            clarity: e.clarity_name,
            cut: e.cut_name,
            polish: e.polish_name,
            symmetry: e.symmetry_name,
            rate: e.rate,
            ratio: e.ratio,
            pair: e.pair,
            growth_type: e.growth_type,
            amount: e.amount,
            status: e.status,
            id: e.id,
          };
        });
      setState([...details]);
    }
  }, [props.StockListReducer]);

  const handleDelete = () => {
    props.deleteStockList(isDeleteId);
  };

  useEffect(() => {
    if (props.addStockListReducer.success) {
      props.getStockList();
      props.resetAddStockList();
    }
  }, [props.addStockListReducer]);

  useEffect(() => {
    if (props.deleteStockListReducer.success) {
      setIsDelete(false);
      setIsDeleteId(null);
      props.getStockList();
      props.resetDeleteStock();
      toast.success("stock deleted successfully...");
    }
  }, [props.deleteStockListReducer]);

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <div className={`d-flex justify-content-center`}>
          <button
            className="btn-action"
            onClick={() => {
              history.push(`/stock/${value.id}/update`);
            }}
          >
            <i className="fa-regular fa-pen-to-square"></i>
          </button>
          <button
            className="btn-action mx-2"
            onClick={() => {
              history.push(`/stock/${value.id}`);
            }}
          >
            <i className="fa-regular fa-eye"></i>
          </button>
          {/* <Button
            className="btn-action"
            onClick={() => {
              setIsDelete(true);
              setIsDeleteId(value.id);
            }}
          >
            <i className="fa-regular fa-trash-can"></i>
          </Button> */}
        </div>
      </div>
    );
  };

  const handleStatus = async (status, id) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeStockListStatus({
      details: details,
      id: id,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.statusStockListReducer.success) {
      toast.success("Status changed successfull...");
      props.resetStatusStockList();
    }
  }, [props.statusStockListReducer]);

  useEffect(() => {
    if (props.savecsvFileReducer.success) {
      props.getStockList();
      props.resetSaveCsvFile();
    }
  }, [props.savecsvFileReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.status, value.id)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };
  const certificate = (cell, value) => {
    return (
      <div
        className={`d-flex justify-content-between`}
        style={{ cursor: "pointer", textDecoration: "underline" }}
        onClick={() => {
          window.open(value.cert_url, "_blank");
        }}
      >
        {value.cert_no}
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const createCustomSearchField = (props) => {
    return <SearchField className="mb-sm-5 me-1" placeholder="Search" />;
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <BootstrapTable
            bordered={false}
            data={state}
            version="4"
            pagination
            options={options}
            search
            scrollTop={"Bottom"}
            tableContainerClass={`coustom-table stone-table table-striped table-hover`}
          >
            <TableHeaderColumn dataField="srNo" width="50px">
              <span className="fs-sm">Sr No.</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="stone_id" dataSort>
              <span className="fs-sm">Stone ID</span>
            </TableHeaderColumn>
            <TableHeaderColumn
              dataField="cert_no"
              dataSort
              dataFormat={certificate.bind(this)}
              width="125px"
            >
              <span className="fs-sm">Cerificate No.</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="cert" dataSort width="145px">
              <span className="fs-sm">Certifiacate Type</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="diam_type" width="106px">
              <span className="fs-sm">Diamond Type</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="shape" width="100px">
              <span className="fs-sm">Shape</span>
            </TableHeaderColumn>
            {/* <TableHeaderColumn dataField="color" >
              <span className="fs-sm">Color</span>
            </TableHeaderColumn> */}
            <TableHeaderColumn dataField="carat" >
              <span className="fs-sm">Carat</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="clarity" >
              <span className="fs-sm">Clarity</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="cut" >
              <span className="fs-sm">Cut</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="polish" >
              <span className="fs-sm">Polish</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="symmetry" >
              <span className="fs-sm">Symmetry</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="rate" >
              <span className="fs-sm">Rate</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="amount" >
              <span className="fs-sm">Amount</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="ratio" >
              <span className="fs-sm">Ratio</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="pair" >
              <span className="fs-sm">Pair</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="growth_type" width="100px">
              <span className="fs-sm">Growth Type</span>
            </TableHeaderColumn>
            <TableHeaderColumn
              dataField="status"
              dataFormat={statusChange.bind(this)}
            >
              <span className="fs-sm">Status</span>
            </TableHeaderColumn>
            <TableHeaderColumn
              isKey
              dataField="id"
              dataFormat={actionFormatter.bind(this)}
              width="120px"
            >
              <span className="fs-sm">Actions</span>
            </TableHeaderColumn>
          </BootstrapTable>

          <Modal size="sm" isOpen={isDelete} toggle={() => setIsDelete(false)}>
            <ModalHeader
              toggle={() => {
                setIsDelete(false);
                setIsDeleteId(null);
              }}
            >
              Confirm delete
            </ModalHeader>
            <ModalBody className="bg-white">
              Are you sure you want to delete this item?
            </ModalBody>
            <ModalFooter>
              <Button
                color="secondary"
                onClick={() => {
                  setIsDelete(false);
                  setIsDeleteId(null);
                }}
              >
                Cancel
              </Button>
              <Button color="primary" onClick={handleDelete}>
                Delete
              </Button>
            </ModalFooter>
          </Modal>
        </>
      )}
    </>
  );
};

const mapStateToProp = (state) => ({
  StockListReducer: state.stockReducer.StockList,
  deleteStockListReducer: state.stockReducer.deleteStockList,
  addStockListReducer: state.stockReducer.addStockList,
  updateStockListReducer: state.stockReducer.updateStockList,
  statusStockListReducer: state.stockReducer.statusStockList,
  savecsvFileReducer: state.stockReducer.saveCsvFile,
});

const mapDispatchToProps = (dispatch) => ({
  getStockList: () => dispatch(getStockList()),
  deleteStockList: (id) => dispatch(deleteStockList(id)),
  changeStockListStatus: (details) => dispatch(changeStockListStatus(details)),
  resetDeleteStock: (id) => dispatch({ type: RESET_DELETE_STOCK_LIST }),
  resetAddStockList: (id) => dispatch({ type: RESET_ADD_STOCK_LIST }),
  resetStatusStockList: (id) => dispatch({ type: RESET_STOCK_LIST_STATUS }),
  resetSaveCsvFile: (id) => dispatch({ type: RESET_SAVE_CSV }),
});

export default connect(mapStateToProp, mapDispatchToProps)(StockTable);
