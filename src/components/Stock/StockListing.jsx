import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import StockTable from "./StockTable";
import { Tab, Tabs } from "react-bootstrap";
import LabGrownTable from "./LabGrownTable";
import { connect } from "react-redux";
import {
  getStockList,
  resetDiamondList,
} from "../../Services/actions/stockAction";
import { Button, Modal, ModalBody, ModalFooter, ModalHeader } from "reactstrap";

const StockListing = (props) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectKey, setSelectKey] = useState("natural");

  useEffect(() => {
    if (props.resetDiamondReducer.success) {
      props.getStockList();
      setIsOpen(false);
    }
  }, [props.resetDiamondReducer]);
  return (
    <div>
      <div className="page-top-line">
        <h2 className="page-title">
          Stock - <span className="fw-semi-bold">Management</span>
        </h2>
      </div>

      <div className="text-end mb-4">
        <div
          className="btn btn-primary me-4"
          onClick={() => {
            setIsOpen(true);
          }}
        >
          Reset Diamond
        </div>
        <Link to="/stock/add" className="btn btn-primary">
          Add New
        </Link>
      </div>

      <Tabs
        defaultActiveKey={selectKey}
        id="AddStockTabs"
        onSelect={(e) => setSelectKey(e)}
      >
        <Tab eventKey="natural" title="Natural Diamond List">
          {/* <div className="card main-card border-0">
            <div className="card-body"> */}
              <StockTable />
            {/* </div>
          </div> */}
        </Tab>
        <Tab eventKey="lab-grown" title={"Lab-Grown Diamond List"}>
          {/* <div className="card main-card border-0">
            <div className="card-body"> */}
              <LabGrownTable />
            {/* </div>
          </div> */}
        </Tab>
      </Tabs>
      <Modal
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        isOpen={isOpen}
        toggle={() => {
          setIsOpen(false);
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsOpen(false);
          }}
        >
          Confirm Reset
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to reset this diamonds?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              props.resetDiamondList(
                selectKey === "natural"
                  ? "1"
                  : selectKey === "lab-grown"
                  ? "2"
                  : 1
              );
            }}
          >
            Reset
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

const mapStateToProp = (state) => ({
  resetDiamondReducer: state.stockReducer.resetDiamond,
});

const mapDispatchToProps = (dispatch) => ({
  resetDiamondList: (details) => dispatch(resetDiamondList(details)),
  getStockList: () => dispatch(getStockList()),
});

export default connect(mapStateToProp, mapDispatchToProps)(StockListing);
