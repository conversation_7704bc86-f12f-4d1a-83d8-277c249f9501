import React from "react";
import { Tab, Tabs } from "react-bootstrap";
import { useParams } from "react-router";
import CSVupload from "./CSVupload";
import AddSingleProduct from "./AddSingleProduct";

function AddNewStock() {
  const params = useParams();
  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          {!!params.id ? "Update" : "Add"} -
          <span className="fw-semi-bold"> Product</span>
        </h2>
      </div>
      <Tabs
        defaultActiveKey={!params.id ? "home" : "profile"}
        id="AddStockTabs"
      >
        {!params.id && (
          <Tab eventKey="home" title="Upload CSV">
            <CSVupload />
          </Tab>
        )}
        <Tab
          eventKey="profile"
          title={`${!!params.id ? "Update" : "Add"} Product`}
        >
          <AddSingleProduct />
        </Tab>
      </Tabs>
    </>
  );
}
export default AddNewStock;