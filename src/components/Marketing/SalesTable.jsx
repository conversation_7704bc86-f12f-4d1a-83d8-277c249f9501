import * as dataFormat from "components/Users/<USER>/UsersDataFormatters";
import React, { useEffect } from "react";
import { connect, useDispatch } from "react-redux";
import {
  Dropdown,
  DropdownMenu,
  DropdownToggle,
  DropdownItem,
  Button,
  Modal,
  ModalHeader,
  Modal<PERSON>ody,
  Modal<PERSON>ooter,
} from "reactstrap";
import {
  BootstrapTable,
  TableHeaderColumn,
  SearchField,
} from "react-bootstrap-table";
import actions from "../../Services/actions/usersFormActions";

const StockTable = (props) => {
  const rows = props.rows;
  const modalOpen = props.modalOpen;
  const idToDelete = props.idToDelete;
  const handleDelete = () => {
    const userId = idToDelete;
    dispatch(actions.doDelete(userId));
  };

  const openModal = (cell) => {
    const userId = cell;
    dispatch(actions.doOpenConfirm(userId));
  };

  const closeModal = () => {
    dispatch(actions.doCloseConfirm());
  };

  const actionFormatter = (cell) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          className="btn btn-danger"
          color="danger"
          size="xs"
          onClick={() => openModal(cell)}
        >
          Delete
        </Button>
      </div>
    );
  };
  const dispatch = useDispatch();
  //   dispatch(actions.doFetch({}));

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const createCustomSearchField = (props) => {
    return <SearchField className="mb-sm-5 me-1" placeholder="Search" />;
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <div>
      <div>
        <BootstrapTable
          bordered={false}
          data={rows}
          version="4"
          pagination
          options={options}
          search
          tableContainerClass={`table-responsive table-striped table-hover`}
        >
          <TableHeaderColumn dataField="" dataSort>
            <span className="fs-sm">Sr No.</span>
          </TableHeaderColumn>

          <TableHeaderColumn dataField="firstName" dataSort>
            <span className="fs-sm">Name</span>
          </TableHeaderColumn>

          <TableHeaderColumn dataField="phoneNumber" dataSort>
            <span className="fs-sm">Phone Number</span>
          </TableHeaderColumn>

          <TableHeaderColumn dataField="email" dataSort>
            <span className="fs-sm">E-mail</span>
          </TableHeaderColumn>

          <TableHeaderColumn
            dataField="Status"
            dataSort
            dataFormat={dataFormat.booleanFormatter}
          >
            <span className="fs-sm">Status</span>
          </TableHeaderColumn>

          <TableHeaderColumn
            isKey
            dataField="id"
            dataFormat={actionFormatter.bind(this)}
          >
            <span className="fs-sm">Actions</span>
          </TableHeaderColumn>
        </BootstrapTable>
      </div>
      <Modal size="sm" isOpen={modalOpen} toggle={() => closeModal()}>
        <ModalHeader toggle={() => closeModal()}>Confirm delete</ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this item?
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" onClick={() => closeModal()}>
            Cancel
          </Button>
          <Button color="primary" onClick={() => handleDelete()}>
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};
function mapStateToProps(store) {
  return {
    loading: store.users.list.loading,
    rows: store.users.list.rows,
    modalOpen: store.users.list.modalOpen,
    idToDelete: store.users.list.idToDelete,
  };
}

export default connect(mapStateToProps)(StockTable);
