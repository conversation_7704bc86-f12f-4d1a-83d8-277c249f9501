import classNames from "classnames";
import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import { But<PERSON>, Dropdown, DropdownItem, DropdownMenu, DropdownToggle, Modal, ModalBody, ModalFooter, ModalHeader, } from "reactstrap";
import { changeFluorescenceStatus, deleteFluorescence, getFluorescence, } from "../../../Services/actions/masterAction";
import { RESET_ADD_FLUORESCENCE, RESET_DELETE_FLUORESCENCE, RESET_FLUORESCENCE_STATUS, RESET_UPDATE_FLUORESCENCE, } from "../../../Services/Constant";
import Loader from "../../Loader/Loader";
import { toast } from "react-toastify";

function FluorescenceList(props) {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isDelete, setIsDelete] = useState(false);
  const [deleteId, setDeleteId] = useState("");

  useEffect(() => {
    props.getFluorescence();
  }, []);

  useEffect(() => {
    setLoading(props.getFluorescenceReducer.loading);
    if (props.getFluorescenceReducer.success) {
      const data = props.getFluorescenceReducer.data;
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          name: e.name,
          type: e.type === 0 ? "None" : e.type === 1 ? "Color" : "BGM",
          status: e.status,
          id: e.id,
        };
      });
      setState([...list]);
    }
  }, [props.getFluorescenceReducer]);

  const handleDeleteFluorescence = (id) => {
    props.deleteFluorescence(id);
  };

  useEffect(() => {
    if (props.deleteFluorescenceReducer.success) {
      props.getFluorescence();
      setIsDelete(false);
      setDeleteId("");
      toast.success("fluorescence deleted successfully...");
      props.resetDeleteFluorescence();
    }
  }, [props.deleteFluorescenceReducer]);

  const handleStatus = async (id, status) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeFluorescenceStatus({
      id: id,
      details: details,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.FluorescenceStatusReducer.success) {
      // props.getFluorescence();
      props.resetFluorescenceStatus();
    }
  }, [props.FluorescenceStatusReducer]);

  useEffect(() => {
    if (props.addFluorescenceReducer.success) {
      props.getFluorescence();
      toast.success("fluorescence added successfully...");
      props.resetAddFluorescence();
    }
  }, [props.addFluorescenceReducer]);

  useEffect(() => {
    if (props.updateFluorescenceReducer.success) {
      props.getFluorescence();
      toast.success("fluorescence updated successfully...");
      props.resetUpdateFluorescence();
    }
  }, [props.updateFluorescenceReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.id, value.status)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center align-items-center`}>
        <Link className="" to={`/fluorescence/${value.id}`}>
          <i className="fa fa-pencil-square-o" />
        </Link>
        <div
          className="btn text-danger"
          onClick={() => {
            setIsDelete(true);
            setDeleteId(value.id);
          }}
        >
          <i className="fa fa-trash" />
        </div>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Fluorescence - <span className="fw-semi-bold">Listing</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <div className="text-end mb-4">
            <Link to="/fluorescence/add" className="btn btn-primary">
              Add Fluorescence
            </Link>
          </div>
          {loading ? (
            <Loader />
          ) : (
            <BootstrapTable
              bordered={false}
              data={state}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`coustom-table stone-table table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo" width="50px">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="name" dataSort>
                <span className="fs-sm">Fluorescence</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="type" width="100%" dataSort>
                <span className="fs-sm">Type</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">status</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          )}
        </div>
      </div>
      <Modal
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        isOpen={isDelete}
        toggle={() => {
          setIsDelete(false);
          setDeleteId("");
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setDeleteId("");
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this fluorescence?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setDeleteId("");
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleDeleteFluorescence(deleteId);
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
}

const mapStateToProp = (state) => ({
  getFluorescenceReducer: state.masterReducer.Fluorescence.getFluorescence,
  addFluorescenceReducer: state.masterReducer.Fluorescence.addFluorescence,
  deleteFluorescenceReducer:
    state.masterReducer.Fluorescence.deleteFluorescence,
  updateFluorescenceReducer:
    state.masterReducer.Fluorescence.updateFluorescence,
  FluorescenceStatusReducer:
    state.masterReducer.Fluorescence.FluorescenceStatus,
  //   getAgentList: state.DeliveryAgentReducer.getAgentList,
});

const mapDispatchToProps = (dispatch) => ({
  getFluorescence: () => dispatch(getFluorescence()),
  deleteFluorescence: (id) => dispatch(deleteFluorescence(id)),
  changeFluorescenceStatus: (details) =>
    dispatch(changeFluorescenceStatus(details)),
  resetDeleteFluorescence: (id) =>
    dispatch({ type: RESET_DELETE_FLUORESCENCE }),
  resetAddFluorescence: () => dispatch({ type: RESET_ADD_FLUORESCENCE }),
  resetUpdateFluorescence: () => dispatch({ type: RESET_UPDATE_FLUORESCENCE }),
  resetFluorescenceStatus: (id) =>
    dispatch({ type: RESET_FLUORESCENCE_STATUS }),
});

export default connect(mapStateToProp, mapDispatchToProps)(FluorescenceList);
