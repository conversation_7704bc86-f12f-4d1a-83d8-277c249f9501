import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory, useParams } from "react-router";
import {  addFluorescence,  updateFluorescence} from "../../../Services/actions/masterAction";

function AddFluorescence(props) {
  const history = useHistory();
  const params = useParams();

  const [state, setState] = useState({
    name: "",
    type: "",
  });

  const [error, setError] = useState({
    name: false,
    type: false,
  });

  useEffect(() => {
    if (!!params.id && props.getFluorescenceReducer.success) {
      const data = props.getFluorescenceReducer.data;
      const details = data.find((e) => {
        return parseInt(e.id) === parseInt(params.id);
      });
      setState((prevState) => ({
        ...prevState,
        name: details.name,
        type: details.type.toString(),
      }));
    }
  }, [params, props.getFluorescenceReducer]);

  useEffect(() => {
    if (props.addFluorescenceReducer.success) {
      history.push("/fluorescence");
    }
  }, [props.addFluorescenceReducer]);

  useEffect(() => {
    if (props.updateFluorescenceReducer.success) {
      history.push("/fluorescence");
    }
  }, [props.updateFluorescenceReducer]);

  const handleInput = (e) => {
    const { name, value } = e.target;
    setState((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    switch (name) {
      case "name":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            name: "* Please enter fluorescence",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            name: false,
          }));
        }
        break;

      case "type":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            type: "* Please select type",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            type: false,
          }));
        }
        break;
      default:
        break;
    }
  };

  const addFluorescence = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter fluorescence",
      }));
    } else if (state.type === "") {
      setError((prevState) => ({
        ...prevState,
        type: "* Please select type",
      }));
    }
    if (state.name !== "" && state.type !== "" && !error.name && !error.type) {
      const details = {
        name: state.name,
        type: parseInt(state.type),
      };
      props.addFluorescence(details);
    }
  };

  const updateFluorescence = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter fluorescence",
      }));
    } else if (state.type === "") {
      setError((prevState) => ({
        ...prevState,
        type: "* Please select type",
      }));
    }

    if (state.name !== "" && state.type !== "" && !error.name && !error.type) {
      const details = {
        name: state.name,
        type: parseInt(state.type),
      };
      props.updateFluorescence({
        details: details,
        id: params.id,
      });
    }
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          {!!params.id ? "Update" : "Add"} -{" "}
          <span className="fw-semi-bold">Fluorescence</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">
            {!!params.id ? "Update" : "Add"} Fluorescence
          </h4>

          <div className="col-lg-3 col-md-6">
            <div className="mb-4">
              <label className="form-label">Fluorescence</label>
              <input
                placeholder="Enter Fluorescence"
                name="name"
                type="text"
                className="form-control"
                value={state.name}
                onChange={handleInput}
              />
              {error.name && (
                <div>
                  <span className="text-danger h6">{error.name}</span>
                </div>
              )}
            </div>
            <div className="mb-4">
              <label className="form-label">Fancy Fluorescence Type</label>
              <select
                className="form-select"
                name="type"
                value={state.type}
                onChange={handleInput}
              >
                <option value="">Select Fluorescence type</option>
                <option value={"0"}>None</option>
                <option value={"1"}>Color</option>
                <option value={"2"}>BGM</option>
              </select>
              {error.type && (
                <div>
                  <span className="text-danger h6">{error.type}</span>
                </div>
              )}
            </div>
            <button
              className="btn btn-primary"
              onClick={!!params.id ? updateFluorescence : addFluorescence}
            >
              {!!params.id ? "Update" : "Submit"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

const mapStateToProp = (state) => ({
  addFluorescenceReducer: state.masterReducer.Fluorescence.addFluorescence,
  getFluorescenceReducer: state.masterReducer.Fluorescence.getFluorescence,
  updateFluorescenceReducer:
    state.masterReducer.Fluorescence.updateFluorescence,
});

const mapDispatchToProps = (dispatch) => ({
  addFluorescence: (details) => dispatch(addFluorescence(details)),
  updateFluorescence: (details) => dispatch(updateFluorescence(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddFluorescence);
