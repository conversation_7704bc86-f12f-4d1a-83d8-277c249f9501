import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory, useParams } from "react-router-dom";
import {
  addClarity,
  updateClarity,
} from "../../../Services/actions/masterAction";
function AddClarity(props) {
  const history = useHistory();
  const params = useParams();

  const [state, setState] = useState({
    name: "",
  });

  const [error, setError] = useState({
    name: false,
  });

  useEffect(() => {
    if (!!params.id && props.getClarityReducer.success) {
      const data = props.getClarityReducer.data;
      const _name = data.find((e) => {
        return parseInt(e.id) === parseInt(params.id);
      }).name;
      setState((prevState) => ({
        ...prevState,
        name: _name,
      }));
    }
  }, [params, props.getClarityReducer]);

  useEffect(() => {
    if (props.addClarityReducer.success) {
      history.push("/clarity");
    }
  }, [props.addClarityReducer]);

  useEffect(() => {
    if (props.updateClarityReducer.success) {
      history.push("/clarity");
    }
  }, [props.updateClarityReducer]);

  const handleInput = (e) => {
    const { name, value } = e.target;
    setState((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    switch (name) {
      case "name":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            name: "* Please enter clarity name",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            name: false,
          }));
        }
        break;
      default:
        break;
    }
  };

  const addClarity = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter clarity name",
      }));
    }
    if (state.name !== "" && !error.name) {
      const details = {
        name: state.name,
      };
      props.addClarity(details);
    }
  };

  const updateClarity = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter clarity name",
      }));
    }
    if (state.name !== "" && !error.name) {
      const details = {
        name: state.name,
      };
      props.updateClarity({
        details: details,
        id: params.id,
      });
    }
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          {!!params.id ? "Update" : "Add"} -{" "}
          <span className="fw-semi-bold">Clarity</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">
            {!!params.id ? "Update" : "Add"} Clarity
          </h4>

          <div className="col-lg-3 col-md-4">
            <div className="mb-4">
              <label className="form-label">Clarity</label>
              <input
                placeholder="Enter Clarity Name"
                name="name"
                type="text"
                className="form-control"
                value={state.name}
                onChange={handleInput}
              />
              {error.name && (
                <div>
                  <span className="text-danger h6">{error.name}</span>
                </div>
              )}
            </div>
            <button
              className="btn btn-primary"
              onClick={!!params.id ? updateClarity : addClarity}
            >
              {!!params.id ? "Update" : "Submit"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

const mapStateToProp = (state) => ({
  addClarityReducer: state.masterReducer.Clarity.addClarity,
  getClarityReducer: state.masterReducer.Clarity.getClarity,
  updateClarityReducer: state.masterReducer.Clarity.updateClarity,
  //   getAgentList: state.DeliveryAgentReducer.getAgentList,
});

const mapDispatchToProps = (dispatch) => ({
  addClarity: (details) => dispatch(addClarity(details)),
  updateClarity: (details) => dispatch(updateClarity(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddClarity);
