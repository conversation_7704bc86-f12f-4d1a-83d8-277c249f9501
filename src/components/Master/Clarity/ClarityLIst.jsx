import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Dropdown<PERSON>oggle,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
} from "reactstrap";
import {
  changeClarityStatus,
  deleteClarity,
  getClarity,
} from "../../../Services/actions/masterAction";
import {
  RESET_ADD_CLARITY,
  RESET_CLARITY_STATUS,
  RESET_DELETE_CLARITY,
  RESET_UPDATE_CLARITY,
} from "../../../Services/Constant";
import Loader from "../../Loader/Loader";
import { toast } from "react-toastify";

function ClarityList(props) {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isDelete, setIsDelete] = useState(false);
  const [deleteId, setDeleteId] = useState("");

  useEffect(() => {
    props.getClarity();
  }, []);

  useEffect(() => {
    setLoading(props.getClarityReducer.loading);
    if (props.getClarityReducer.success) {
      const data = props.getClarityReducer.data;
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          name: e.name,
          status: e.status,
          id: e.id,
        };
      });
      setState([...list]);
    }
  }, [props.getClarityReducer]);

  const handleDeleteClarity = (id) => {
    props.deleteClarity(id);
  };

  useEffect(() => {
    if (props.deleteClarityReducer.success) {
      props.getClarity();
      setIsDelete(false);
      setDeleteId("");
      toast.success("clarity deleted successfully...");
      props.resetDeleteClarity();
    }
  }, [props.deleteClarityReducer]);

  const handleStatus = async (id, status) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeClarityStatus({
      id: id,
      details: details,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.clarityStatusReducer.success) {
      // props.getClarity();
      props.resetClarityStatus();
    }
  }, [props.clarityStatusReducer]);

  useEffect(() => {
    if (props.addClarityReducer.success) {
      props.getClarity();
      toast.success("clarity added successfully...");
      props.resetAddClarity();
    }
  }, [props.addClarityReducer]);

  useEffect(() => {
    if (props.updateClarityReducer.success) {
      props.getClarity();
      toast.success("clarity updated successfully...");
      props.resetUpdateClarity();
    }
  }, [props.updateClarityReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.id, value.status)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <>
        <Link className="btn-action me-2" to={`/clarity/${value.id}`}>
          <i className="fa fa-pencil-square-o" />
        </Link>
        <button
          className="btn-action"
          onClick={() => {
            setIsDelete(true);
            setDeleteId(value.id);
          }}
        >
          <i className="fa fa-trash" />
        </button>
      </>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Clarity - <span className="fw-semi-bold">Listing</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <div className="text-end mb-4">
            <Link to="/clarity/add" className="btn btn-primary">
              Add Clarity
            </Link>
          </div>
          {loading ? (
            <Loader />
          ) : (
            <BootstrapTable
              bordered={false}
              data={state}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`coustom-table stone-table table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="name" width="100%" dataSort>
                <span className="fs-sm">Clarity</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">status</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          )}
        </div>
      </div>
      <Modal
        size="sm"
        isOpen={isDelete}
        aria-labelledby="contained-modal-title-vcenter"
        toggle={() => {
          setIsDelete(false);
          setDeleteId("");
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setDeleteId("");
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this clarity?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setDeleteId("");
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleDeleteClarity(deleteId);
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
}

const mapStateToProp = (state) => ({
  getClarityReducer: state.masterReducer.Clarity.getClarity,
  addClarityReducer: state.masterReducer.Clarity.addClarity,
  deleteClarityReducer: state.masterReducer.Clarity.deleteClarity,
  updateClarityReducer: state.masterReducer.Clarity.updateClarity,
  clarityStatusReducer: state.masterReducer.Clarity.clarityStatus,
  //   getAgentList: state.DeliveryAgentReducer.getAgentList,
});

const mapDispatchToProps = (dispatch) => ({
  getClarity: () => dispatch(getClarity()),
  deleteClarity: (id) => dispatch(deleteClarity(id)),
  changeClarityStatus: (details) => dispatch(changeClarityStatus(details)),
  resetDeleteClarity: (id) => dispatch({ type: RESET_DELETE_CLARITY }),
  resetAddClarity: () => dispatch({ type: RESET_ADD_CLARITY }),
  resetUpdateClarity: () => dispatch({ type: RESET_UPDATE_CLARITY }),
  resetClarityStatus: (id) => dispatch({ type: RESET_CLARITY_STATUS }),
});

export default connect(mapStateToProp, mapDispatchToProps)(ClarityList);
