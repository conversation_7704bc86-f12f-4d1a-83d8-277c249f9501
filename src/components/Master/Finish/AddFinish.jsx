import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory, useParams } from "react-router";
import {  addFinish,  updateFinish} from "../../../Services/actions/masterAction";

function AddFinish(props) {
  const history = useHistory();
  const params = useParams();

  const [state, setState] = useState({
    name: "",
    type: "",
  });

  const [error, setError] = useState({
    name: false,
    type: false,
  });

  useEffect(() => {
    if (!!params.id && props.getFinishReducer.success) {
      const data = props.getFinishReducer.data;
      const details = data.find((e) => {
        return parseInt(e.id) === parseInt(params.id);
      });
      setState((prevState) => ({
        ...prevState,
        name: details.name,
        type: details.type.toString(),
      }));
    }
  }, [params, props.getFinishReducer]);

  useEffect(() => {
    if (props.addFinishReducer.success) {
      history.push("/finish");
    }
  }, [props.addFinishReducer]);

  useEffect(() => {
    if (props.updateFinishReducer.success) {
      history.push("/finish");
    }
  }, [props.updateFinishReducer]);

  const handleInput = (e) => {
    const { name, value } = e.target;
    setState((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    switch (name) {
      case "name":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            name: "* Please enter finish",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            name: false,
          }));
        }
        break;

      case "type":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            type: "* Please select type",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            type: false,
          }));
        }
        break;

      default:
        break;
    }
  };

  const addFinish = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter finish",
      }));
    } else if (state.type === "") {
      setError((prevState) => ({
        ...prevState,
        type: "* Please select type",
      }));
    }

    if (state.name !== "" && state.type !== "" && !error.name && !error.type) {
      const details = {
        name: state.name,
        type: parseInt(state.type),
        specific_type: 0,
      };
      props.addFinish(details);
    }
  };

  const updateFinish = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter finish name",
      }));
    } else if (state.type === "") {
      setError((prevState) => ({
        ...prevState,
        type: "* Please select type",
      }));
    }

    if (state.name !== "" && state.type !== "" && !error.name && !error.type) {
      const details = {
        name: state.name,
        type: parseInt(state.type),
        specific_type: 0,
      };
      props.updateFinish({
        details: details,
        id: params.id,
      });
    }
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          {!!params.id ? "Update" : "Add"} -{" "}
          <span className="fw-semi-bold">Finish</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">
            {!!params.id ? "Update" : "Add"} Finish
          </h4>

          <div className="col-lg-3 col-md-4">
            <div className="mb-4">
              <label className="form-label">Finish</label>
              <input
                placeholder="Enter Finish name"
                name="name"
                type="text"
                className="form-control"
                value={state.name}
                onChange={handleInput}
              />
              {error.name && (
                <div>
                  <span className="text-danger h6">{error.name}</span>
                </div>
              )}
            </div>
            <div className="mb-4">
              <label className="form-label">Finish Type</label>
              <select
                className="form-select"
                name="type"
                value={state.type}
                onChange={handleInput}
              >
                <option value="">Select Finish type</option>
                <option value={"0"}>Cut</option>
                <option value={"1"}>Polish</option>
                <option value={"2"}>Symmetry</option>
              </select>
              {error.type && (
                <div>
                  <span className="text-danger h6">{error.type}</span>
                </div>
              )}
            </div>
            <button
              className="btn btn-primary"
              onClick={!!params.id ? updateFinish : addFinish}
            >
              {!!params.id ? "Update" : "Submit"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

const mapStateToProp = (state) => ({
  addFinishReducer: state.masterReducer.Finish.addFinish,
  getFinishReducer: state.masterReducer.Finish.getFinish,
  updateFinishReducer: state.masterReducer.Finish.updateFinish,
});

const mapDispatchToProps = (dispatch) => ({
  addFinish: (details) => dispatch(addFinish(details)),
  updateFinish: (details) => dispatch(updateFinish(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddFinish);
