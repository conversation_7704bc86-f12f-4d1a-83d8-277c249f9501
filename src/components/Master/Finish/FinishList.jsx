import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import {  Button,  Dropdown,  DropdownItem,  DropdownMenu,  DropdownToggle,  Modal,  Modal<PERSON>ody,  Modal<PERSON>ooter,  ModalHeader,} from "reactstrap";
import {  changeFinishStatus,  deleteFinish,  getFinish,} from "../../../Services/actions/masterAction";
import {  RESET_ADD_FINISH,  RESET_DELETE_FINISH,  RESET_FINISH_STATUS,  RESET_UPDATE_FINISH,} from "../../../Services/Constant";
import Loader from "../../Loader/Loader";
import { toast } from "react-toastify";

function FinishList(props) {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isDelete, setIsDelete] = useState(false);
  const [deleteId, setDeleteId] = useState("");

  useEffect(() => {
    props.getFinish();
  }, []);

  useEffect(() => {
    setLoading(props.getFinishReducer.loading);
    if (props.getFinishReducer.success) {
      const data = props.getFinishReducer.data;
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          name: e.name,
          type: e.type === 0 ? "Cut" : e.type === 1 ? "Polish" : "Symmetry",
          status: e.status,
          id: e.id,
        };
      });
      setState([...list]);
    }
  }, [props.getFinishReducer]);

  const handleDeleteFinish = (id) => {
    props.deleteFinish(id);
  };

  useEffect(() => {
    if (props.deleteFinishReducer.success) {
      props.getFinish();
      setIsDelete(false);
      setDeleteId("");
      toast.success("finish deleted successfully...");
      props.resetDeleteFinish();
    }
  }, [props.deleteFinishReducer]);

  const handleStatus = async (id, status) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeFinishStatus({
      id: id,
      details: details,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.FinishStatusReducer.success) {
      // props.getFinish();
      props.resetFinishStatus();
    }
  }, [props.FinishStatusReducer]);

  useEffect(() => {
    if (props.addFinishReducer.success) {
      props.getFinish();
      toast.success("finish added successfully...");
      props.resetAddFinish();
    }
  }, [props.addFinishReducer]);

  useEffect(() => {
    if (props.updateFinishReducer.success) {
      props.getFinish();
      toast.success("finish updated successfully...");
      props.resetUpdateFinish();
    }
  }, [props.updateFinishReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.id, value.status)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center align-items-center`}>
        <Link className="" to={`/finish/${value.id}`}>
          <i className="fa fa-pencil-square-o" />
        </Link>
        <div
          className="btn text-danger"
          onClick={() => {
            setIsDelete(true);
            setDeleteId(value.id);
          }}
        >
          <i className="fa fa-trash" />
        </div>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Finish - <span className="fw-semi-bold">Listing</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <div className="text-end mb-4">
            <Link to="/finish/add" className="btn btn-primary">
              Add Finish
            </Link>
          </div>
          {loading ? (
            <Loader />
          ) : (
            <BootstrapTable
              bordered={false}
              data={state}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`coustom-table stone-table table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo" width="50px">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="name" dataSort>
                <span className="fs-sm">Finish</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="type" width="100%" dataSort>
                <span className="fs-sm">Type</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">status</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          )}
        </div>
      </div>
      <Modal
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        isOpen={isDelete}
        toggle={() => {
          setIsDelete(false);
          setDeleteId("");
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setDeleteId("");
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this finish?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setDeleteId("");
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleDeleteFinish(deleteId);
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
}

const mapStateToProp = (state) => ({
  getFinishReducer: state.masterReducer.Finish.getFinish,
  addFinishReducer: state.masterReducer.Finish.addFinish,
  deleteFinishReducer: state.masterReducer.Finish.deleteFinish,
  updateFinishReducer: state.masterReducer.Finish.updateFinish,
  FinishStatusReducer: state.masterReducer.Finish.FinishStatus,
  //   getAgentList: state.DeliveryAgentReducer.getAgentList,
});

const mapDispatchToProps = (dispatch) => ({
  getFinish: () => dispatch(getFinish()),
  deleteFinish: (id) => dispatch(deleteFinish(id)),
  changeFinishStatus: (details) => dispatch(changeFinishStatus(details)),
  resetDeleteFinish: (id) => dispatch({ type: RESET_DELETE_FINISH }),
  resetAddFinish: () => dispatch({ type: RESET_ADD_FINISH }),
  resetUpdateFinish: () => dispatch({ type: RESET_UPDATE_FINISH }),
  resetFinishStatus: (id) => dispatch({ type: RESET_FINISH_STATUS }),
});

export default connect(mapStateToProp, mapDispatchToProps)(FinishList);
