import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import { Button, Dropdown, DropdownItem, DropdownMenu, Dropdown<PERSON>oggle, <PERSON>dal, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>ooter, ModalHeader, } from "reactstrap";
import { changeFancyColorStatus, deleteFancyColor, getFancyColor, } from "../../../Services/actions/masterAction";
import { RESET_ADD_FANCY_COLOR, RESET_DELETE_FANCY_COLOR, RESET_FANCY_COLOR_STATUS, RESET_UPDATE_FANCY_COLOR, } from "../../../Services/Constant";
import Loader from "../../Loader/Loader";
import { toast } from "react-toastify";

function FancyColorList(props) {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isDelete, setIsDelete] = useState(false);
  const [deleteId, setDeleteId] = useState("");

  useEffect(() => {
    props.getFancyColor();
  }, []);

  useEffect(() => {
    setLoading(props.getFancyColorReducer.loading);
    if (props.getFancyColorReducer.success) {
      const data = props.getFancyColorReducer.data;
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          name: e.name,
          type:
            e.type === 0 ? "Colors" : e.type === 1 ? "Overtone" : "Intensity",
          image: e.image,
          status: e.status,
          id: e.id,
        };
      });
      setState([...list]);
    }
  }, [props.getFancyColorReducer]);

  const handleDeleteFancyColor = (id) => {
    props.deleteFancyColor(id);
  };

  useEffect(() => {
    if (props.deleteFancyColorReducer.success) {
      props.getFancyColor();
      setIsDelete(false);
      setDeleteId("");
      toast.success("Fancycolor deleted successfully...");
      props.resetDeleteFancyColor();
    }
  }, [props.deleteFancyColorReducer]);

  const handleStatus = async (id, status) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeFancyColorStatus({
      id: id,
      details: details,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.FancyColorStatusReducer.success) {
      // props.getFancyColor();
      props.resetFancyColorStatus();
    }
  }, [props.FancyColorStatusReducer]);

  useEffect(() => {
    if (props.addFancyColorReducer.success) {
      props.getFancyColor();
      toast.success("Fancycolor added successfully...");
      props.resetAddFancyColor();
    }
  }, [props.addFancyColorReducer]);

  useEffect(() => {
    if (props.updateFancyColorReducer.success) {
      props.getFancyColor();
      toast.success("Fancycolor updated successfully...");
      props.resetUpdateFancyColor();
    }
  }, [props.updateFancyColorReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.id, value.status)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };
  const imageCell = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        {!!value.image && value.image !== "" ? (
          <img src={value.image} style={{ width: "50px", height: "50px" }} />
        ) : (
          "N/A"
        )}
      </div>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center align-items-center`}>
        <Link className="" to={`/fancy-color/${value.id}`}>
          <i className="fa fa-pencil-square-o" />
        </Link>
        <div
          className="btn text-danger"
          onClick={() => {
            setIsDelete(true);
            setDeleteId(value.id);
          }}
        >
          <i className="fa fa-trash" />
        </div>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };
  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Fancy-Color - <span className="fw-semi-bold">Listing</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <div className="text-end mb-4">
            <Link to="/fancy-color/add" className="btn btn-primary">
              Add Fancy color
            </Link>
          </div>
          {loading ? (
            <Loader />
          ) : (
            <BootstrapTable
              bordered={false}
              data={state}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`coustom-table stone-table table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo" width="50px">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="name" dataSort>
                <span className="fs-sm">Finish</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="type" dataSort>
                <span className="fs-sm">Type</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="image"
                dataFormat={imageCell.bind(this)}
                width="100%"
              >
                <span className="fs-sm">Image</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">status</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          )}
        </div>
      </div>
      <Modal
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        isOpen={isDelete}
        toggle={() => {
          setIsDelete(false);
          setDeleteId("");
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setDeleteId("");
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this fancy-color?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setDeleteId("");
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleDeleteFancyColor(deleteId);
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
}

const mapStateToProp = (state) => ({
  getFancyColorReducer: state.masterReducer.FancyColor.getFancyColor,
  addFancyColorReducer: state.masterReducer.FancyColor.addFancyColor,
  deleteFancyColorReducer: state.masterReducer.FancyColor.deleteFancyColor,
  updateFancyColorReducer: state.masterReducer.FancyColor.updateFancyColor,
  FancyColorStatusReducer: state.masterReducer.FancyColor.FancyColorStatus,
  //   getAgentList: state.DeliveryAgentReducer.getAgentList,
});

const mapDispatchToProps = (dispatch) => ({
  getFancyColor: () => dispatch(getFancyColor()),
  deleteFancyColor: (id) => dispatch(deleteFancyColor(id)),
  changeFancyColorStatus: (details) =>
    dispatch(changeFancyColorStatus(details)),
  resetDeleteFancyColor: (id) => dispatch({ type: RESET_DELETE_FANCY_COLOR }),
  resetAddFancyColor: () => dispatch({ type: RESET_ADD_FANCY_COLOR }),
  resetUpdateFancyColor: () => dispatch({ type: RESET_UPDATE_FANCY_COLOR }),
  resetFancyColorStatus: (id) => dispatch({ type: RESET_FANCY_COLOR_STATUS }),
});

export default connect(mapStateToProp, mapDispatchToProps)(FancyColorList);
