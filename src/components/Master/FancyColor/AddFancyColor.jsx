import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory, useParams } from "react-router";
import { addFancyColor, getFancyColor, updateFancyColor, } from "../../../Services/actions/masterAction";

function AddFancyColor(props) {
  const history = useHistory();
  const params = useParams();

  const [state, setState] = useState({
    name: "",
    type: "",
    image: "",
    imageFile: null,
    Imagename: "",
  });

  const [error, setError] = useState({
    name: false,
    type: false,
    image: false,
  });
  useEffect(() => {
    if (!props.getFancyColorReducer.success) {
      props.getFancyColor();
    }
  }, []);

  useEffect(() => {
    if (!!params.id && props.getFancyColorReducer.success) {
      const data = props.getFancyColorReducer.data;
      const details = data.find((e) => {
        return parseInt(e.id) === parseInt(params.id);
      });
      const _list = details.image.split("/");
      const imageName = _list[_list.length - 1];
      setState((prevState) => ({
        ...prevState,
        name: details.name,
        type: details.type.toString(),
        image: details.image,
        imageFile: details.image,
        Imagename: imageName,
      }));
    }
  }, [params, props.getFancyColorReducer]);

  useEffect(() => {
    if (props.addFancyColorReducer.success) {
      history.push("/fancy-color");
    }
  }, [props.addFancyColorReducer]);

  useEffect(() => {
    if (props.updateFancyColorReducer.success) {
      history.push("/fancy-color");
    }
  }, [props.updateFancyColorReducer]);

  const handleInput = (e) => {
    const { name, value } = e.target;

    if (name === "type" && value !== "0") {
      setState((prevState) => ({
        ...prevState,
        image: "",
      }));
    }
    if (name === "image") {
      setState((prev) => ({
        ...prev,
        imageFile: e.target.files[0],
        Imagename: e.target.files[0].name,
        [name]: value,
      }));
    } else {
      setState((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    }

    switch (name) {
      case "name":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            name: "* Please enter fancy color name",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            name: false,
          }));
        }
        break;

      case "type":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            type: "* Please select type",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            type: false,
          }));
        }
        break;

      default:
        break;
    }
  };
  const addFancyColor = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter fancy color name",
      }));
    } else if (state.type === "") {
      setError((prevState) => ({
        ...prevState,
        type: "* Please select type",
      }));
    } else if (state.image === "") {
      setError((prevState) => ({
        ...prevState,
        image: "* Please select image",
      }));
    }
    if (state.name !== "" && state.type !== "" && !error.name && !error.type) {
      const details = {
        name: state.name,
        type: parseInt(state.type),
        ...(state.type === "0" && {
          image: state.imageFile !== state.image ? state.imageFile : null,
        }),
      };
      props.addFancyColor(details);
    }
  };

  const updateFancyColor = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter fancy color name",
      }));
    } else if (state.type === "") {
      setError((prevState) => ({
        ...prevState,
        type: "* Please select type",
      }));
    }

    if (state.name !== "" && state.type !== "" && !error.name && !error.type) {
      const details = {
        name: state.name,
        type: parseInt(state.type),
        ...(state.type === "0" && {
          image: state.imageFile !== state.image ? state.imageFile : null,
        }),
      };
      props.updateFancyColor({
        details: details,
        id: params.id,
      });
    }
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          {!!params.id ? "Update" : "Add"} -{" "}
          <span className="fw-semi-bold">Fancy Color</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">
            {!!params.id ? "Update" : "Add"} Fancy Color
          </h4>

          <div className="col-lg-3 col-md-6">
            <div className="mb-4">
              <label className="form-label">Fancy Color</label>
              <input
                placeholder="Enter Fancy color Name"
                name="name"
                type="text"
                className="form-control"
                value={state.name}
                onChange={handleInput}
              />
              {error.name && (
                <div>
                  <span className="text-danger h6">{error.name}</span>
                </div>
              )}
            </div>
            <div className="mb-4">
              <label className="form-label">Fancy Color Type</label>
              <select
                className="form-select"
                name="type"
                value={state.type}
                onChange={handleInput}
              >
                <option value="">Select Fancy color type</option>
                <option value={"0"}>Colors</option>
                <option value={"1"}>Overtone</option>
                <option value={"2"}>Intensity</option>
              </select>
              {error.type && (
                <div>
                  <span className="text-danger h6">{error.type}</span>
                </div>
              )}
            </div>
            {state.type === "0" && (
              <div className="mb-4">
                <label className="form-label">Image</label>
                {state.image === "" ? (
                  <input
                    // placeholder="Enter Fancy color Name"
                    name="image"
                    type="file"
                    className="form-control"
                    value={state.image === null ? "" : state.image}
                    onChange={handleInput}
                    accept="image/jpeg, image/jpg, image/png"
                  />
                ) : (
                  <div className="d-flex justify-content-between align-items-center form-control">
                    <div>{state.Imagename}</div>
                    <div
                      style={{ cursor: "pointer" }}
                      onClick={() => {
                        setState((pre) => ({
                          ...pre,
                          image: "",
                          imageFile: null,
                          Imagename: "",
                        }));
                      }}
                    >
                      <i className="fas fa-times text-danger"></i>
                    </div>
                  </div>
                )}
                {error.image && (
                  <div>
                    <span className="text-danger h6">{error.image}</span>
                  </div>
                )}
              </div>
            )}
            <button
              className="btn btn-primary"
              onClick={!!params.id ? updateFancyColor : addFancyColor}
            >
              {!!params.id ? "Update" : "Submit"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

const mapStateToProp = (state) => ({
  addFancyColorReducer: state.masterReducer.FancyColor.addFancyColor,
  getFancyColorReducer: state.masterReducer.FancyColor.getFancyColor,
  updateFancyColorReducer: state.masterReducer.FancyColor.updateFancyColor,
});

const mapDispatchToProps = (dispatch) => ({
  getFancyColor: () => dispatch(getFancyColor()),
  addFancyColor: (details) => dispatch(addFancyColor(details)),
  updateFancyColor: (details) => dispatch(updateFancyColor(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddFancyColor);
