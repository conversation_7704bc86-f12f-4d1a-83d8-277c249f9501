import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  Modal,
  Modal<PERSON>ody,
  Modal<PERSON>ooter,
  ModalHeader,
} from "reactstrap";
import {
  changeShapeStatus,
  deleteShape,
  getShape,
} from "../../../Services/actions/masterAction";
import {
  RESET_ADD_SHAPE,
  RESET_DELETE_SHAPE,
  RESET_SHAPE_STATUS,
  RESET_UPDATE_SHAPE,
} from "../../../Services/Constant";
import Loader from "../../Loader/Loader";
import { toast } from "react-toastify";

const ShapeList = (props) => {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isDelete, setIsDelete] = useState(false);
  const [deleteId, setDeleteId] = useState("");

  useEffect(() => {
    props.getShape()
  }, []);

  useEffect(() => {
    setLoading(props.getShapeReducer.loading);
    if (props.getShapeReducer.success) {
      const data = props.getShapeReducer.data;
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          name: e.name,
          icon: e.icon,
          status: e.status,
          id: e.id,
        };
      });
      setState([...list]);
    }
  }, [props.getShapeReducer]);

  const handleDeleteShape = (id) => {
    props.deleteShape(id);
  };

  useEffect(() => {
    if (props.deleteShapeReducer.success) {
      props.getShape();
      setIsDelete(false);
      setDeleteId("");
      toast.success("shape deleted successfully...");
      props.resetDeleteShape();
    }
  }, [props.deleteShapeReducer]);

  const handleStatus = async (id, status) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeShapeStatus({
      id: id,
      details: details,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.ShapeStatusReducer.success) {
      // props.getShape();
      props.resetShapeStatus();
    }
  }, [props.ShapeStatusReducer]);

  useEffect(() => {
    if (props.addShapeReducer.success) {
      props.getShape();
      toast.success("shape added successfully...");
      props.resetAddShape();
    }
  }, [props.addShapeReducer]);

  useEffect(() => {
    if (props.updateShapeReducer.success) {
      props.getShape();
      toast.success("shape updated successfully...");
      props.resetUpdateShape();
    }
  }, [props.updateShapeReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.id, value.status)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center align-items-center`}>
        <Link className="" to={`/shape/${value.id}`}>
          <i className="fa fa-pencil-square-o" />
        </Link>
        <div
          className="btn text-danger"
          onClick={() => {
            setIsDelete(true);
            setDeleteId(value.id);
          }}
        >
          <i className="fa fa-trash" />
        </div>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Shape - <span className="fw-semi-bold">Listing</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <div className="text-end mb-4">
            <Link to="/shape/all" className="btn btn-secondary me-2">
              View All Shape
            </Link>
            <Link to="/shape/add" className="btn btn-primary">
              Add Shape
            </Link>
          </div>
          {loading ? (
            <Loader />
          ) : (
            <BootstrapTable
              bordered={false}
              data={state}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`coustom-table stone-table table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo" width="50px">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="name" dataSort width="200px">
                <span className="fs-sm">Shape</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="icon" dataSort width="75%">
                <span className="fs-sm">Icon</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">status</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          )}
        </div>
      </div>
      <Modal
        size="sm"
aria-labelledby="contained-modal-title-vcenter"
        centered
        isOpen={isDelete}
        toggle={() => {
          setIsDelete(false);
          setDeleteId("");
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setDeleteId("");
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this shape?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setDeleteId("");
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleDeleteShape(deleteId);
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

const mapStateToProp = (state) => ({
  getShapeReducer: state.masterReducer.Shape.getShape,
  addShapeReducer: state.masterReducer.Shape.addShape,
  updateShapeReducer: state.masterReducer.Shape.updateShape,
  deleteShapeReducer: state.masterReducer.Shape.deleteShape,
  ShapeStatusReducer: state.masterReducer.Shape.ShapeStatus,
});

const mapDispatchToProps = (dispatch) => ({
  getShape: () => dispatch(getShape()),
  deleteShape: (id) => dispatch(deleteShape(id)),
  changeShapeStatus: (details) => dispatch(changeShapeStatus(details)),
  resetDeleteShape: (id) => dispatch({ type: RESET_DELETE_SHAPE }),
  resetAddShape: () => dispatch({ type: RESET_ADD_SHAPE }),
  resetUpdateShape: () => dispatch({ type: RESET_UPDATE_SHAPE }),
  resetShapeStatus: (id) => dispatch({ type: RESET_SHAPE_STATUS }),
});

export default connect(mapStateToProp, mapDispatchToProps)(ShapeList);
