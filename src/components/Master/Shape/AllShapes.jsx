import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import imageurl from "../../../images";

const AllShapes = () => {
    return (<>
        <div className="page-top-line">
            <h2 className="page-title">
                All - <span className="fw-semi-bold">Shape List</span>
            </h2>
        </div>
        <div className="card main-card">
            <div className="card-body">
                <div className="shape-container">
                    <div className="shape-box">
                        <img src={imageurl.round} className="img-fluid" alt="Round" />
                        <p>round.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.oval} className="img-fluid" alt="Round" />
                        <p>oval.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.pear} className="img-fluid" alt="Round" />
                        <p>pear.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.cushmod} className="img-fluid" alt="Round" />
                        <p>cushmod.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.cushBrill} className="img-fluid" alt="Round" />
                        <p>cushBrill.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.emerald} className="img-fluid" alt="Round" />
                        <p>emerald.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.radiant} className="img-fluid" alt="Round" />
                        <p>radiant.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.princess} className="img-fluid" alt="Round" />
                        <p>princess.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.asscher} className="img-fluid" alt="Round" />
                        <p>asscher.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.square} className="img-fluid" alt="Round" />
                        <p>square.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.marquise} className="img-fluid" alt="Round" />
                        <p>marquise.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.heart} className="img-fluid" alt="Round" />
                        <p>heart.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.trilliant} className="img-fluid" alt="Round" />
                        <p>trilliant.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.euroCut} className="img-fluid" alt="Round" />
                        <p>euroCut.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.oldMiner} className="img-fluid" alt="ico" />
                        <p>oldMiner.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.briolette} className="img-fluid" alt="ico" />
                        <p>briolette.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.roseCut} className="img-fluid" alt="ico" />
                        <p>roseCut.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.lozenge} className="img-fluid" alt="ico" />
                        <p>lozenge.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.baguette} className="img-fluid" alt="ico" />
                        <p>baguette.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.tapBaguette} className="img-fluid" alt="ico" />
                        <p>tapBaguette.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.halfMoon} className="img-fluid" alt="ico" />
                        <p>halfMoon.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.flanders} className="img-fluid" alt="ico" />
                        <p>flanders.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.trapezoid} className="img-fluid" alt="ico" />
                        <p>trapezoid.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.bullets} className="img-fluid" alt="ico" />
                        <p>bullets.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.kite} className="img-fluid" alt="ico" />
                        <p>kite.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.shield} className="img-fluid" alt="ico" />
                        <p>shield.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.star} className="img-fluid" alt="ico" />
                        <p>star.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.pentagonal} className="img-fluid" alt="ico" />
                        <p>pentagonal.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.hexagonal} className="img-fluid" alt="ico" />
                        <p>hexagonal.svg</p>
                    </div>
                    <div className="shape-box">
                        <img src={imageurl.octagonal} className="img-fluid" alt="ico" />
                        <p>octagonal.svg</p>
                    </div>
                   
                </div>
            </div>
        </div>
    </>)
}

export default AllShapes;