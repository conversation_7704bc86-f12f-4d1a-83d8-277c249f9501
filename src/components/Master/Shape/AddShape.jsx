import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory, useParams } from "react-router";
import { addShape, updateShape } from "../../../Services/actions/masterAction";

function AddShape(props) {
  const history = useHistory();
  const params = useParams();

  const [state, setState] = useState({
    name: "",
    icon: "",
  });
  const [error, setError] = useState({
    name: false,
    icon: false,
  });

  useEffect(() => {
    if (!!params.id && props.getShapeReducer.success) {
      const data = props.getShapeReducer.data;
      const details = data.find((e) => {
        return parseInt(e.id) === parseInt(params.id);
      });
      setState((prevState) => ({
        ...prevState,
        name: details.name,
        icon: details.icon,
      }));
    }
  }, [params, props.getShapeReducer]);

  useEffect(() => {
    if (props.addShapeReducer.success) {
      history.push("/shape");
    }
  }, [props.addShapeReducer]);

  useEffect(() => {
    if (props.updateShapeReducer.success) {
      history.push("/shape");
    }
  }, [props.updateShapeReducer]);

  const handleInput = (e) => {
    const { name, value } = e.target;
    setState((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    switch (name) {
      case "name":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            name: "* Please enter shape name",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            name: false,
          }));
        }
        break;

      case "icon":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            icon: "* Please enter icon value",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            icon: false,
          }));
        }
        break;

      default:
        break;
    }
  };

  const addShape = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter shape name",
      }));
    } else if (state.icon === "") {
      setError((prevState) => ({
        ...prevState,
        icon: "* Please enter icon value",
      }));
    }

    if (state.name !== "" && state.icon !== "" && !error.name && !error.icon) {
      const details = {
        name: state.name,
        icon: state.icon,
      };
      props.addShape(details);
    }
  };

  const updateShape = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter shape name",
      }));
    } else if (state.icon === "") {
      setError((prevState) => ({
        ...prevState,
        icon: "* Please enter icon value",
      }));
    }
    if (state.name !== "" && state.icon !== "" && !error.name && !error.icon) {
      const details = {
        name: state.name,
        icon: state.icon,
      };
      props.updateShape({
        details: details,
        id: params.id,
      });
    }
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          {!!params.id ? "Update" : "Add"} -{" "}
          <span className="fw-semi-bold">Shape</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">
            {!!params.id ? "Update" : "Add"} Shape
          </h4>

          <div className="col-lg-3 col-md-4">
            <div className="mb-4">
              <label className="form-label">Shape</label>
              <input
                placeholder="Enter Shape Name"
                name="name"
                type="text"
                className="form-control"
                value={state.name}
                onChange={handleInput}
              />
              {error.name && (
                <div>
                  <span className="text-danger h6">{error.name}</span>
                </div>
              )}
            </div>
            <div className="mb-4">
              <label className="form-label">Icon</label>
              <input
                placeholder="Enter Icon"
                name="icon"
                type="text"
                className="form-control"
                value={state.icon}
                onChange={handleInput}
              />
              {error.icon && (
                <div>
                  <span className="text-danger h6">{error.icon}</span>
                </div>
              )}
            </div>
            <button
              className="btn btn-primary"
              onClick={!!params.id ? updateShape : addShape}
            >
              {!!params.id ? "Update" : "Submit"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

const mapStateToProp = (state) => ({
  addShapeReducer: state.masterReducer.Shape.addShape,
  getShapeReducer: state.masterReducer.Shape.getShape,
  updateShapeReducer: state.masterReducer.Shape.updateShape,
  //   getAgentList: state.DeliveryAgentReducer.getAgentList,
});

const mapDispatchToProps = (dispatch) => ({
  addShape: (details) => dispatch(addShape(details)),
  updateShape: (details) => dispatch(updateShape(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddShape);
