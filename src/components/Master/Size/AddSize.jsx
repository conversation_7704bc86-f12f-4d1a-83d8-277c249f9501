import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory, useParams } from "react-router-dom";
import { addSize, updateSize } from "../../../Services/actions/masterAction";

function AddSize(props) {
  const history = useHistory();
  const params = useParams();

  const [state, setState] = useState({
    name: "",
  });

  const [error, setError] = useState({
    name: false,
  });

  useEffect(() => {
    if (!!params.id && props.getSizeReducer.success) {
      const data = props.getSizeReducer.data;
      const _name = data.find((e) => {
        return parseInt(e.id) === parseInt(params.id);
      }).name;
      setState((prevState) => ({
        ...prevState,
        name: _name,
      }));
    }
  }, [params, props.getSizeReducer]);

  useEffect(() => {
    if (props.addSizeReducer.success) {
      history.push("/size");
    }
  }, [props.addSizeReducer]);

  useEffect(() => {
    if (props.updateSizeReducer.success) {
      history.push("/size");
    }
  }, [props.updateSizeReducer]);

  const handleInput = (e) => {
    const { name, value } = e.target;
    setState((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    switch (name) {
      case "name":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            name: "* Please enter size",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            name: false,
          }));
        }
        break;
      default:
        break;
    }
  };

  const addSize = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter size",
      }));
    }
    if (state.name !== "" && !error.name) {
      const details = {
        name: state.name,
      };
      props.addSize(details);
    }
  };

  const updateSize = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter size",
      }));
    }
    if (state.name !== "" && !error.name) {
      const details = {
        name: state.name,
      };
      props.updateSize({
        details: details,
        id: params.id,
      });
    }
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          {!!params.id ? "Update" : "Add"} -{" "}
          <span className="fw-semi-bold">Size</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">
            {!!params.id ? "Update" : "Add"} Size
          </h4>

          <div className="col-lg-3 col-md-4">
            <div className="mb-4">
              <label className="form-label">Size</label>
              <input
                placeholder="Enter Size"
                name="name"
                type="text"
                className="form-control"
                value={state.name}
                onChange={handleInput}
              />
              {error.name && (
                <div>
                  <span className="text-danger h6">{error.name}</span>
                </div>
              )}
            </div>
            <button
              className="btn btn-primary"
              onClick={!!params.id ? updateSize : addSize}
            >
              {!!params.id ? "Update" : "Submit"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

const mapStateToProp = (state) => ({
  getSizeReducer: state.masterReducer.Size.getSize,
  addSizeReducer: state.masterReducer.Size.addSize,
  updateSizeReducer: state.masterReducer.Size.updateSize,
  //   getAgentList: state.DeliveryAgentReducer.getAgentList,
});

const mapDispatchToProps = (dispatch) => ({
  addSize: (details) => dispatch(addSize(details)),
  updateSize: (details) => dispatch(updateSize(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddSize);
