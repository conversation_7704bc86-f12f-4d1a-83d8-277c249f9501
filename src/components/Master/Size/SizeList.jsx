import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Dropdown<PERSON>oggle,
  Modal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
} from "reactstrap";
import {
  changeSizeStatus,
  deleteSize,
  getSize,
} from "../../../Services/actions/masterAction";
import {
  RESET_ADD_SIZE,
  RESET_DELETE_SIZE,
  RESET_SIZE_STATUS,
  RESET_UPDATE_SIZE,
} from "../../../Services/Constant";
import Loader from "../../Loader/Loader";
import { toast } from "react-toastify";

const SizeList = (props) => {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isDelete, setIsDelete] = useState(false);
  const [deleteId, setDeleteId] = useState("");

  useEffect(() => {
    props.getSize();
  }, []);

  useEffect(() => {
    setLoading(props.getSizeReducer.loading);
    if (props.getSizeReducer.success) {
      const data = props.getSizeReducer.data;
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          name: e.name,
          status: e.status,
          id: e.id,
        };
      });
      setState([...list]);
    }
  }, [props.getSizeReducer]);

  const handleDeleteSize = (id) => {
    props.deleteSize(id);
  };

  useEffect(() => {
    if (props.deleteSizeReducer.success) {
      props.getSize();
      setIsDelete(false);
      setDeleteId("");
      toast.success("size deleted successfully...");
      props.resetDeleteSize();
    }
  }, [props.deleteSizeReducer]);

  const handleStatus = async (id, status) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeSizeStatus({
      id: id,
      details: details,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.SizeStatusReducer.success) {
      // props.getSize();
      props.resetSizeStatus();
    }
  }, [props.SizeStatusReducer]);

  useEffect(() => {
    if (props.addSizeReducer.success) {
      props.getSize();
      toast.success("size added successfully...");
      props.resetAddSize();
    }
  }, [props.addSizeReducer]);

  useEffect(() => {
    if (props.updateSizeReducer.success) {
      props.getSize();
      toast.success("size updated successfully...");
      props.resetUpdateSize();
    }
  }, [props.updateSizeReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.id, value.status)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center align-items-center`}>
        <Link className="" to={`/size/${value.id}`}>
          <i className="fa fa-pencil-square-o" />
        </Link>
        <div
          className="btn text-danger"
          onClick={() => {
            setIsDelete(true);
            setDeleteId(value.id);
          }}
        >
          <i className="fa fa-trash" />
        </div>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Size - <span className="fw-semi-bold">Listing</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <div className="text-end mb-4">
            <Link to="/size/add" className="btn btn-primary">
              Add New Size
            </Link>
          </div>
          {loading ? (
            <Loader />
          ) : (
            <BootstrapTable
              bordered={false}
              data={state}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`coustom-table stone-table table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo" width="50px">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="name" width="100%" dataSort>
                <span className="fs-sm">Size</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">status</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          )}
        </div>
      </div>
      <Modal
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        isOpen={isDelete}
        toggle={() => {
          setIsDelete(false);
          setDeleteId("");
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setDeleteId("");
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this size?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setDeleteId("");
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleDeleteSize(deleteId);
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

const mapStateToProp = (state) => ({
  getSizeReducer: state.masterReducer.Size.getSize,
  addSizeReducer: state.masterReducer.Size.addSize,
  deleteSizeReducer: state.masterReducer.Size.deleteSize,
  updateSizeReducer: state.masterReducer.Size.updateSize,
  SizeStatusReducer: state.masterReducer.Size.SizeStatus,
  //   getAgentList: state.DeliveryAgentReducer.getAgentList,
});

const mapDispatchToProps = (dispatch) => ({
  getSize: () => dispatch(getSize()),
  deleteSize: (id) => dispatch(deleteSize(id)),
  changeSizeStatus: (details) => dispatch(changeSizeStatus(details)),
  resetDeleteSize: (id) => dispatch({ type: RESET_DELETE_SIZE }),
  resetAddSize: () => dispatch({ type: RESET_ADD_SIZE }),
  resetUpdateSize: () => dispatch({ type: RESET_UPDATE_SIZE }),
  resetSizeStatus: (id) => dispatch({ type: RESET_SIZE_STATUS }),
});

export default connect(mapStateToProp, mapDispatchToProps)(SizeList);
