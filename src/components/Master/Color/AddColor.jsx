import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory, useParams } from "react-router";
import { addColor, updateColor } from "../../../Services/actions/masterAction";

function AddColor(props) {
  const history = useHistory();
  const params = useParams();

  const [state, setState] = useState({
    name: "",
  });

  const [error, setError] = useState({
    name: false,
  });

  useEffect(() => {
    if (!!params.id && props.getColorReducer.success) {
      const data = props.getColorReducer.data;
      const _name = data.find((e) => {
        return parseInt(e.id) === parseInt(params.id);
      }).name;
      setState((prevState) => ({
        ...prevState,
        name: _name,
      }));
    }
  }, [params, props.getColorReducer]);

  useEffect(() => {
    if (props.addColorReducer.success) {
      history.push("/color");
    }
  }, [props.addColorReducer]);

  useEffect(() => {
    if (props.updateColorReducer.success) {
      history.push("/color");
    }
  }, [props.updateColorReducer]);

  const handleInput = (e) => {
    const { name, value } = e.target;
    setState((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    switch (name) {
      case "name":
        if (value === "") {
          setError((prevState) => ({
            ...prevState,
            name: "* Please enter color",
          }));
        } else {
          setError((prevState) => ({
            ...prevState,
            name: false,
          }));
        }
        break;
      default:
        break;
    }
  };

  const addColor = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter color",
      }));
    }
    if (state.name !== "" && !error.name) {
      const details = {
        name: state.name,
      };
      props.addColor(details);
    }
  };

  const updateColor = () => {
    if (state.name === "") {
      setError((prevState) => ({
        ...prevState,
        name: "* Please enter color",
      }));
    }
    if (state.name !== "" && !error.name) {
      const details = {
        name: state.name,
      };
      props.updateColor({
        details: details,
        id: params.id,
      });
    }
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          {!!params.id ? "Update" : "Add"} -{" "}
          <span className="fw-semi-bold">Color</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">
            {!!params.id ? "Update" : "Add"} Color
          </h4>

          <div className="col-lg-3 col-md-4">
            <div className="mb-4">
              <label className="form-label">Color</label>
              <input
                placeholder="Enter Color Name"
                name="name"
                type="text"
                className="form-control"
                value={state.name}
                onChange={handleInput}
              />
              {error.name && (
                <div>
                  <span className="text-danger h6">{error.name}</span>
                </div>
              )}
            </div>
            <button
              className="btn btn-primary"
              onClick={!!params.id ? updateColor : addColor}
            >
              {!!params.id ? "Update" : "Submit"}
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

const mapStateToProp = (state) => ({
  addColorReducer: state.masterReducer.Color.addColor,
  getColorReducer: state.masterReducer.Color.getColor,
  updateColorReducer: state.masterReducer.Color.updateColor,
});

const mapDispatchToProps = (dispatch) => ({
  addColor: (details) => dispatch(addColor(details)),
  updateColor: (details) => dispatch(updateColor(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddColor);
