import classNames from "classnames";
import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import {
  But<PERSON>,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
} from "reactstrap";
import {
  changeColorStatus,
  deleteColor,
  getColor,
} from "../../../Services/actions/masterAction";
import {
  RESET_ADD_COLOR,
  RESET_COLOR_STATUS,
  RESET_DELETE_COLOR,
  RESET_UPDATE_COLOR,
} from "../../../Services/Constant";
import Loader from "../../Loader/Loader";
import { toast } from "react-toastify";

const ColorList = (props) => {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isDelete, setIsDelete] = useState(false);
  const [deleteId, setDeleteId] = useState("");
  useEffect(() => {
    props.getColor();
  }, []);

  useEffect(() => {
    setLoading(props.getColorReducer.loading);
    if (props.getColorReducer.success) {
      const data = props.getColorReducer.data;
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          name: e.name,
          status: e.status,
          id: e.id,
        };
      });
      setState([...list]);
    }
  }, [props.getColorReducer]);

  const handleDeleteColor = (id) => {
    props.deleteColor(id);
  };

  useEffect(() => {
    if (props.deleteColorReducer.success) {
      props.getColor();
      setIsDelete(false);
      setDeleteId("");
      toast.success("color deleted succesfully...");
      props.resetDeleteColor();
    }
  }, [props.deleteColorReducer]);

  const handleStatus = async (id, status) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeColorStatus({
      id: id,
      details: details,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.ColorStatusReducer.success) {
      // props.getColor();
      props.resetColorStatus();
    }
  }, [props.ColorStatusReducer]);

  useEffect(() => {
    if (props.addColorReducer.success) {
      props.getColor();
      toast.success("color added succesfully...");
      props.resetAddColor();
    }
  }, [props.addColorReducer]);

  useEffect(() => {
    if (props.updateColorReducer.success) {
      props.getColor();
      toast.success("color updated succesfully...");
      props.resetUpdateColor();
    }
  }, [props.updateColorReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.id, value.status)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center align-items-center`}>
        <Link className="" to={`/color/${value.id}`}>
          <i className="fa fa-pencil-square-o" />
        </Link>
        <div
          className="btn text-danger"
          onClick={() => {
            setIsDelete(true);
            setDeleteId(value.id);
          }}
        >
          <i className="fa fa-trash" />
        </div>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Color - <span className="fw-semi-bold">Listing</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <div className="text-end mb-4">
            <Link to="/color/add" className="btn btn-primary">
              Add Color
            </Link>
          </div>
          {loading ? (
            <Loader />
          ) : (
            <BootstrapTable
              bordered={false}
              data={state}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`coustom-table stone-table table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo" width="50px">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="name" width="100%" dataSort>
                <span className="fs-sm">Color</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">status</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          )}
        </div>
      </div>
      <Modal
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        isOpen={isDelete}
        toggle={() => {
          setIsDelete(false);
          setDeleteId("");
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setDeleteId("");
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this color?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setDeleteId("");
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleDeleteColor(deleteId);
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};
const mapStateToProp = (state) => ({
  getColorReducer: state.masterReducer.Color.getColor,
  addColorReducer: state.masterReducer.Color.addColor,
  updateColorReducer: state.masterReducer.Color.updateColor,
  deleteColorReducer: state.masterReducer.Color.deleteColor,
  ColorStatusReducer: state.masterReducer.Color.ColorStatus,
});

const mapDispatchToProps = (dispatch) => ({
  getColor: () => dispatch(getColor()),
  deleteColor: (id) => dispatch(deleteColor(id)),
  changeColorStatus: (details) => dispatch(changeColorStatus(details)),
  resetDeleteColor: (id) => dispatch({ type: RESET_DELETE_COLOR }),
  resetAddColor: () => dispatch({ type: RESET_ADD_COLOR }),
  resetUpdateColor: () => dispatch({ type: RESET_UPDATE_COLOR }),
  resetColorStatus: (id) => dispatch({ type: RESET_COLOR_STATUS }),
});

export default connect(mapStateToProp, mapDispatchToProps)(ColorList);
