@import "../../styles/app";

.colorsList {
  display: flex;
  flex-wrap: wrap;

  .colorBox {
    min-width: 25px;
    height: 25px;
    border: 1px solid $border-color;
    border-radius: 0;
    cursor: pointer;
    margin: 3px;
    transition: $transition-base;

    :global {
      .rc-color-picker-trigger {
        width: 100%;
        height: 100%;
      }
    }

    &:hover {
      border-color: darken($border-color, 30%);
    }

    &.active {
      border-color: darken($primary, 10%);
    }
  }
}
