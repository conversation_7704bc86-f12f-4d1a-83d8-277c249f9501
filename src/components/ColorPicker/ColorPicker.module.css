/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.colorsList {
  display: flex;
  flex-wrap: wrap;
}
.colorsList .colorBox {
  min-width: 25px;
  height: 25px;
  border: 1px solid #d6dee5;
  border-radius: 0;
  cursor: pointer;
  margin: 3px;
  transition: all 0.2s ease-in-out;
}
.colorsList .colorBox :global .rc-color-picker-trigger {
  width: 100%;
  height: 100%;
}
.colorsList .colorBox:hover {
  border-color: #7893aa;
}
.colorsList .colorBox.active {
  border-color: #3e95f7;
}/*# sourceMappingURL=ColorPicker.module.css.map */