import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import {
  downloadUploadHistory,
  getUploadHistoryList,
} from "../../Services/actions/uploadHistoryAction";
import Loader from "../Loader/Loader";
import {
  BootstrapTable,
  SearchField,
  TableHeaderColumn,
} from "react-bootstrap-table";
import moment from "moment";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
} from "reactstrap";

function UploadHistoryTable(props) {
  const [loading, setLoading] = useState(true);
  const [details, setDetails] = useState([]);

  useEffect(() => {
    if (!props.getUploadHistoryListReducer.success) {
      props.getUploadHistoryList();
      setLoading(true);
    }
  }, []);

  useEffect(() => {
    if (props.getUploadHistoryListReducer.success) {
      const data = props.getUploadHistoryListReducer.data;

      const list = data.map((e, i) => {
        return {
          ...e,
          srNo: i + 1,
          upload_type: e.upload_type === 0 ? "Manual" : "File",
          status: e.status === 0 ? "Error" : "Complete",
          date: moment(data.updated_at).format("yyyy-MM-DD HH:MM:SS"),
          time: moment(data.updated_at).format("yyyy-MM-DD HH:MM:SS"),
        };
      });
      setDetails(list);
      setLoading(false);
    }
  }, [props.getUploadHistoryListReducer]);

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const createCustomSearchField = (props) => {
    return <SearchField className="mb-sm-5 me-1" placeholder="Search" />;
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  const actionFormatter = (cell, value) => {
    return (
      <Button
        className="btn-action"
        onClick={() => {
          props.downloadUploadHistory(cell);
        }}
      >
        <i class="fa-solid fa-download"></i>
      </Button>
    );
  };

  return (
    <>
      {loading ? (
        <Loader />
      ) : (
        <>
          <BootstrapTable
            bordered={false}
            data={details}
            version="4"
            pagination
            options={options}
            search
            tableContainerClass={`coustom-table stone-table table-striped table-hover`}
          >
            <TableHeaderColumn dataField="srNo" width="55px">
              <span className="fs-sm">Sr No.</span>
            </TableHeaderColumn>
            <TableHeaderColumn
              dataField="file_name"
              dataSort
              width="250"
            >
              <span className="fs-sm">File Name</span>
            </TableHeaderColumn>

            <TableHeaderColumn dataField="upload_type">
              <span className="fs-sm">Upload Type</span>
            </TableHeaderColumn>

            <TableHeaderColumn dataField="date" width="155px">
              <span className="fs-sm">Uploaded Date</span>
            </TableHeaderColumn>

            <TableHeaderColumn dataField="time" width="155px">
              <span className="fs-sm">Uploaded Time</span>
            </TableHeaderColumn>

            <TableHeaderColumn dataField="total" width="90px" dataAlign="center" >
              <span className="fs-sm">Total Diam.</span>
            </TableHeaderColumn>

            <TableHeaderColumn dataField="valid" dataAlign="center" >
              <span className="fs-sm">Valid Diam.</span>
            </TableHeaderColumn>

            <TableHeaderColumn dataField="invalid" width="100px" dataAlign="center">
              <span className="fs-sm">Invalid Diam.</span>
            </TableHeaderColumn>
            <TableHeaderColumn dataField="status" width="120px">
              <span className="fs-sm">Status</span>
            </TableHeaderColumn>
            <TableHeaderColumn
              isKey
              dataField="id"
              dataFormat={actionFormatter.bind(this)}
              dataAlign="center"
            >
              <span className="fs-sm text-center d-block">Download</span>
            </TableHeaderColumn>
          </BootstrapTable>
        </>
      )}
    </>
  );
}

const mapStateToProp = (state) => ({
  getUploadHistoryListReducer: state.uploadHistoryReducer.getUploadHistoryList,
});

const mapDispatchToProps = (dispatch) => ({
  getUploadHistoryList: () => dispatch(getUploadHistoryList()),
  downloadUploadHistory: (id) => dispatch(downloadUploadHistory(id)),
});

export default connect(mapStateToProp, mapDispatchToProps)(UploadHistoryTable);
