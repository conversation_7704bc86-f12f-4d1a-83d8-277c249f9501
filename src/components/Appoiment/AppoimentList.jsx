import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";

import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  ModalHeader,
} from "reactstrap";
import {
  changeAppoimentStatus,
  deleteAppoiment,
  getAppoiment,
} from "../../Services/actions/appoimentAction";
import {
  RESET_APPOIMENT_STATUS,
  RESET_DELETE_APPOIMENT,
} from "../../Services/Constant";
import Loader from "../Loader/Loader";
import styles from "./AppoimentList.module.css";
import { toast } from "react-toastify";

function AppoimentList(props) {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isDelete, setIsDelete] = useState(false);
  const [deleteId, setDeleteId] = useState("");

  useEffect(() => {
    if (!props.getAppoimentReducer.success) {
      props.getAppoiment();
    }
  }, []);

  useEffect(() => {
    setLoading(props.getAppoimentReducer.loading);
    if (props.getAppoimentReducer.success) {
      const data = props.getAppoimentReducer.data;
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          fullName: e?.user?.fullname,
          email: e?.user?.email,
          company: e?.user?.company,
          date: e?.date,
          time: e?.time,
          remark: e?.remark,
          status: e?.status,
          id: e?.id,
        };
      });
      setState([...list]);
    }
  }, [props.getAppoimentReducer]);

  const handleDeleteAppoiment = (id) => {
    props.deleteAppoiment(id);
  };

  const actionFormatter = (cell, value) => {
    return (
        <button
        className="btn-action"
          onClick={() => {
            setIsDelete(true);
            setDeleteId(value.id);
          }}
        >
          <i className="fa fa-trash" />
        </button>
    );
  };

  useEffect(() => {
    if (props.deleteAppoimentReducer.success) {
      props.getAppoiment();
      setIsDelete(false);
      setDeleteId("");
      toast.success("appointment deleted successfully...");
      props.resetDeleteAppoiment();
    }
  }, [props.deleteAppoimentReducer]);

  const handleStatus = async (status, id) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeAppoimentStatus({
      id: id,
      details: details,
    });

    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.statusAppoimentReducer.success) {
      // props.getAppoiment();
      props.resetAppoimentStatus();
    }
  }, [props.statusAppoimentReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.status, value.id)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Appointment - <span className="fw-semi-bold">Listing</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          {/* <div className="text-end mb-4">
           <Link to="/clarity/add" className="btn btn-primary">
             Add Appointment
           </Link>
         </div> */}
          {loading ? (
            <Loader />
          ) : (
            <BootstrapTable
              id={styles.view_request}
              bordered={false}
              // tableHeaderClass={styles.headerClass}
              // tableBodyClass={styles.bodyClass}
              data={state}
              version="4"
              pagination
              // selectRow={{
              //   mode: "checkbox",
              //   onSelect: (value, item) =>
              //     handleSelectRow("select", value, item),
              //   onSelectAll: (value, item) =>
              //     handleSelectRow("all", item, value),
              // }}
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`custome-table stone-table table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo" width="55px">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="fullName" dataSort>
                <span className="fs-sm">Full Name</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="email" width="255px">
                <span className="fs-sm">Email</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="company" dataSort>
                <span className="fs-sm">Company</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="date" dataSort>
                <span className="fs-sm">Date</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="time" dataSort>
                <span className="fs-sm">Time</span>
              </TableHeaderColumn>
              <TableHeaderColumn dataField="remark" width="200px">
                <span className="fs-sm">Remark</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">status</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
                dataAlign="center"
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          )}
        </div>
      </div>
      <Modal
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        // centered
        isOpen={isDelete}
        toggle={() => {
          setIsDelete(false);
          setDeleteId("");
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setDeleteId("");
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this appointment?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setDeleteId("");
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleDeleteAppoiment(deleteId);
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
}

const mapStateToProp = (state) => ({
  getAppoimentReducer: state.appoimentReducer.getAppoiment,
  deleteAppoimentReducer: state.appoimentReducer.deleteAppoiment,
  statusAppoimentReducer: state.appoimentReducer.statusAppoiment,
});

const mapDispatchToProps = (dispatch) => ({
  getAppoiment: () => dispatch(getAppoiment()),
  deleteAppoiment: (id) => dispatch(deleteAppoiment(id)),
  changeAppoimentStatus: (details) => dispatch(changeAppoimentStatus(details)),
  resetDeleteAppoiment: (id) => dispatch({ type: RESET_DELETE_APPOIMENT }),
  resetAppoimentStatus: (id) => dispatch({ type: RESET_APPOIMENT_STATUS }),
});

export default connect(mapStateToProp, mapDispatchToProps)(AppoimentList);
