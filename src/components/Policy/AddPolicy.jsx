import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory } from "react-router";
import { addPolicy } from "../../Services/actions/PolicyAction";
import { CKEditor } from "ckeditor4-react";

const AddPolicy = (props) => {
  const history = useHistory();
  const [state, setState] = useState({
    name: "",
    type: "",
    description: "",
    version: "",
  });
  const [error, setError] = useState({
    name: false,
    type: false,
    description: false,
    version: false,
  });

  const handleInput = (e) => {
    const { name, value } = e.target;
    if (name === "version") {
      if (RegExp(/^([0-9]{0,2}([.][0-9]{0,2})?|100)$/i).test(value)) {
        setState((prevState) => ({
          ...prevState,
          [name]: value,
        }));
      } else return;
    } else {
      setState((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    }

    switch (name) {
      case "name":
        if (value === "") {
          error[name] = "* Enter Policy Name";
        } else {
          error[name] = false;
        }
        break;
      case "type":
        if (value === "") {
          error[name] = "* Enter Policy Type";
        } else {
          error[name] = false;
        }
        break;
      case "version":
        if (value === "") {
          error[name] = "* Enter Policy Version";
        } else {
          error[name] = false;
        }
        break;

      default:
        break;
    }
  };

  const submitPolicy = () => {
    if (state.name === "") {
      setError((prev) => ({
        ...prev,
        name: "* Enter Policy Name",
      }));
    }
    if (state.type === "") {
      setError((prev) => ({
        ...prev,
        type: "* Enter Policy Type",
      }));
    }
    if (state.description === "") {
      setError((prev) => ({
        ...prev,
        description: "* Enter Policy Description",
      }));
    }
    if (state.version === "") {
      setError((prev) => ({
        ...prev,
        version: "* Enter Policy Version",
      }));
    }

    if (
      state.name !== "" &&
      state.type !== "" &&
      state.description !== "" &&
      state.version !== ""
    ) {
      const details = {
        name: state.name,
        type: state.type,
        description: state.description,
        version: state.version,
      };

      props.addPolicy(details);
    }
  };

  useEffect(() => {
    if (props.addPolicyReducer.success) {
      history.push("/policy");
    }
  }, [props.addPolicyReducer]);

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Add - <span className="fw-semi-bold">Policy</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">Add Policy</h4>

          <div className="col-lg-6 col-md-6">
            <div className="row gx-3">
              <div className="mb-4 col-md-6">
                <label className="form-label">Policy Name</label>
                <input
                  placeholder="Enter policy name"
                  type="text"
                  name="name"
                  className="form-control"
                  value={state.name}
                  onChange={handleInput}
                />
                {error.name && (
                  <div>
                    <span className="text-danger h6">{error.name}</span>
                  </div>
                )}
              </div>
              <div className="mb-4 col-md-6">
                <label className="form-label">Policy Type</label>
                <select
                  className="form-select"
                  name="type"
                  value={state.type}
                  onChange={handleInput}
                >
                  <option value="">Select Policy type</option>
                  <option value={"1"}>Terms</option>
                  <option value={"2"}>Policy</option>
                </select>
                {error.type && (
                  <div>
                    <span className="text-danger h6">{error.type}</span>
                  </div>
                )}
              </div>
            </div>
            <div className="mb-4">
              <label className="form-label">Policy Version</label>
              <input
                placeholder="Enter policy version"
                type="text"
                name="version"
                className="form-control"
                value={state.version}
                onChange={handleInput}
              />
              {error.version && (
                <div>
                  <span className="text-danger h6">{error.version}</span>
                </div>
              )}
            </div>
            <div className="mb-4">
              <label className="form-label">Policy Description</label>
              <CKEditor
                id="editor"
                onChange={(e) => {
                  setState((prev) => ({
                    ...prev,
                    description: e.editor.getData(),
                  }));

                  if (e.editor.getData() === "") {
                    setError((prev) => ({
                      ...prev,
                      description: "* Enter Policy Description",
                    }));
                  } else {
                    setError((prev) => ({
                      ...prev,
                      description: false,
                    }));
                  }
                }}
              />
              {error.description && (
                <div>
                  <span className="text-danger h6">{error.description}</span>
                </div>
              )}
            </div>
            <button className="btn btn-primary" onClick={submitPolicy}>
              Submit
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

const mapStateToProp = (state) => ({
  addPolicyReducer: state.PolicyReducer.addPolicy,
});

const mapDispatchToProps = (dispatch) => ({
  addPolicy: (details) => dispatch(addPolicy(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddPolicy);
