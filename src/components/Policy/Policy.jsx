import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Dropdown<PERSON>oggle,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
} from "reactstrap";
import {
  changePolicyStatus,
  deletePolicy,
  getPolicy,
} from "../../Services/actions/PolicyAction";
import {
  RESET_ADD_POLICY,
  RESET_DELETE_POLICY,
  RESET_POLICY_STATUS,
} from "../../Services/Constant";
import Loader from "../Loader/Loader";
import { toast } from "react-toastify";

const Policy = (props) => {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true);


  const [isDelete, setIsDelete] = useState(false);
  const [deleteId, setDeleteId] = useState("");

  useEffect(() => {
    props.getPolicy();
  }, []);

  useEffect(() => {
    setLoading(props.getPolicyReducer.loading);
    if (props.getPolicyReducer.success) {
      const data = props.getPolicyReducer.data;
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          name: e.name,
          status: e.status,
          id: e.id,
        };
      });
      setState([...list]);
    }
  }, [props.getPolicyReducer]);

  const handleStatus = async (id, status) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changePolicyStatus({
      details: details,
      id: id,
    });
    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;

    setState([...list]);
  };

  useEffect(() => {
    if (props.statusPolicyReducer.success) {
      // props.getPolicy();
      props.resetChangePolicyStatus();
    }
  }, [props.statusPolicyReducer]);

  const handlePolicy = (id) => {
    props.deletePolicy(id);
  };

  useEffect(() => {
    if (props.deletePolicyReducer.success) {
      props.getPolicy();
      toast.success("Policy deleted successfully...");
      props.resetDeletePolicy();
    } else if (props.deletePolicyReducer.error) {
      toast.error(props.deletePolicyReducer.errorMsg);
      props.resetDeletePolicy();
    }
  }, [props.deletePolicyReducer]);

  useEffect(() => {
    if (props.addPolicyReducer.success) {
      props.getPolicy();
      toast.success("Policy added successfully...");
      props.resetAddPolicy();
    }
  }, [props.addPolicyReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={parseInt(value.status) === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.id, value.status)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center align-items-center`}>
        <div
          className="btn text-danger"
          onClick={() => {
            setIsDelete(true);
            setDeleteId(value.id);
          }}
        >
          <i className="fa fa-trash" />
        </div>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Policy - <span className="fw-semi-bold">Management</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <div className="text-end mb-4">
            <Link to="/policy/add" className="btn btn-primary py-2">
              Add Policy
            </Link>
          </div>
          {loading ? (
            <Loader />
          ) : (
            <BootstrapTable
              bordered={false}
              data={state}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`coustom-table stone-table table-striped table-hover`}
            > 
              <TableHeaderColumn dataField="srNo" width="50px">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="name" dataSort width="100%">
                <span className="fs-sm">Policy</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">status</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>
          )}
        </div>
      </div>

      <Modal
        size="sm"
        isOpen={isDelete}
        aria-labelledby="contained-modal-title-vcenter"
        toggle={() => {
          setIsDelete(false);
          setDeleteId("");
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setDeleteId("");
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this policy?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setDeleteId("");
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handlePolicy(deleteId);
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

const mapStateToProp = (state) => ({
  getPolicyReducer: state.PolicyReducer.getPolicy,
  deletePolicyReducer: state.PolicyReducer.deletePolicy,
  statusPolicyReducer: state.PolicyReducer.statusPolicy,
  addPolicyReducer: state.PolicyReducer.addPolicy,
});

const mapDispatchToProps = (dispatch) => ({
  getPolicy: () => dispatch(getPolicy()),
  deletePolicy: (id) => dispatch(deletePolicy(id)),
  changePolicyStatus: (details) => dispatch(changePolicyStatus(details)),
  resetDeletePolicy: (id) => dispatch({ type: RESET_DELETE_POLICY }),
  resetChangePolicyStatus: (id) => dispatch({ type: RESET_POLICY_STATUS }),
  resetAddPolicy: (id) => dispatch({ type: RESET_ADD_POLICY }),
});

export default connect(mapStateToProp, mapDispatchToProps)(Policy);
