import React, { useEffect, useState } from "react";
import {
  BootstrapTable,
  SearchField,
  TableHeaderColumn,
} from "react-bootstrap-table";
import { connect } from "react-redux";
import { useHistory } from "react-router";
import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
} from "reactstrap";
import { getConfirmDiamondUser } from "../../Services/actions/confirmDiamondList";

const ConfirmTable = (props) => {
  const history = useHistory();
  const [state, setState] = useState([]);
  const [selectedId, setSelectedId] = useState([]);

  // useEffect(() => {
  //   if (props.userId) {
  //     props.getConfirmDiamondUser(props.userId);
  //   }
  // }, [props.userId]);

  useEffect(() => {
    props.selectedItem(selectedId.length);
    props.setSelectedIds(
      selectedId.map((e) => {
        return e.id;
      })
    );
    const Amount = selectedId
      .map((e) => {
        return e.amount;
      })
      .reduce((a, b) => a + b, 0);

    const rapRate = selectedId
      .map((e) => {
        return e.rapRate;
      })
      .reduce((a, b) => a + b, 0);

    const disc =
      selectedId
        .map((e) => {
          return e.disc;
        })
        .reduce((a, b) => a + b, 0) / selectedId.length;
    props.setSelectedAmount(Amount);
    props.setSelectedRapAmount(rapRate);
    props.setSelectedDisc(isNaN(disc) ? 0 : disc);
  }, [selectedId]);

  useEffect(() => {
    if (props.getConfirmDiamondUserListReducer.success) {
      const data = props.getConfirmDiamondUserListReducer.data;
      const details = data.map((e, i) => {
        return {
          srNo: i + 1,
          stone_id: !!e.product ? e.product.stone_id : "",
          cert: !!e.product ? e.product.cert : "",
          cert_no: !!e.product ? e.product.cert_no : "",
          cert_url: !!e.product ? e.product.cert_url : "",
          diamond_type_name: !!e.product ? e.product.diamond_type_name : "",
          image: !!e.product ? e.product.image : "",
          video: !!e.product ? e.product.video : "",
          amount: !!e.product ? e.product.amount : "",
          rapRate: !!e.product ? e.product.rapo_rate : "",
          disc: !!e.product ? e.product.discount : "",
          shape: !!e.product ? e.product.shape_name : "",
          id: e.id,
          product_id: e.product_id,
        };
      });
      setState([...details]);
    }
  }, [props.getConfirmDiamondUserListReducer]);

  const handleSelectRow = (type, item, value) => {
    if (type === "all") {
      //  select all
      if (value === true) {
        const listOfSelected = state.map((e) => {
          return e;
        });
        setSelectedId([...listOfSelected]);
      } else {
        setSelectedId([]);
      }
    } else {
      //  select
      if (value === true) {
        setSelectedId((pre) => [...pre, item]);
      } else {
        const idList = [...selectedId];
        const index = idList.findIndex((e) => e === item.id);
        idList.splice(index, 1);
        setSelectedId([...idList]);
      }
    }
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const createCustomSearchField = (props) => {
    return <SearchField className="mb-sm-5 me-1" placeholder="Search" />;
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center`}>
        <div
          onClick={() => window.open(value.image, "_blank")}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-image"></i>
        </div>
        <div
          onClick={() => window.open(value.video, "_blank")}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-file-video"></i>
        </div>
        <div
          onClick={() => window.open(value.cert_url, "_blank")}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-file"></i>
        </div>
        <div
          // preview
          onClick={() => history.push(`/stone/${value.product_id}`)}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-eye"></i>
        </div>
      </div>
    );
  };

  return (
    <>
      <BootstrapTable
        bordered={false}
        data={state}
        version="4"
        pagination
        selectRow={{
          mode: "checkbox",
          onSelect: (value, item) => handleSelectRow("select", value, item),
          onSelectAll: (value, item) => handleSelectRow("all", item, value),
        }}
        options={options}
        search
        scrollTop={"Bottom"}
        tableContainerClass={`table-responsive table-striped table-hover`}
      >
        <TableHeaderColumn dataField="srNo">
          <span className="fs-sm">Sr No.</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="stone_id" dataSort>
          <span className="fs-sm">Stone ID</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="cert" dataSort>
          <span className="fs-sm">Certificate</span>
        </TableHeaderColumn>
        <TableHeaderColumn dataField="cert_no" dataSort>
          <span className="fs-sm">Certificate NO</span>
        </TableHeaderColumn>
        <TableHeaderColumn dataField="diamond_type_name" dataSort>
          <span className="fs-sm">Diamond Type</span>
        </TableHeaderColumn>
        <TableHeaderColumn dataField="shape" dataSort>
          <span className="fs-sm">Shape</span>
        </TableHeaderColumn>

        <TableHeaderColumn
          isKey
          dataField="id"
          dataFormat={actionFormatter.bind(this)}
        >
          <span className="fs-sm text-center d-block">Actions</span>
        </TableHeaderColumn>
      </BootstrapTable>
    </>
  );
};

const mapStateToProp = (state) => ({
  getConfirmDiamondUserListReducer:
    state.confirmDiamondReducer.getConfirmDiamondUserList,
});

const mapDispatchToProps = (dispatch) => ({
  getConfirmDiamondUser: (id) => dispatch(getConfirmDiamondUser(id)),
});

export default connect(mapStateToProp, mapDispatchToProps)(ConfirmTable);
