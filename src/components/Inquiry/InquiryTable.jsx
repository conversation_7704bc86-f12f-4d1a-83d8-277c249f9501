import React, { useEffect, useState } from "react";
import {
  BootstrapTable,
  SearchField,
  TableHeaderColumn,
} from "react-bootstrap-table";
import { connect } from "react-redux";
import { useHistory } from "react-router";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  Modal,
  ModalBody,
  Modal<PERSON>ooter,
  ModalHeader,
} from "reactstrap";
import Loader from "../Loader/Loader";
import { deleteInquiry, getInquiryListUser } from "../../Services/actions/inquiryList";
import { deleteStoneFromCart } from "../../Services/actions/cartList";
import { RESET_DELETE_INQUIRY_STONE } from "../../Services/Constant";
import { toast } from "react-toastify";

function InquiryTable(props) {
  const history = useHistory();

  const [state, setState] = useState([]);
  const [selectedId, setSelectedId] = useState([]);

  const [isDelete, setIsDelete] = useState(false);
  const [deletedId, setDeletedId] = useState([]);
  // useEffect(() => {
  //   if (props.userId) {
  //     props.getCartUser(props.userId);
  //   }
  // }, [props.userId]);

  useEffect(() => {
    props.selectedItem(selectedId.length);
    const Amount = selectedId
      .map((e) => {
        return e.amount === "" ? 0 : e.amount;
      })
      .reduce((a, b) => a + b, 0);

    const rapRate = selectedId
      .map((e) => {
        return e.rapRate === "" ? 0 : e.rapRate;
      })
      .reduce((a, b) => a + b, 0);

    const disc =
      selectedId
        .map((e) => {
          return e.disc === "" ? 0 : e.disc;
        })
        .reduce((a, b) => a + b, 0) / selectedId.length;
    props.setSelectedAmount(Amount);
    props.setSelectedRapAmount(rapRate);
    props.setSelectedDisc(isNaN(disc) ? 0 : disc);
  }, [selectedId]);

  useEffect(() => {
    if (props.getInquirydUserListReducer.success) {
      const data = props.getInquirydUserListReducer.data;
      const details = data.map((e, i) => {
        return {
          srNo: i + 1,
          stone_id: !!e.product ? e.product.stone_id : "",
          cert: !!e.product ? e.product.cert : "",
          cert_no: !!e.product ? e.product.cert_no : "",
          cert_url: !!e.product ? e.product.cert_url : "",
          diamond_type_name: !!e.product ? e.product.diamond_type_name : "",
          image: !!e.product ? e.product.image : "",
          video: !!e.product ? e.product.video : "",
          amount: !!e.product ? e.product.amount : "",
          rapRate: !!e.product ? e.product.rapo_rate : "",
          disc: !!e.product ? e.product.discount : "",
          shape: !!e.product ? e.product.shape_name : "",
          product_id: e.product_id,
          id: e.id,
        };
      });
      setState([...details]);
    }
  }, [props.getInquirydUserListReducer]);

  const handleSelectRow = (type, item, value) => {
    if (type === "all") {
      //  select all
      if (value === true) {
        const listOfSelected = state.map((e) => {
          return e;
        });
        const idList = state.map((e) => {
          return e.id;
        });
        setSelectedId([...listOfSelected]);
        setDeletedId([...idList]);
      } else {
        setSelectedId([]);
        setDeletedId([]);
      }
    } else {
      //  select
      if (value === true) {
        //   const selectedId = item.id;
        setSelectedId((pre) => [...pre, item]);
        setDeletedId((pre) => [...pre, item.id]);
      } else {
        const idList = [...selectedId];
        const deleteList = [...deletedId];
        const index = idList.findIndex((e) => e.id === item.id);
        const _index = deleteList.findIndex((e) => e === item.id);
        idList.splice(index, 1);
        deleteList.splice(_index, 1);
        setSelectedId([...idList]);
        setDeletedId([...deleteList]);
      }
    }
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const createCustomSearchField = (props) => {
    return <SearchField className="mb-sm-5 me-1" placeholder="Search" />;
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center`}>
        <div
          onClick={() => window.open(value.image, "_blank")}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-image"></i>
        </div>
        <div
          onClick={() => window.open(value.video, "_blank")}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-file-video"></i>
        </div>
        <div
          onClick={() => window.open(value.cert_url, "_blank")}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-file"></i>
        </div>
        <div
          // preview
          onClick={() => history.push(`/stone/${value.product_id}`)}
          className="mx-2"
          style={{ cursor: "pointer" }}
        >
          <i className="fa-regular fa-eye"></i>
        </div>
        <div
          // preview
          onClick={() => {
            setIsDelete(true);
            setDeletedId([value.id]);
          }}
          className="mx-2 text-danger"
          style={{ cursor: "pointer" }}
        >
          <i className="fa fa-trash"></i>
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (props.deleteInquiryReducer.success) {
      setIsDelete(false);
      setSelectedId([]);
      setDeletedId([]);
      toast.success("Delete inquiry successfully...");
      props.resetDelete();
      props.getInquiryListUser(props.userId);
    }
  }, [props.deleteInquiryReducer]);

  return (
    <>
      {selectedId.length !== 0 && (
        <div className="text-end mb-3">
          {/* <Link to="/stock/add" className="btn btn-primary">Add New</Link> */}
          <div
            className="btn btn-primary py-2"
            onClick={() => setIsDelete(true)}
          >
            Delete
          </div>
        </div>
      )}
      <BootstrapTable
        bordered={false}
        data={state}
        version="4"
        pagination
        selectRow={{
          mode: "checkbox",
          onSelect: (value, item) => handleSelectRow("select", value, item),
          onSelectAll: (value, item) => handleSelectRow("all", item, value),
        }}
        options={options}
        search
        scrollTop={"Bottom"}
        tableContainerClass={`table-responsive table-striped table-hover`}
      >
        <TableHeaderColumn dataField="srNo">
          <span className="fs-sm">Sr No.</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="stone_id" dataSort>
          <span className="fs-sm">Stone ID</span>
        </TableHeaderColumn>

        <TableHeaderColumn dataField="cert" dataSort>
          <span className="fs-sm">Certificate</span>
        </TableHeaderColumn>
        <TableHeaderColumn dataField="cert_no" dataSort>
          <span className="fs-sm">Certificate NO</span>
        </TableHeaderColumn>
        <TableHeaderColumn dataField="diamond_type_name" dataSort>
          <span className="fs-sm">Diamond Type</span>
        </TableHeaderColumn>
        <TableHeaderColumn dataField="shape" dataSort>
          <span className="fs-sm">Shape</span>
        </TableHeaderColumn>

        <TableHeaderColumn
          isKey
          dataField="id"
          dataFormat={actionFormatter.bind(this)}
        >
          <span className="fs-sm text-center d-block">Actions</span>
        </TableHeaderColumn>
      </BootstrapTable>

      <Modal
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        isOpen={isDelete}
        toggle={() => {
          setIsDelete(false);
          setSelectedId([]);
          setDeletedId([]);
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setSelectedId([]);
            setDeletedId([]);
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this Inquiry?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setSelectedId([]);
              setDeletedId([]);
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              props.deleteInquiry({
                inquiry_id: JSON.stringify(deletedId),
              });
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
}

const mapStateToProp = (state) => ({
  getInquirydUserListReducer: state.inquiryReducer.getInquirydUserList,
  deleteInquiryReducer: state.inquiryReducer.deleteInquiry,
});

const mapDispatchToProps = (dispatch) => ({
  getInquiryListUser: (id) => dispatch(getInquiryListUser(id)),
  deleteInquiry: (details) => dispatch(deleteInquiry(details)),
  resetDelete: (details) => dispatch({ type: RESET_DELETE_INQUIRY_STONE }),
});

export default connect(mapStateToProp, mapDispatchToProps)(InquiryTable);
