import React, { useEffect, useState } from "react";
import { Col, Nav, Row, Tab } from "react-bootstrap";
import { connect } from "react-redux";
import Search from "../../images/sidebar/basil/Search";
import {
  getInquiryList,
  getInquiryListUser,
} from "../../Services/actions/inquiryList";
import Loader from "../Loader/Loader";
import InquiryTable from "./InquiryTable";

function InquiryListing(props) {
  const [userList, setUserList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");

  const [selectedItem, setSelectedItem] = useState(0);
  const [selectedAmount, setSelectedAmount] = useState(0);
  const [selectedRapRate, setSelectedRapAmount] = useState(0);
  const [selectedDisc, setSelectedDisc] = useState(0);

  useEffect(() => {
    props.getInquiryList();
  }, []);

  useEffect(() => {
    setLoading(props.getInquiryListReducer.loading);
    if (props.getInquiryListReducer.success) {
      const data = props.getInquiryListReducer.data;
      setUserList([...data]);
      data.length !== 0 && props.getInquiryListUser(data[0].user_id);
    }
  }, [props.getInquiryListReducer]);

  const handleSearch = (e) => {
    const { value } = e.target;
    setSearchText(value);
    const element = document.getElementsByClassName("searchStr");
    for (var i = element.length - 1; i >= 0; i--) {
      if (element[i].textContent.toLowerCase().indexOf(value) === -1) {
        element[i].classList.add("d-none");
      } else {
        element[i].classList.remove("d-none");
      }
    }
  };

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          View - <span className="fw-semi-bold">Inquiry</span>
        </h2>
      </div>

      <div className=" mb-4">
        <div className="row">
          <div className="col-md-3">
            <div className="card main-card">
              <div className="card-body">
                <h6 className="text-muted">Total selected Diamonds</h6>
                <h2>{selectedItem.toFixed(2)}</h2>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card main-card">
              <div className="card-body">
                <h6 className="text-muted">selected Diamond Amount</h6>
                <h2>{selectedAmount.toFixed(2)}</h2>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card main-card">
              <div className="card-body">
                <h6 className="text-muted">selected Diamonds Raprate</h6>
                <h2>{selectedRapRate.toFixed(2)}</h2>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card main-card">
              <div className="card-body">
                <h6 className="text-muted">Total Discount</h6>
                <h2>{selectedDisc.toFixed(2)}</h2>
              </div>
            </div>
          </div>
        </div>
      </div>

      {loading ? (
        <Loader />
      ) : (
        <Tab.Container
          id="left-tabs-example"
          defaultActiveKey={userList.length !== 0 && userList[0].user_id}
          onSelect={(e) => {
            props.getInquiryListUser(e);
            setSelectedDisc(0);
            setSelectedAmount(0);
            setSelectedItem(0);
            setSelectedRapAmount(0);
          }}
        >
          <Row>
            <Col xl={2} md={3} className="pe-0">
              <div className="search">
                <input
                  className="form-control"
                  type="text"
                  placeholder="Search user..."
                  onChange={handleSearch}
                  value={searchText}
                />
                <button className="btn btn-search" type="text">
                  <Search />
                </button>
              </div>
              <div className="mb-5 card main-card border-0 list-card">
                <div className="card-body p-0">
                  <Nav
                    variant="pills"
                    className="flex-column"
                    // activeKey={userList.length !== 0 && userList[0].user_id}
                    // defaultActiveKey={userList?.[0].user_id}
                  >
                    {loading ? (
                      <Loader />
                    ) : userList.length !== 0 ? (
                      userList.map((e, i) => {
                        return (
                          <Nav.Item key={i} className="searchStr">
                            <Nav.Link eventKey={e.user_id}>
                              {e.user.username}
                            </Nav.Link>
                          </Nav.Item>
                        );
                      })
                    ) : (
                      <div className="text-center py-5 text-secondary">
                        No UserList Available
                      </div>
                    )}
                  </Nav>
                </div>
              </div>
            </Col>
            <Col xl={10} md={9}>
              <div className="mb-5 card main-card border-0">
                <div className="card-body p-0">
                  <Tab.Content>
                    {userList.length !== 0 ? (
                      userList.map((e, i) => {
                        return (
                          <Tab.Pane eventKey={e.user_id} key={i}>
                            <InquiryTable
                              userId={e.user_id}
                              selectedItem={setSelectedItem}
                              setSelectedDisc={setSelectedDisc}
                              setSelectedRapAmount={setSelectedRapAmount}
                              setSelectedAmount={setSelectedAmount}
                            />
                          </Tab.Pane>
                        );
                      })
                    ) : (
                      <div className="text-center py-5  text-secondary">
                        No User Details Available
                      </div>
                    )}
                  </Tab.Content>
                </div>
              </div>
            </Col>
          </Row>
        </Tab.Container>
      )}
    </>
  );
}

const mapStateToProp = (state) => ({
  getInquiryListReducer: state.inquiryReducer.getInquiryList,
  getInquirydUserListReducer: state.inquiryReducer.getInquirydUserList,
});

const mapDispatchToProps = (dispatch) => ({
  getInquiryList: () => dispatch(getInquiryList()),
  getInquiryListUser: (id) => dispatch(getInquiryListUser(id)),
});
export default connect(mapStateToProp, mapDispatchToProps)(InquiryListing);
