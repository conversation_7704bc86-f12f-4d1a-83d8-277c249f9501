import React, { useEffect } from 'react';
import { connect, useDispatch } from 'react-redux';
import { Switch, Route, Redirect, useLocation, withRouter } from 'react-router';
import { ToastContainer } from 'react-toastify';
import { AuthRoute } from './RouteComponents';

/* eslint-disable */
import ErrorPage from '../pages/error';
/* eslint-enable */

import '../styles/theme.scss';
import '../styles/style.scss';
import '../styles/jewll-fonts.css';
import Login from '../pages/auth/login';
import Verify from '../pages/auth/verify';
import Reset from '../pages/auth/reset';
import Forgot from '../pages/auth/forgot';
import { push } from 'connected-react-router';


import Hammer from 'rc-hammerjs';

// import Profile from '../../pages/profile';
// import ExtraCalendar from '../../pages/extra/calendar';
// import ExtraInvoice from '../../pages/extra/invoice';
// import ExtraSearch from '../../pages/extra/search';
// import Dashboard from '../../pages/dashboard';
// import { SidebarTypes } from '../../Services/reducers/layout';

import { CSSTransition } from 'react-transition-group';
import s from '../components/Layout/Layout.module.scss';
import AppoimentList from './Appoiment/AppoimentList';
import BreadcrumbHistory from './BreadcrumbHistory/BreadcrumbHistory';
import CartListing from './Cart/CartListing';
import AddUserList from './Users/<USER>/AddUserList';
import UserListDetails from './Users/<USER>/UserListDetails';
import StockListing from './Stock/StockListing';
// import AddStock from './Stock/AddStock';
import StockDetails from './Stock/StockDetails';
import SalesListing from './Sales/SalesListing';
import StoneDetails from './StoneDetails/StoneDetails';
import ClarityLIst from './Master/Clarity/ClarityLIst';
import AddClarity from './Master/Clarity/AddClarity';
import ColorList from './Master/Color/ColorList';
import AddColor from './Master/Color/AddColor';
import SizeList from './Master/Size/SizeList';
import AddSize from './Master/Size/AddSize';
import ShapeList from './Master/Shape/ShapeList';
import AddShape from './Master/Shape/AddShape';
import FancyColorList from './Master/FancyColor/FancyColorList';
import AddFancyColor from './Master/FancyColor/AddFancyColor';
import FinishList from './Master/Finish/FinishList';
import AddFinish from './Master/Finish/AddFinish';
import FluorescenceList from './Master/Fluorescence/FluorescenceList';
import AddFluorescence from './Master/Fluorescence/AddFluorescence';
import Policy from './Policy/Policy';
import AddPolicy from './Policy/AddPolicy';
import AddMarketing from '../pages/Marketing/AddMarketing';
import RequestListing from './ViewRequest/RequestListing';
import AddSlot from './ViewRequest/AddSlot';
import HoldedList from './Hold/HoldedList';
import ConfirmedList from './ConfirmedDiamonds/ConfirmedList';
import InquiryListing from './Inquiry/InquiryListing';
import DemandList from './DemandList/DemandList';
import MarketingList from '../pages/Marketing/MarketingList';
import Header from './Header/Header';
import Sidebar from './Sidebar/Sidebar';
import UsersListPage from './Users/<USER>/UsersListPage';
import Profile from '../pages/profile/Profile';
import Dashboard from '../pages/dashboard/Dashboard';
import { DashboardThemes, SidebarTypes } from '../Services/reducers/layout';
import { closeSidebar, openSidebar } from '../Services/actions/navigation';
import StarMeleeListing from './StarMelee/StarMeleeListing';
import AddStarMelee from './StarMelee/AddStarMelee';
import InquiryStarMelee from './StarMelee/InquiryStarMelee';
import VendorList from './Vendors/VendorList';
import AddVendor from './Vendors/AddVendor';
import MerchantList from './Merchant/MerchantList';
import AddMerchant from './Merchant/AddMerchant';
import InvalidStoneList from './InvalidStone/InvalidStoneList';
import AddNewStock from './Stock/AddNewStock';
import UploadHistoryList from './UploadHistory/UploadHistoryList';
import AllShapes from './Master/Shape/AllShapes';
import VendorListDetails from './Vendors/VendorListDetails';
// import { closeSidebar, openSidebar } from '../Services/actions/navigation';


// const defaultProps = {
//   sidebarStatic: false,
//   sidebarOpened: true,
//   dashboardTheme: DashboardThemes.DARK
// };


const CloseButton = ({ closeToast }) => <i onClick={closeToast} className="la la-close notifications-close" />

function App({ sidebarStatic = false, sidebarOpened = true, dashboardTheme = DashboardThemes.DARK, ...props }) {
  const token = localStorage.getItem('token')
  const location = useLocation()
  // const history = useHistory()
  const dispatch = useDispatch()
  useEffect(() => {
    if (!token) {
      dispatch(push('/login'));
    }
  }, [location.pathname])

  const handleSwipe = (e) => {
    if ('ontouchstart' in window) {
      if (e.direction === 4) {
        props.dispatch(openSidebar());
        return;
      }

      if (e.direction === 2 && sidebarOpened) {
        props.dispatch(closeSidebar());
        return;
      }
    }
  }

  return (
    <div>

      <ToastContainer
        autoClose={5000}
        hideProgressBar
        closeButton={<CloseButton />}
      />
      {
        token === null ?
          <Switch>
            <Route path="/" exact render={() => <Redirect to="/login" />} />
            {/* <Route path="/app" exact render={() => <Redirect to="/app/main" />} /> */}
            {/* <UserRoute path="/app" dispatch={props.dispatch} component={LayoutComponent} />
  <AdminRoute path="/admin" currentUser={props.currentUser} dispatch={props.dispatch}
    component={LayoutComponent} /> */}
            <AuthRoute path="/login" exact component={Login} />
            <AuthRoute path="/verify-email" exact component={Verify} />
            <AuthRoute path="/password-reset" exact component={Reset} />
            <AuthRoute path="/forgot" exact component={Forgot} />
            <Redirect from="*" to="/login" />
          </Switch> :
          <div
            className={[
              s.root,
              sidebarStatic ? `${s.sidebarStatic}` : '',
              !sidebarOpened ? s.sidebarClose : '',
              'delight-dashboard',
              `dashboard-${(localStorage.getItem("sidebarType") === SidebarTypes.TRANSPARENT) ? "light" : localStorage.getItem("dashboardTheme")}`,
              `header-${localStorage.getItem("navbarColor") ? localStorage.getItem("navbarColor").replace('#', '') : 'FFFFFF'}`
            ].join(' ')}
          >
            <Sidebar />
            <div className={s.wrap}>
              <Header />
              {/* <Helper /> */}
              <Hammer onSwipe={handleSwipe}>
                {/* <Hammer onSwipe={handleSwipe}> */}
                <main className={s.content}>
                  <BreadcrumbHistory url={props.location.pathname !== '/' ? props.location.pathname : ''} />
                  <CSSTransition
                    key={props.location.key}
                    classNames="fade"
                    timeout={200}
                  >
                    <Switch>
                      <AuthRoute path="/login" exact component={Login} />
                      <AuthRoute path="/verify-email" exact component={Verify} />
                      <AuthRoute path="/password-reset" exact component={Reset} />
                      <AuthRoute path="/forgot" exact component={Forgot} />
                      {/* <Route path="/app/main" exact render={() => <Redirect to="/app/main/analytics" />} /> */}
                      <Route path="/" exact render={() => <Redirect to="/dashboard" />} />
                      <Route path="/dashboard" exact component={Dashboard} />

                      {/* user list */}
                      <Route path="/users" exact component={UsersListPage} />
                      <Route path="/users/add" exact component={AddUserList} />
                      <Route path="/users/:id/update" exact component={AddUserList} />
                      <Route path="/users/:id" exact component={UserListDetails} />
                      {/* **** */}

                      {/* vendor list */}
                      <Route path="/vendor" exact component={VendorList} />
                      <Route path="/vendor/add" exact component={AddVendor} />
                      <Route path="/vendor/:id/update" exact component={AddVendor} />
                      <Route path="/vendor/:id" exact component={VendorListDetails} />
                      {/* **** */}

                      {/* merchant list */}
                      <Route path="/merchant" exact component={MerchantList} />
                      <Route path="/merchant/add" exact component={AddMerchant} />
                      <Route path="/merchant/:id" exact component={AddMerchant} />
                      {/* **** */}

                      {/*  stock list */}
                      <Route path="/stock" exact component={StockListing} />
                      <Route path="/stock/add" exact component={AddNewStock} />
                      <Route path="/stock/:id/update" exact component={AddNewStock} />
                      <Route path="/stock/:id" exact component={StockDetails} />
                      {/* **** */}
                      <Route path="/sales" exact component={SalesListing} />

                      <Route path="/stone/:id" exact component={StoneDetails} />


                      {/* star melee */}

                      <Route path="/star-melee" exact component={StarMeleeListing} />
                      <Route path="/star-melee/add" exact component={AddStarMelee} />
                      <Route path="/star-melee/:id" exact component={AddStarMelee} />
                      <Route path="/star-melee-inquiry" exact component={InquiryStarMelee} />

                      {/* **** */}

                      {/* master */}
                      <Route path="/clarity" exact component={ClarityLIst} />
                      <Route path="/clarity/add" exact component={AddClarity} />
                      <Route path="/clarity/:id" exact component={AddClarity} />
                      <Route path="/color" exact component={ColorList} />
                      <Route path="/color/add" exact component={AddColor} />
                      <Route path="/color/:id" exact component={AddColor} />
                      <Route path="/size" exact component={SizeList} />
                      <Route path="/size/add" exact component={AddSize} />
                      <Route path="/size/:id" exact component={AddSize} />
                      <Route path="/shape" exact component={ShapeList} />
                      <Route path="/shape/add" exact component={AddShape} />
                      <Route path="/shape/all" exact component={AllShapes} />
                      <Route path="/shape/:id" exact component={AddShape} />
                      <Route path="/fancy-color" exact component={FancyColorList} />
                      <Route path="/fancy-color/add" exact component={AddFancyColor} />
                      <Route path="/fancy-color/:id" exact component={AddFancyColor} />
                      <Route path="/finish" exact component={FinishList} />
                      <Route path="/finish/add" exact component={AddFinish} />
                      <Route path="/finish/:id" exact component={AddFinish} />
                      <Route path="/fluorescence" exact component={FluorescenceList} />
                      <Route path="/fluorescence/add" exact component={AddFluorescence} />
                      <Route path="/fluorescence/:id" exact component={AddFluorescence} />

                      {/* *** */}

                      {/* policy */}
                      <Route path="/policy" exact component={Policy} />
                      <Route path="/policy/add" exact component={AddPolicy} />
                      {/* **** */}

                      {/* Marketing */}
                      <Route path="/marketing" exact component={MarketingList} />
                      <Route path="/marketing/add" exact component={AddMarketing} />
                      {/* **** */}



                      {/* request */}
                      <Route path="/request" exact component={RequestListing} />
                      <Route path="/request/slot-add" exact component={AddSlot} />
                      <Route path="/request/:id" exact component={AddSlot} />
                      {/* *** */}




                      <Route path="/cart" exact component={CartListing} />
                      <Route path="/hold" exact component={HoldedList} />
                      <Route path="/confirm" exact component={ConfirmedList} />
                      <Route path="/inquiry" exact component={InquiryListing} />

                      {/*  appoiment and demand */}
                      <Route path="/appointment" exact component={AppoimentList} />
                      <Route path="/demand-list" exact component={DemandList} />
                      {/* ***** */}

                      <Route path="/upload-history" exact component={UploadHistoryList  } />

                      {/* <Route path="" exact render={() => <Redirect to="/users" />} /> */}
                      <Route path="/profile" exact component={Profile} />
                      <Route path="/invalid-stone" exact component={InvalidStoneList} />
                      <Route path="/extra" exact render={() => <Redirect to="/extra/calendar" />} />
                      <Route path="/error" exact component={ErrorPage} />
                      <Route path="*" exact component={ErrorPage} />

                      {/* <Route path="/extra/calendar" exact component={ExtraCalendar} />
                      <Route path="/extra/invoice" exact component={ExtraInvoice} />
                      <Route path="/extra/search" exact component={ExtraSearch} /> */}
                    </Switch>
                  </CSSTransition>
                  <footer className={s.contentFooter}>
                    Delight Diamonds
                  </footer>
                </main>
              </Hammer>
            </div>
          </div>
      }

    </div>
  )
}

const mapStateToProps = store => ({
  currentUser: store.auth.currentUser,
  loadingInit: store.auth.loadingInit,
  sidebarOpened: store.navigation.sidebarOpened,
  sidebarStatic: store.navigation.sidebarStatic,
  dashboardTheme: store.layout.dashboardTheme,
  navbarColor: store.layout.navbarColor,
  sidebarType: store.layout.sidebarType,
  currentUser: store.auth.currentUser,
});

export default withRouter(connect(mapStateToProps)(App));
