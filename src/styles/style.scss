$white: #ffffff;

.main-card {
  border: 0;

  .card-body {
    box-shadow: 0 23px 20px -20px rgba(115, 162, 208, 0.1),
      0 0 15px rgba(115, 162, 208, 0.06);
    padding: 20px;
  }
}

.nav-link {
  padding: 12px 18px;
  border: 0 !important;
  color: #798892;

  &:hover {
    color: #0c2236;
  }
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  background-color: $white;
}

.btn-action {
  background-color: lighten($color: #142542, $amount: 75);
  width: 45px;
  height: 45px;
  display: inline-flex;
  border-radius: 50px;
  align-items: center;
  justify-content: center;
  transition: all 0.5s;
  border: 0;
  color: #142542;

  &:hover {
    background-color: #142542;
    color: $white;
  }
}

.search {
  position: relative;
  margin-bottom: 20px;

  input {
    padding-right: 30px;
    height: 40px;
  }

  .btn-search {
    padding: 0;
    position: absolute;
    top: 7px;
    right: 4px;
  }
}

.list-card {
  // height: 72vh;
  max-height: 46vh;
  overflow-y: auto;

  scrollbar-width: 0;

  &::-webkit-scrollbar {
    width: 0;
  }
}

.texteditor {
  .toolbar {
    flex-wrap: wrap;
  }
}

.react-text-editor {
  margin: 0 !important;
  border-color: #c1ccd3 !important;
  height: 40vh !important;
  background-color: #f9fbfd !important;
}

.pass-field {
  position: relative;

  input {
    padding-right: 35px;
  }

  .pass-hide_show {
    position: absolute;
    top: 13px;
    right: 9px;
    background-color: transparent;
    padding: 0;
    border: 0;
  }
}

.hover-div:hover {
  background-color: #c2b362;
  color: #ffffff;
}

.coustom-table table {
  width: auto !important;
}

.coustom-table table td,
.coustom-table table th {
  min-width: 170px;
  max-width: 170px;
  word-break: break-word;
  white-space: normal !important;
}

.form-error {
  color: red;
  font-weight: 500;
  font-size: 14px;
}


@media (min-width: 576px) {
  .custom-modal {
    max-width: 80% !important;
    margin-right: auto;
    margin-left: auto;
  }
}

.shape-box {
  text-align: center;

  img {
    height: 60px;
  }

  p {
    margin-top: 10px;
  }
}

.shape-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: start;

  .shape-box {
    width: 16.66%;
    flex: 0 0 16.66%;
    margin-bottom: 20px;
  }
}

.stone-table {
  table {
    th:first-child,
    td:first-child {
      min-width: 50px;
    }
    th,
    td {
      white-space: nowrap !important;
      // min-width: 90px;
    }
  }
}

.react-bs-table-no-data{
  width: 100% !important;
  max-width: 100% !important;
}
.react-bs-table table.stone-table {
  table-layout: auto !important;
}

.reason-list{
  list-style: disc !important;
  padding-left: 20px;
}