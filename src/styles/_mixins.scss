/**
 * Custom application mixins available through out the app
 */

// define sass mixin
// Generates single property classes
// -------------------------
@mixin property-variants($base, $property, $variants: ()) {
  @each $size, $value in $variants {
    @if $size == 'md' {
      #{$base} {
        #{$property}: $value;
      }

      #{$base}-n {
        #{$property}: $value;
      }
    }

    #{$base}-#{$size} {
      #{$property}: $value;
    }

    #{$base}-n-#{$size} {
      #{$property}: -$value;
    }
  }
}

// Common thumbnail properties
// -------------------------
@mixin thumb($parent, $size) {
  #{$parent} {
    width: $size;

    > .glyphicon,
    > .fa {
      line-height: $size;
    }
  }
}

@mixin border-radius($radius) {
  border-radius: $radius;
}

@mixin scroll-bar($color) {
  &::-webkit-scrollbar {
    height: 8px;
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    border: none;
    background-color: transparent;
  }

  &:hover::-webkit-scrollbar-thumb {
    background-color: $color;
  }
}

@mixin keyframes($name) {
  @-webkit-keyframes #{$name} {
    @content;
  }

  @keyframes #{$name} {
    @content;
  }
}

/* Chat Scrollbar */

@mixin chat-scrollbar($color, $width) {
  &::-webkit-scrollbar {
    width: $width;
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    border: none;
    border-radius: $border-radius;
    background-color: transparent;
  }

  &:hover::-webkit-scrollbar-thumb {
    border: none;
    background-color: $color;
  }
}


// Hover mixin and `$enable-hover-media-query` are deprecated.
//
// Originally added during our alphas and maintained during betas, this mixin was
// designed to prevent `:hover` stickiness on iOS-an issue where hover styles
// would persist after initial touch.
//
// For backward compatibility, we've kept these mixins and updated them to
// always return their regular pseudo-classes instead of a shimmed media query.
//
// Issue: https://github.com/twbs/bootstrap/issues/25195

@mixin hover() {
  &:hover { @content; }
}

@mixin hover-focus() {
  &:hover,
  &:focus {
    @content;
  }
}

@mixin plain-hover-focus() {
  &,
  &:hover,
  &:focus {
    @content;
  }
}

@mixin hover-focus-active() {
  &:hover,
  &:focus,
  &:active {
    @content;
  }
}

// stylelint-disable declaration-no-important

// Typography

@mixin text-emphasis-variant($parent, $color, $ignore-warning: false) {
  #{$parent} {
    color: $color !important;
  }
  @if $emphasized-link-hover-darken-percentage != 0 {
    a#{$parent} {
      @include hover-focus() {
        color: shade-color($color, $emphasized-link-hover-darken-percentage) !important;
      }
    }
  }
  //@include deprecate("`text-emphasis-variant()`", "v4.4.0", "v5", $ignore-warning);
}

// stylelint-disable declaration-no-important

// Contextual backgrounds

@mixin bg-variant($parent, $color, $ignore-warning: false) {
  #{$parent} {
    background-color: $color !important;
  }
  a#{$parent},
  button#{$parent} {
    :hover, :focus {
      background-color: shade-color($color, 10) !important;
    }
  }
  //@include deprecate("The `bg-variant` mixin", "v4.4.0", "v5", $ignore-warning);
}

@mixin bg-gradient-variant($parent, $color, $ignore-warning: false) {
  #{$parent} {
    background: $color linear-gradient(180deg, mix($body-bg, $color, 15%), $color) repeat-x !important;
  }
  //@include deprecate("The `bg-gradient-variant` mixin", "v4.5.0", "v5", $ignore-warning);
}

@mixin badge-variant($bg) {
  color: color-contrast($bg);
  background-color: $bg;

  @at-root a#{&} {
    @include hover-focus() {
      color: color-contrast($bg);
      background-color: ($bg, 10%);
    }

    &:focus,
    &.focus {
      outline: 0;
      box-shadow: 0 0 0 $badge-focus-width rgba($bg, .5);
    }
  }
}

@function theme-color($key: "primary") {
  @return map-get($theme-colors, $key);
}
