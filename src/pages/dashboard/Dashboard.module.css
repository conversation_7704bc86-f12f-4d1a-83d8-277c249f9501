/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.root :global .post-comments footer {
  margin: 0 -20px -15px;
  padding: 15px 20px;
}
.root :global .stat-item .name {
  margin-top: 10px;
}

.table :global(.table.table-sm) td {
  font-size: 1rem;
  vertical-align: middle;
}/*# sourceMappingURL=Dashboard.module.css.map */