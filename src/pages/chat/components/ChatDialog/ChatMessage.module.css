/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.chatMessage {
  position: relative;
  padding-left: 60px;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.chatMessage .messageBody {
  border-radius: 0.3rem;
  padding: 0.5rem 1rem;
  line-height: 2;
  margin-bottom: 0.5rem;
  background: #fff;
  display: inline-block;
}
.chatMessage .messageBody.messageAttachment {
  padding: 0;
  max-width: 70%;
  overflow: hidden;
}
.chatMessage .messageBody img {
  max-width: 100%;
}
.chatMessage .messageAvatar {
  position: absolute;
  top: 0;
  left: 10px;
}
.chatMessage.owner {
  padding-left: 0;
  padding-right: 60px;
  align-items: flex-end;
}
.chatMessage.owner .messageBody {
  background: #3ceff0;
  color: #fff;
}
.chatMessage.owner .messageAvatar {
  left: unset;
  right: 10px;
}/*# sourceMappingURL=ChatMessage.module.css.map */