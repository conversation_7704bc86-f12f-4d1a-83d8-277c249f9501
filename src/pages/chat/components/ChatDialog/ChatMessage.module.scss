@import "../../../../styles/app";

.chatMessage {
  position: relative;
  padding-left: 60px;
  margin-bottom: $spacer;
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .messageBody {
    border-radius: $border-radius;
    padding: $spacer/2 $spacer;
    line-height: 2;
    margin-bottom: $spacer / 2;
    background: $white;
    display: inline-block;

    &.messageAttachment {
      padding: 0;
      max-width: 70%;
      overflow: hidden;
    }

    img {
      max-width: 100%;
    }
  }

  .messageAvatar {
    position: absolute;
    top: 0;
    left: 10px;
  }

  &.owner {
    padding-left: 0;
    padding-right: 60px;
    align-items: flex-end;

    .messageBody {
      background: lighten($purple, 15%);
      color: $white;
    }

    .messageAvatar {
      left: unset;
      right: 10px;
    }
  }
}
