/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.chatDialogSection {
  min-width: 50%;
  padding: 0 21px;
}

.chatDialogHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}
.chatDialogHeader .infoIcon {
  font-size: 2rem;
  color: #0C2236;
}

.chatDialogBody {
  flex: 1;
  overflow: auto;
}
.chatDialogBody::-webkit-scrollbar {
  width: 3px;
  background: transparent;
}
.chatDialogBody::-webkit-scrollbar-track {
  background: transparent;
}
.chatDialogBody::-webkit-scrollbar-thumb {
  border: none;
  border-radius: 0.3rem;
  background-color: transparent;
}
.chatDialogBody:hover::-webkit-scrollbar-thumb {
  border: none;
  background-color: rgba(12, 34, 54, 0.3);
}
.chatDialogBody .dialogDivider {
  text-align: center;
  padding: 1rem 0;
  font-size: 1.1rem;
  color: #0C2236;
}

.newMessage {
  display: flex;
  align-items: center;
}
.newMessage input {
  margin: 0 1rem;
}
.newMessage .attachment {
  font-size: 24px;
  color: #0C2236;
  background-color: transparent;
  border: none;
}
.newMessage .attachment:hover {
  color: #03080c;
}/*# sourceMappingURL=ChatDialog.module.css.map */