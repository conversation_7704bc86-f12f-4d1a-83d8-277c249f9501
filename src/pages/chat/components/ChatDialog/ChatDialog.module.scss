@import '../../../../styles/app';

.chatDialogSection {
  min-width: 50%;
  padding: 0 $chat-component-padding;
}

.chatDialogHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacer;

  .infoIcon {
    font-size: $spacer * 2;
    color: $text-color;
  }
}

.chatDialogBody {
  flex: 1;
  overflow: auto;
  @include chat-scrollbar(rgba($text-color, 0.3), 3px);

  .dialogDivider {
    text-align: center;
    padding: $spacer 0;
    font-size: $font-size-larger;
    color: $text-color;
  }
}

.newMessage {
  display: flex;
  align-items: center;

  input {
    margin: 0 $spacer;
  }

  .attachment {
    font-size: 24px;
    color: $text-color;
    background-color: transparent;
    border: none;
    &:hover {
      color: darken($text-color, 10%);
    }
  }
}
