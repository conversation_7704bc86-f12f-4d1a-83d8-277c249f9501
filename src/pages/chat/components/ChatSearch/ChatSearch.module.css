/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.searchBox .chatInput {
  background-color: #F7F7F8;
}
.searchBox .chatInput:focus {
  background-color: #F7F7F8;
}
.searchBox .inputIcon {
  background-color: #F7F7F8;
  transition: background-color ease-in-out 0.15s;
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px;
}/*# sourceMappingURL=ChatSearch.module.css.map */