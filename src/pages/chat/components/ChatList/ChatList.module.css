/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.chatsSectionWrap {
  flex: 1;
  overflow: auto;
}
.chatsSectionWrap::-webkit-scrollbar {
  width: 3px;
  background: transparent;
}
.chatsSectionWrap::-webkit-scrollbar-track {
  background: transparent;
}
.chatsSectionWrap::-webkit-scrollbar-thumb {
  border: none;
  border-radius: 0.3rem;
  background-color: transparent;
}
.chatsSectionWrap:hover::-webkit-scrollbar-thumb {
  border: none;
  background-color: rgba(121, 136, 146, 0.15);
}

.chatList {
  margin: 0 -21px;
}

.chatListItem {
  padding-left: 21px;
}
.chatListItem .chatListItemWrapper {
  display: flex;
  align-items: center;
  padding: 1.5rem 21px 1.5rem 0;
}
.chatListItem .chatItemMain {
  flex: 1;
  min-width: 0;
}
.chatListItem .chatTitle {
  margin-bottom: 0;
}
.chatListItem .chatTitle span {
  margin: 0 5px;
}
.chatListItem .chatTitle .groupChatIcon {
  width: 15px;
}
.chatListItem .chatTitle i {
  opacity: 0.3;
}
.chatListItem .chatLastMessage {
  margin: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.chatListItem .chatLastMessage,
.chatListItem .timestamp {
  color: #798892;
}
.chatListItem:hover {
  cursor: pointer;
  background: rgba(0, 0, 0, 0.01);
}
.chatListItem.active {
  background: #3ceff0;
}
.chatListItem.active .chatTitle,
.chatListItem.active .chatLastMessage,
.chatListItem.active .timestamp {
  color: #fff;
}
.chatListItem.active .chatTitle {
  font-weight: 600;
}

.chatListItem + .chatListItem .chatListItemWrapper {
  border-top: 1px solid #d6dee5;
}
.chatListItem + .chatListItem.active .chatListItemWrapper {
  border-top-color: #36cee6;
}

.personalChats,
.groupChats {
  flex: 1;
  overflow: auto;
}
.personalChats::-webkit-scrollbar,
.groupChats::-webkit-scrollbar {
  width: 3px;
  background: transparent;
}
.personalChats::-webkit-scrollbar-track,
.groupChats::-webkit-scrollbar-track {
  background: transparent;
}
.personalChats::-webkit-scrollbar-thumb,
.groupChats::-webkit-scrollbar-thumb {
  border: none;
  border-radius: 0.3rem;
  background-color: transparent;
}
.personalChats:hover::-webkit-scrollbar-thumb,
.groupChats:hover::-webkit-scrollbar-thumb {
  border: none;
  background-color: rgba(121, 136, 146, 0.15);
}

.allChats {
  overflow: auto;
  flex: 1;
}
.allChats::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
.allChats::-webkit-scrollbar-track {
  background: transparent;
}
.allChats::-webkit-scrollbar-thumb {
  border: none;
  border-radius: 0.3rem;
  background-color: transparent;
}
.allChats:hover::-webkit-scrollbar-thumb {
  border: none;
  background-color: rgba(121, 136, 146, 0.15);
}

.chatList {
  margin: 0 -21px;
}

.chatListItem {
  padding-left: 21px;
}
.chatListItem .chatListItemWrapper {
  display: flex;
  align-items: center;
  padding: 1rem 21px 1rem 0;
}
.chatListItem .chatListItemWrapper .avatarsColumn {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}
.chatListItem .chatListItemWrapper .avatarsColumn li {
  height: 15px;
  display: flex;
  align-items: center;
}
.chatListItem .chatItemMain {
  flex: 1;
  min-width: 0;
}
.chatListItem .chatTitle {
  margin-bottom: 0;
}
.chatListItem .chatLastMessage {
  margin: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.chatListItem .chatLastMessage,
.chatListItem .timestamp {
  color: #798892;
}
.chatListItem:hover {
  cursor: pointer;
  background: rgba(0, 0, 0, 0.01);
}
.chatListItem.active {
  background: #9083F7;
}
.chatListItem.active .chatTitle,
.chatListItem.active .chatLastMessage,
.chatListItem.active .timestamp {
  color: #fff;
}
.chatListItem.active .chatTitle {
  font-weight: 600;
}

.chatListItem + .chatListItem .chatListItemWrapper {
  border-top: 1px solid #d6dee5;
}
.chatListItem + .chatListItem.active .chatListItemWrapper {
  border-top-color: #9083F7;
  background: #9083F7;
}

.searchBox .chatInput {
  background-color: #F7F7F8;
}
.searchBox .chatInput:focus {
  background-color: #F7F7F8;
}
.searchBox :global .input-group-prepend {
  background-color: #F7F7F8;
  transition: background-color ease-in-out 0.15s;
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px;
}/*# sourceMappingURL=ChatList.module.css.map */