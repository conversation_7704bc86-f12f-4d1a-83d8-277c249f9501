/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.chatInfoHeader {
  color: #fff;
  position: relative;
}
.chatInfoHeader .avatarsRow {
  display: flex;
  align-items: center;
  padding-right: 10px;
  margin-bottom: 0;
}
.chatInfoHeader .avatarsRow li {
  width: 15px;
  display: flex;
  justify-content: center;
}
.chatInfoHeader .socialLinks {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}
.chatInfoHeader .socialLinks .socialLink a {
  background: #fff;
  border-radius: 50%;
  color: #10CFD0;
  font-size: 18px;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}
.chatInfoHeader .socialLinks .socialLink a .fa-facebook {
  margin-top: 7px;
  margin-left: 3px;
  font-size: 22px;
}
.chatInfoHeader .socialLinks .socialLink + .socialLink {
  margin-left: 0.8333333333rem;
}

.dynamicCard {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
}
.dynamicCard::-webkit-scrollbar {
  width: 3px;
  background: transparent;
}
.dynamicCard::-webkit-scrollbar-track {
  background: transparent;
}
.dynamicCard::-webkit-scrollbar-thumb {
  border: none;
  border-radius: 0.3rem;
  background-color: transparent;
}
.dynamicCard:hover::-webkit-scrollbar-thumb {
  border: none;
  background-color: rgba(121, 136, 146, 0.3);
}
.dynamicCard .toggleHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.dynamicCard :global .accordion .card {
  position: static;
  border-left: none;
  border-top: none;
  border-right: none;
  box-shadow: none;
}
.dynamicCard :global .accordion .card .card-body {
  padding: 0.35em 0.35em 0.35em 3em;
}
.dynamicCard :global .accordion .card .card-body.files-tab {
  padding: 0.35em 1.3em;
}
.dynamicCard :global .accordion .card:last-child {
  border-bottom: none;
}
.dynamicCard :global .accordion .card button {
  text-align: left;
  border: none;
  font-size: 19px;
  padding: 13px 1.25rem;
  background: #fff;
}
.dynamicCard :global .accordion .card button:focus {
  outline: none;
}
.dynamicCard :global .accordion .card button .la.la-angle-up {
  transition: transform 0.25s ease;
}
.dynamicCard :global .accordion .card button.active .la.la-angle-up {
  transform: rotate(180deg);
}
.dynamicCard :global .accordion .card button img {
  margin-right: 10px;
  width: 20px;
}
.dynamicCard :global .toggle input[type=checkbox] {
  display: none;
}
.dynamicCard :global .toggle label {
  color: #000000;
  font-size: 19px;
  width: 100%;
  padding: 13px 1.25rem;
  margin: 0;
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}
.dynamicCard :global .toggle input[type=checkbox] + label::before {
  content: " ";
  display: block;
  height: 12px;
  width: 35px;
  border-radius: 9px;
  position: absolute;
  top: 20px;
  right: 10px;
  background: #26CD5F;
}
.dynamicCard :global .toggle input[type=checkbox] + label::after {
  content: " ";
  display: block;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  position: absolute;
  top: 16px;
  border: 1px solid #E6E5EA;
  right: 25px;
  background: #fff;
  transition: all 0.3s ease-in;
}
.dynamicCard :global .toggle input[type=checkbox]:checked + label::after {
  right: 5px;
  transition: all 0.3s ease-in;
}
.dynamicCard .linksBody {
  margin: 0;
  padding: 0;
}
.dynamicCard .linksBody li {
  margin-bottom: 5px;
}
.dynamicCard .personalInformation {
  padding: 0;
  margin: 0;
}
.dynamicCard .personalInformation li:nth-child(odd) {
  color: #313947;
  font-size: 17px;
}
.dynamicCard .personalInformation li:nth-child(even) {
  color: rgba(49, 57, 71, 0.5);
  font-size: 14px;
  margin-top: 5px;
}
.dynamicCard .personalInformation li:nth-child(3n+2) {
  margin-bottom: 20px;
}
.dynamicCard .listWithImages {
  margin: 0;
  padding: 0;
}
.dynamicCard .listWithImages li {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.dynamicCard .listWithImages li .imgText {
  margin-left: 15px;
}

.cursorStyle {
  cursor: pointer;
}

.groupListModalWrapper {
  position: absolute;
  top: 50%;
  left: -50%;
  z-index: 1000;
}
.groupListModalWrapper .groupListModal {
  overflow-y: auto;
  max-height: 450px;
  width: 300px;
  color: #0C2236;
}
.groupListModalWrapper .groupListModal::-webkit-scrollbar {
  width: 3px;
  background: transparent;
}
.groupListModalWrapper .groupListModal::-webkit-scrollbar-track {
  background: transparent;
}
.groupListModalWrapper .groupListModal::-webkit-scrollbar-thumb {
  border: none;
  border-radius: 0.3rem;
  background-color: transparent;
}
.groupListModalWrapper .groupListModal:hover::-webkit-scrollbar-thumb {
  border: none;
  background-color: rgba(121, 136, 146, 0.3);
}
.groupListModalWrapper .groupListModal .groupListHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}
.groupListModalWrapper .groupListModal .groupListHeader span {
  cursor: pointer;
}
.groupListModalWrapper .groupListModal .groupListHeader .laLg {
  vertical-align: middle;
}
.groupListModalWrapper .groupListModal .groupList {
  margin: 0;
  padding: 0;
}
.groupListModalWrapper .groupListModal .groupList > li {
  display: flex;
  align-items: center;
  border-radius: 0.3rem;
  padding: 0.5rem;
}
.groupListModalWrapper .groupListModal .groupList > li:hover {
  cursor: pointer;
  background: rgba(0, 0, 0, 0.01);
}
@media (max-width: 767.98px) {
  .groupListModalWrapper {
    width: 100%;
    left: 0;
  }
  .groupListModalWrapper .groupListModal {
    margin: 0;
    width: 100%;
  }
}
.groupListModalWrapper .backdrop {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

.switch i {
  height: 12px;
  padding-right: 15px;
}
.switch i::before {
  display: block;
  width: 16px;
  height: 16px;
  margin-top: -2px;
  box-shadow: none;
  border: 1px solid #d6dee5;
}
.switch :checked + i {
  padding-right: 0;
  padding-left: 15px;
  background: #3da93b;
}/*# sourceMappingURL=ChatInfo.module.css.map */