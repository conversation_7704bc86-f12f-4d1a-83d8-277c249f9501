/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.avatar {
  height: 30px;
  width: 30px;
  min-width: 30px;
  position: relative;
}
.avatar .imageWrapper {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: 600;
  color: #fff;
  overflow: hidden;
  background: #FF5574;
}
.avatar .imageWrapper.stroke {
  border: 2px solid #fff;
}
.avatar img {
  width: 100%;
  height: 100%;
}
.avatar .status {
  position: absolute;
  bottom: 1px;
  right: 1px;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  border: 1px solid #fff;
}

.imgWrap {
  overflow: hidden;
  border-radius: 50%;
  height: 40px;
  width: 40px;
}
.imgWrap img {
  width: 100%;
}

.sharedDialog {
  position: relative;
  top: -18px;
}
.sharedDialog .imgWrap {
  border: 3px solid #fff;
}
.sharedDialog .imgWrap:nth-child(1) {
  position: absolute;
  top: 19px;
}
.sharedDialog .imgWrap:nth-child(2) {
  position: absolute;
  top: 38px;
}/*# sourceMappingURL=Avatar.module.css.map */