/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.chatPage {
  flex-grow: 1;
  display: flex;
  height: calc(100vh - 60px - 2 * 40px - 20px);
  position: relative;
}
.chatPage .form-control {
  background-color: #f8f9fa;
}
.chatPage .form-control::-moz-placeholder {
  font-size: 0.9rem;
}
.chatPage .form-control:-ms-input-placeholder {
  font-size: 0.9rem;
}
.chatPage .form-control::placeholder {
  font-size: 0.9rem;
}
.chatPage .form-control:focus {
  background-color: #e9ecef;
}

:global .chat-section {
  box-shadow: var(--widget-shadow);
  border-radius: 0.3rem;
  background: #fff;
  padding: 21px;
  margin-bottom: 1rem;
}
:global .chat-dialog-section {
  padding: 0 21px;
  height: 100%;
  flex: 1;
}
:global .chat-info-section,
:global .chat-list-section {
  width: 330px;
  min-width: 330px;
  height: 100%;
  display: flex;
  flex-direction: column;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  :global .chat-page-wrapper .chat-info-section,
:global .chat-page-wrapper .chat-list-section,
:global .chat-page-wrapper .chat-dialog-section {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 1;
    transition: left 0.3s ease-in-out, opacity 0.2s ease-in-out, padding-right 0.2s ease-in-out, padding-left 0.2s ease-in-out, width 0.2s ease-in-out;
  }
  :global .chat-page-wrapper .chat-info-section {
    opacity: 0;
    left: 100vw;
  }
  :global .chat-page-wrapper .chat-dialog-section {
    left: 330px;
    padding-right: 0;
    width: calc(100% - 330px);
  }
  :global .chat-page-wrapper .chat-list-section {
    left: 0;
  }
  :global .chat-page-wrapper.info-state .chat-info-section {
    opacity: 1;
    left: calc(100% - 330px);
  }
  :global .chat-page-wrapper.info-state .chat-dialog-section {
    left: 0;
    padding-right: 21px;
    padding-left: 0;
  }
  :global .chat-page-wrapper.info-state .chat-list-section {
    left: -330px;
    opacity: 0;
  }
}
@media (max-width: 767.98px) {
  :global .chat-page-wrapper .chat-info-section,
:global .chat-page-wrapper .chat-list-section,
:global .chat-page-wrapper .chat-dialog-section {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    min-width: 100%;
    opacity: 1;
    transition: left 0.3s ease-in-out, opacity 0.2s ease-in-out;
  }
  :global .chat-page-wrapper .chat-info-section,
:global .chat-page-wrapper .chat-dialog-section {
    opacity: 0;
    left: 100vw;
  }
  :global .chat-page-wrapper .chat-dialog-section {
    padding: 0;
  }
  :global .chat-page-wrapper .chat-section {
    margin-right: 0;
    margin-left: 0;
  }
  :global .chat-page-wrapper.chat-state .chat-dialog-section {
    opacity: 1;
    left: 0;
  }
  :global .chat-page-wrapper.chat-state .chat-list-section,
:global .chat-page-wrapper.chat-state .chat-info-section {
    opacity: 0;
  }
  :global .chat-page-wrapper.info-state .chat-info-section {
    opacity: 1;
  }
  :global .chat-page-wrapper.info-state .chat-dialog-section,
:global .chat-page-wrapper.info-state .chat-info-section {
    left: 0;
  }
  :global .chat-page-wrapper.info-state .chat-list-section,
:global .chat-page-wrapper.info-state .chat-dialog-section {
    opacity: 0;
  }
  :global .chat-page-wrapper .chat-mobile-navigation {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    color: #0C2236;
  }
  :global .chat-page-wrapper .chat-mobile-navigation .la {
    margin-right: 0.5rem;
  }
}/*# sourceMappingURL=Chat.module.css.map */