import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useHistory } from "react-router";
import { addMarketing } from "../../Services/actions/MarketingAction";

function AddMarketing(props) {
  const history = useHistory();
  const [uploadImage, setUploadImage] = useState('')
  const [state, setState] = useState({
    name: "",
    type: "",
    image: "",
  });
  const [error, setError] = useState({
    name: false,
    type: false,
    image: false,
  });

  const handleInput = (e) => {
    const { name, value } = e.target;

    setState((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    if (name === 'image') {
      setUploadImage(e.target.files[0])
    }

    switch (name) {
      case "name":
        if (value === "") {
          error[name] = "* Enter Marketing Name";
        } else {
          error[name] = false;
        }
        break;
      case "type":
        if (value === "") {
          error[name] = "* Enter Marketing Type";
        } else {
          error[name] = false;
        }
        break;
      case "image":
        if (value === "") {
          error[name] = "* Enter Marketing Image";
        } else {
          error[name] = false;
        }
        break;
      default:
        break;
    }
  };

  const submitMarketing = () => {
    if (state.name === "") {
      setError(prev => ({
        ...prev,
        name: "* Enter Marketing Name"
      }))
    } if (state.type === "") {
      setError(prev => ({
        ...prev,
        type: "* Enter Marketing Type"
      }))
    } if (state.image === "") {
      setError(prev => ({
        ...prev,
        image: "* Enter Marketing Image"
      }))
    }

    if (
      state.name !== "" &&
      state.type !== "" &&
      state.image !== ""
    ) {
      const details = {
        name: state.name,
        type: state.type,
        // image: state.image
        image: uploadImage
      };

      props.addMarketing(details);
    }
  };

  useEffect(() => {
    if (props.addMarketingReducer.success) {
      history.push("/marketing");
    }
  }, [props.addMarketingReducer]);

  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Add - <span className="fw-semi-bold">Marketing</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <h4 className="mb-5 fw-light">Add Marketing</h4>

          <div className="col-lg-3 col-md-6">
            <div className="mb-4">
              <label className="form-label">Marketing Name</label>
              <input
                placeholder="Enter marketing name"
                type="text"
                name="name"
                className="form-control"
                value={state.name}
                onChange={handleInput}
              />
              {error.name && (
                <div>
                  <span className="text-danger h6">{error.name}</span>
                </div>
              )}
            </div>
            <div className="mb-4">
              <label className="form-label">Marketing Type</label>
              <select
                className="form-select"
                name="type"
                value={state.type}
                onChange={handleInput}
              >
                <option value="">Select Marketing type</option>
                <option value={"1"}>Home</option>
                <option value={"2"}>Dashboard</option>
              </select>
              {error.type && (
                <div>
                  <span className="text-danger h6">{error.type}</span>
                </div>
              )}
            </div>
            <div className="mb-4">
              <label className="form-label">Marketing Image</label>
              <input
                // placeholder="Enter Fancy color Name"
                name="image"
                type="file"
                className="form-control"
                value={state.image === null ? "" : state.image}
                onChange={handleInput}
                accept="image/jpeg, image/jpg, image/png"
              />
              {error.image && (
                <div>
                  <span className="text-danger h6">{error.image}</span>
                </div>
              )}
            </div>
            <button className="btn btn-primary" onClick={submitMarketing}>
              Submit
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

const mapStateToProp = (state) => ({
  addMarketingReducer: state.MarketingReducer.addMarketing,
});

const mapDispatchToProps = (dispatch) => ({
  addMarketing: (details) => dispatch(addMarketing(details)),
});

export default connect(mapStateToProp, mapDispatchToProps)(AddMarketing);
