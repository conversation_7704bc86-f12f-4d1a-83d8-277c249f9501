import React, { useEffect, useState } from "react";
import { BootstrapTable, TableHeaderColumn } from "react-bootstrap-table";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import {  Button, Dropdown, DropdownItem, DropdownMenu, DropdownToggle, Modal,  Modal<PERSON>ody,  Modal<PERSON>ooter,  ModalHeader,} from "reactstrap";
import Loader from "../../components/Loader/Loader";
import {  changeMarketingStatus,  deleteMarketing,  getMarketing,} from "../../Services/actions/MarketingAction";
import {  RESET_ADD_MARKETING,  RESET_DELETE_MARKETING,  RESET_MARKETING_STATUS} from "../../Services/Constant";
import { toast } from "react-toastify";
function MarketingList(props) {
  const [state, setState] = useState([]);
  const [loading, setLoading] = useState(true)
  const [isDelete, setIsDelete] = useState(false);
  const [deleteId, setDeleteId] = useState("");

  useEffect(() => {
    if (!props.getMarketingReducer.success) {
      props.getMarketing();
    }
  }, []);

  useEffect(() => {
    setLoading(props.getMarketingReducer.loading)
    if (props.getMarketingReducer.success) {
      const data = props.getMarketingReducer.data;
      const list = data.map((e, i) => {
        return {
          srNo: i + 1,
          name: e.name,
          status: e.status,
          id: e.id,
        };
      });
      setState([...list]);
    }
  }, [props.getMarketingReducer]);

  const handleStatus = async (id, status) => {
    const details = {
      status: status === 1 ? 0 : 1,
    };
    await props.changeMarketingStatus({
      details: details,
      id: id,
    });
    const list = [...state];
    const findItemIndex = state.findIndex((e) => e.id === id);
    list[findItemIndex]["status"] = list[findItemIndex]["status"] === 1 ? 0 : 1;
    setState([...list]);
  };

  useEffect(() => {
    if (props.statusMarketingReducer.success) {
      // props.getMarketing();
      props.resetChangeMarketingStatus();
    }
  }, [props.statusMarketingReducer]);

  const handleMarketing = (id) => {
    props.deleteMarketing(id);
  };

  useEffect(() => {
    if (props.deleteMarketingReducer.success) {
      props.getMarketing();
      setIsDelete(false);
      setDeleteId("");
      toast.success("marketing deleted successfully...");
      props.resetDeleteMarketing();
    }
  }, [props.deleteMarketingReducer]);

  useEffect(() => {
    if (props.addMarketingReducer.success) {
      props.getMarketing();
      toast.success("marketing added successfully...");
      props.resetAddMarketing();
    }
  }, [props.addMarketingReducer]);

  const statusChange = (cell, value) => {
    return (
      <div className={`d-flex justify-content-between`}>
        <Button
          color={value.status === 1 ? "default" : "secondary"}
          className="btn btn-rounded-f"
          type="button"
          onClick={() => handleStatus(value.id, value.status)}
        >
          {value.status === 1 ? "Active" : "Deactive"}
        </Button>
      </div>
    );
  };

  const actionFormatter = (cell, value) => {
    return (
      <div className={`d-flex justify-content-center align-items-center`}>
        <div
          className="btn text-danger"
          onClick={() => {
            setIsDelete(true);
            setDeleteId(value.id);
          }}
        >
          <i className="fa fa-trash" />
        </div>
      </div>
    );
  };

  const renderSizePerPageDropDown = (props) => {
    const limits = [];
    props.sizePerPageList.forEach((limit) => {
      limits.push(
        <DropdownItem
          key={limit}
          onClick={() => props.changeSizePerPage(limit)}
        >
          {limit}
        </DropdownItem>
      );
    });
    return (
      <Dropdown isOpen={props.open} toggle={props.toggleDropDown}>
        <DropdownToggle color="default" caret>
          {props.currSizePerPage}
        </DropdownToggle>
        <DropdownMenu>{limits}</DropdownMenu>
      </Dropdown>
    );
  };

  const options = {
    sizePerPage: 10,
    paginationSize: 5,
    // searchField: createCustomSearchField,
    sizePerPageDropDown: renderSizePerPageDropDown,
  };
  return (
    <>
      <div className="page-top-line">
        <h2 className="page-title">
          Marketing - <span className="fw-semi-bold">Management</span>
        </h2>
      </div>
      <div className="card main-card">
        <div className="card-body">
          <div className="text-end mb-4">
            <Link to="/marketing/add" className="btn btn-primary py-2">
              Add Marketing
            </Link>
          </div>
          {loading ? <Loader /> :
            <BootstrapTable
              bordered={false}
              data={state}
              version="4"
              pagination
              options={options}
              search
              scrollTop={"Bottom"}
              tableContainerClass={`coustom-table stone-table table-striped table-hover`}
            >
              <TableHeaderColumn dataField="srNo" width="50px">
                <span className="fs-sm">Sr No.</span>
              </TableHeaderColumn>

              <TableHeaderColumn dataField="name" dataSort width="100%">
                <span className="fs-sm">Marketing</span>
              </TableHeaderColumn>
              <TableHeaderColumn
                dataField="status"
                dataFormat={statusChange.bind(this)}
              >
                <span className="fs-sm">status</span>
              </TableHeaderColumn>

              <TableHeaderColumn
                isKey
                dataField="id"
                dataFormat={actionFormatter.bind(this)}
              >
                <span className="fs-sm text-center d-block">Actions</span>
              </TableHeaderColumn>
            </BootstrapTable>}
        </div>
      </div>
      <Modal
        size="sm"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        isOpen={isDelete}
        toggle={() => {
          setIsDelete(false);
          setDeleteId("");
        }}
      >
        <ModalHeader
          toggle={() => {
            setIsDelete(false);
            setDeleteId("");
          }}
        >
          Confirm delete
        </ModalHeader>
        <ModalBody className="bg-white">
          Are you sure you want to delete this marketng?
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              setIsDelete(false);
              setDeleteId("");
            }}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handleMarketing(deleteId);
            }}
          >
            Delete
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
}

const mapStateToProp = (state) => ({
  getMarketingReducer: state.MarketingReducer.getMarketing,
  addMarketingReducer: state.MarketingReducer.addMarketing,
  deleteMarketingReducer: state.MarketingReducer.deleteMarketing,
  statusMarketingReducer: state.MarketingReducer.statusMarketing,
});

const mapDispatchToProps = (dispatch) => ({
  getMarketing: () => dispatch(getMarketing()),
  deleteMarketing: (id) => dispatch(deleteMarketing(id)),
  changeMarketingStatus: (details) => dispatch(changeMarketingStatus(details)),
  resetDeleteMarketing: (id) => dispatch({ type: RESET_DELETE_MARKETING }),
  resetChangeMarketingStatus: (id) =>
    dispatch({ type: RESET_MARKETING_STATUS }),
  resetAddMarketing: (id) => dispatch({ type: RESET_ADD_MARKETING }),
});
export default connect(mapStateToProp, mapDispatchToProps)(MarketingList);
