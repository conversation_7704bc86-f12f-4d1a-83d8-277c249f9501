import React from 'react';
import {
  Container,
  Form,
  FormGroup,
  Input,
  Button,
} from 'reactstrap';
import { Link } from 'react-router-dom';

import s from './ErrorPage.module.scss';

class ErrorPage extends React.Component {
  render() {
    return (
      <div className={s.errorPage}>
        <Container>
          <div className={`${s.errorContainer} mx-auto`}>
            <h1 className={s.errorCode}>404</h1>
            <p className={s.errorInfo}>
              Opps, it seems that this page does not exist.
            </p>
            <p className={[s.errorHelp, 'mb-3'].join(' ')}>
              If you are sure it should, search for it.
            </p>
            <div>
              <FormGroup>
                <Input className="input-no-border" type="text" placeholder="Search Pages" />
              </FormGroup>
                <Button className={s.errorBtn}  color="success">
                  Search <i className="fa fa-search text-white ms-2" />
                </Button>
              {/* <Link to="app/extra/search">
              </Link> */}
            </div>
          </div>
          <footer className={s.pageFooter}>
            2019 &copy; Delight Dimonds
          </footer>
        </Container>
      </div>
    );
  }
}

export default ErrorPage;
