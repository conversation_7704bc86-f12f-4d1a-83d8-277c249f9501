/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.errorPage {
  padding-top: 5%;
  height: 100vh;
}

.errorContainer {
  width: 365px;
  text-align: center;
}

.errorBtn {
  padding-left: 35px;
  padding-right: 35px;
}

.errorCode {
  margin: 20px;
  font-size: 80px;
  font-weight: 400;
  color: #6FB0F9;
}
@media (min-width: 768px) {
  .errorCode {
    font-size: 180px;
  }
}

.errorInfo {
  font-size: 20px;
  color: #3c484f;
}

.errorHelp {
  font-size: 14px;
}

.pageFooter {
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  width: 100%;
  font-size: 0.9rem;
  color: #798892;
  text-align: center;
}/*# sourceMappingURL=ErrorPage.module.css.map */