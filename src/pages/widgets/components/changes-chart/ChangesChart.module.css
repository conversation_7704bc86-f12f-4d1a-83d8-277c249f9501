/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.changesChart .chart {
  margin: -15px -20px 0;
  padding: 15px 0 0;
}
.changesChart .chartTitle {
  margin: 20px 0 0;
}
.changesChart .chartValue,
.changesChart .chartValueChange {
  padding: 0 20px;
}
.changesChart .chartValue {
  margin-bottom: 0;
  font-weight: 700;
  font-size: 21px;
  line-height: 1;
  color: #fff;
}
.changesChart .chartValueChange {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875em;
  margin-bottom: 1rem;
}/*# sourceMappingURL=ChangesChart.module.css.map */