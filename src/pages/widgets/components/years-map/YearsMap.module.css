/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.mapChart {
  margin: -15px -20px 0;
}
.mapChart .map {
  position: relative;
  height: 400px;
}
.mapChart .stats {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  margin: 5% 10%;
}

:global .map-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  border-top: 1px solid #bbb;
  background-color: #f8f9fa;
  border-bottom-left-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}
:global .map-controls > .nav-item > .nav-link {
  border-radius: 0;
  padding: 0.7143rem 0;
  color: #0C2236;
}
:global .map-controls > .nav-item > .nav-link:hover {
  background-color: #e9ecef;
  color: #0C2236;
}
:global .map-controls > .nav-item > .nav-link.active, :global .map-controls > .nav-item > .nav-link.active:hover {
  background-color: #fff;
  color: #0C2236;
  font-weight: 700;
}
:global .map-controls > .nav-item:first-child > a {
  border-bottom-left-radius: 0.3rem;
}
:global .map-controls > .nav-item:last-child > a {
  border-bottom-right-radius: 0.3rem;
}/*# sourceMappingURL=YearsMap.module.css.map */