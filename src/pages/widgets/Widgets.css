/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
/*          Post User           */
.post-user {
  position: relative;
}
.post-user::after {
  display: block;
  clear: both;
  content: "";
}
.post-user img {
  border: 3px solid white;
}

/*            Tags              */
.tags {
  padding-left: 0;
  list-style: none;
}
.tags::after {
  display: block;
  clear: both;
  content: "";
}
.tags > li {
  float: left;
}
.tags > li > a {
  padding: 2px 8px;
  font-size: 0.9rem;
  border-radius: 6px;
  border: 1px solid white;
  color: inherit;
  text-decoration: none;
}
.tags > li > a:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
.tags > li > a .fa {
  font-size: 8px;
  margin-right: 3px;
  opacity: 0.8;
}
.tags > li + li > a {
  margin-left: 6px;
}

.widget-top-overflow > img + .tags {
  position: absolute;
  bottom: 0;
  right: 0;
  margin: 20px;
}

/*           Weather             */
.widget-image .forecast {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  margin-bottom: 5px;
  padding-left: 15px;
  padding-right: 15px;
  text-align: center;
}

/*       Chat List Group         */
.widget-chat-list-group {
  padding-top: 0.5rem;
}
.widget-chat-list-group .list-group-item {
  margin-left: 20px;
  margin-right: 20px;
  padding: 0;
  border: 0;
  display: flex;
}
.widget-chat-list-group .list-group-item div {
  width: 100%;
}
.widget-chat-list-group .list-group-item:nth-child(even) {
  margin-left: 75px;
}
.widget-chat-list-group .list-group-item:hover {
  background: none;
}
.widget-chat-list-group .list-group-item + .list-group-item {
  margin-top: 20px;
}
.widget-chat-list-group .list-group-item .thumb,
.widget-chat-list-group .list-group-item .thumb-sm {
  float: left;
  margin-right: 15px;
}
.widget-chat-list-group .list-group-item .time {
  font-size: 0.875rem;
  color: #798892;
}
.widget-chat-list-group .list-group-item .sender {
  margin-top: 5px;
  margin-bottom: 5px;
  font-size: 0.9rem;
  font-weight: 400;
  color: #6FB0F9;
}
.widget-chat-list-group .list-group-item .body {
  font-size: 0.9rem;
  margin-bottom: 0;
}
.widget-chat-list-group .list-group-item.on-right div {
  padding-right: 1rem;
}
.widget-chat-list-group .list-group-item.on-right .thumb,
.widget-chat-list-group .list-group-item.on-right .thumb-sm {
  order: 1;
  margin-left: auto;
  margin-right: 0;
}
.widget-chat-list-group .list-group-item.on-right .time {
  float: left;
}
.widget-chat-list-group .list-group-item.on-right .sender {
  text-align: right;
}/*# sourceMappingURL=Widgets.css.map */