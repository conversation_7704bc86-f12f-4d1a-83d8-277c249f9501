/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.messageAttachments {
  width: 50%;
}
@media screen and (max-width: 576px) {
  .messageAttachments {
    width: 100%;
  }
}

.attachmentsInfo {
  margin: -5px 0 10px;
}
.attachmentsInfo a {
  margin-left: 5px;
}

.attachment {
  max-width: 100%;
}
.attachment img {
  width: 100%;
}
.attachment h5 {
  font-weight: 600;
}

.attachmentButtons {
  margin: -5px 0 15px;
}
.attachmentButtons a {
  margin-left: 10px;
}/*# sourceMappingURL=MessageAttachments.module.css.map */