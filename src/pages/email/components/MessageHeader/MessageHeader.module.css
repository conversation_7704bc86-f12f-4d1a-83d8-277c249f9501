/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.messageHeader {
  width: 100%;
}

.messageHeaderLine {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
  flex-wrap: wrap;
}

.messageFrom {
  display: flex;
  align-items: center;
}
.messageFrom img {
  height: 30px;
  width: 30px;
  margin-right: 5px;
}

.messageFromInfo {
  display: flex;
  flex-direction: column;
  line-height: 1.1;
}

.email {
  color: #868e96;
  font-size: 0.9rem;
  margin-left: 5px;
}

.to {
  color: #868e96;
}

.messageHeaderDate {
  padding: 15px 0;
}
.messageHeaderDate :global .btn-group {
  margin-left: 10px;
}
.messageHeaderDate :global .btn-group button {
  font-size: 12px;
}
.messageHeaderDate :global .btn-group button i {
  margin-right: 3px;
}/*# sourceMappingURL=MessageHeader.module.css.map */