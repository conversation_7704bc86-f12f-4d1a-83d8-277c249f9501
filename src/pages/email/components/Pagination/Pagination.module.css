/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.pagination {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 15px;
}

.paginationText {
  color: #868e96;
  font-size: 0.9rem;
}

.paginationPages {
  border-left: 1px solid #868e96;
  padding-left: 11px;
  margin-left: 10px;
  display: flex;
}
.paginationPages button {
  margin-left: 4px;
}

.button {
  transition: 0.3s;
  padding: 0.45rem 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 400;
  border-radius: 0.2rem;
  color: #888;
  background: #fff;
  border: none;
}
.button:hover {
  background-color: transparent;
}

.buttonActive {
  background: #d6dee5;
}

.buttonDisabled:hover {
  background-color: #fff;
}/*# sourceMappingURL=Pagination.module.css.map */