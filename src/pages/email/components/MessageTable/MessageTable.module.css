/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.messages {
  width: 84%;
  border-radius: 0.2rem;
}
@media screen and (max-width: 1125px) {
  .messages {
    width: 100%;
  }
}
.messages :global .form-check-input {
  margin: 0;
  position: relative;
}
.messages :global .table {
  margin-bottom: 0;
}

.unreadedMessage td {
  font-weight: 600;
}

.messageCheckbox {
  width: 50px;
  padding-right: 0;
}
.messageCheckbox :global .form-check {
  margin-bottom: 0;
}

.messageStar {
  left: 25px;
  margin-left: -10px;
}

.messageStarred {
  color: #EBB834;
}

@media screen and (max-width: 768px) {
  .messageFrom,
.messageClip {
    display: none;
  }
}

.messageDate {
  display: flex;
  justify-content: flex-end;
}
@media screen and (max-width: 768px) {
  .messageDate {
    width: 65px;
  }
}

.backButton {
  margin-bottom: 15px;
}/*# sourceMappingURL=MessageTable.module.css.map */