@import '../../../../styles/app';

.filters {
  width: 16%;
  padding-right: 15px;

  @media screen and (max-width: 1125px) {
    width: 100%;
    padding-right: 0;
  }
}

.mainFilterButtons {
  margin: 15px 0;
}

.button {
  width: 100%;
  padding: 10px 14px !important;
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  font-weight: $font-weight-normal;
  border-radius: 0.2rem;
  color: #868e96;
  background: transparent;

  &:hover {
    background-color: #e5e5e5;
    color: $gray-700;
  }

  & :global .badge {
    width: 20px;
    height: 20px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 10px;
  }
}

.buttonActive {
  background-color: $white;
  color: #555;
  font-weight: 600;
}
