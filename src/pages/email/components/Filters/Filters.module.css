/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.filters {
  width: 16%;
  padding-right: 15px;
}
@media screen and (max-width: 1125px) {
  .filters {
    width: 100%;
    padding-right: 0;
  }
}

.mainFilterButtons {
  margin: 15px 0;
}

.button {
  width: 100%;
  padding: 10px 14px !important;
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  font-weight: 400;
  border-radius: 0.2rem;
  color: #868e96;
  background: transparent;
}
.button:hover {
  background-color: #e5e5e5;
  color: #495057;
}
.button :global .badge {
  width: 20px;
  height: 20px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 10px;
}

.buttonActive {
  background-color: #fff;
  color: #555;
  font-weight: 600;
}/*# sourceMappingURL=Filters.module.css.map */