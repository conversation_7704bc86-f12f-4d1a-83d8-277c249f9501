/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.sidesWrapper {
  display: flex;
  flex-wrap: wrap;
}

.analyticsSide {
  width: 80%;
}
.analyticsSide:last-child {
  width: 20%;
  padding-left: 40px;
}
@media (max-width: 1620px) {
  .analyticsSide {
    width: 100% !important;
  }
  .analyticsSide:last-child {
    padding-left: 0;
  }
}
@media (min-width: 1200px) and (max-width: 1440px) {
  .analyticsSide .lastSideElement {
    max-width: 50%;
  }
}
@media (min-width: 1440px) and (max-width: 1620px) {
  .analyticsSide .lastSideElement {
    max-width: 33.3333333333%;
  }
}

.visitElement h6 {
  font-size: 115%;
}

.sparklineWrapper {
  display: flex;
  justify-content: center;
  min-width: calc(100% - 150px);
}

.notifications :global .la {
  font-size: 1.25rem;
  margin-top: 0.15rem;
}
.notifications p {
  margin-bottom: 1.4rem;
}

:global .legendColorBox div {
  border-radius: 50%;
}/*# sourceMappingURL=Analitycs.module.css.map */