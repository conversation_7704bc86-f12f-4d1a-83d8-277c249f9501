/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.table td,
.table th {
  font-weight: normal;
  align-items: center;
  min-width: 100px;
}
.table th {
  font-size: 0.875rem;
}
.table .headCellPadding {
  padding-left: 10px;
}
.table tbody tr:nth-child(odd) {
  background-color: #f8f9fa;
}

table thead {
  border-style: hidden !important;
}/*# sourceMappingURL=TableContainer.module.css.map */