@charset "UTF-8";
/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
/*           Post Comments         */
.postComments {
  font-size: 0.875rem;
  padding-left: 0;
}
.postComments::after {
  display: block;
  clear: both;
  content: "";
}
.postLinks + .postComments {
  margin-top: 0.5rem;
}
.postComments > li {
  padding: 10px;
  border-top: 1px solid #e7e7e7;
  list-style: none;
}
.postComments > li::after {
  display: block;
  clear: both;
  content: "";
}
.postComments > li:last-child {
  padding-bottom: 0;
}
.postComments p:last-child {
  margin-bottom: 0;
}
.postComments .avatar {
  margin-top: 1px;
}
.postComments .author {
  margin-top: 0;
  margin-bottom: 2px;
  color: #7ca9dd;
}
.postComments .commentBody {
  overflow: auto;
}
.postComments h6.author > small {
  font-size: 11px;
}
.widget > footer .postComments :global {
  margin-left: -20px;
  margin-right: -20px;
}

/*           Post Links            */
.postLinks {
  margin-bottom: 0;
  font-size: 0.875rem;
  padding-left: 0;
}
.postLinks::after {
  display: block;
  clear: both;
  content: "";
}
.postLinks > li {
  float: left;
  list-style: none;
}
.postLinks > li + li::before {
  color: #999;
  content: "●";
  padding: 0 8px;
}
.postLinks > li > a {
  text-decoration: none;
  color: #798892;
}
.postLinks > li > a :hover {
  color: #798892;
}
.postLinks :global .no-separator > li + li {
  margin-left: 12px;
}
.postLinks :global .no-separator > li + li::before {
  content: normal;
}

/*            Time Line            */
.timeline {
  position: relative;
  min-height: 100%;
  list-style: none;
  padding-left: 0;
  margin-bottom: -40px; /* content padding bottom */
  padding-bottom: 80px;
  /* the time line :) */
}
.timeline > li::after {
  display: block;
  clear: both;
  content: "";
}
.timeline > li + li {
  margin-top: 30px;
}
.timeline::before {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 24%;
  width: 8px;
  content: " ";
  margin-left: -4px;
  background-color: #fff;
}
@media (min-width: 992px) {
  .timeline::before {
    left: 50%;
    margin-left: -4px;
  }
}

.event {
  background: #fff;
  border-radius: 0.3rem;
  padding: 20px 20px 0;
  position: relative;
  box-shadow: var(--widget-shadow);
}
.timeline .event {
  float: right;
  width: 68%;
}
.timeline .event::before {
  right: 100%;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  border: 10px solid rgba(0, 0, 0, 0);
  border-right-color: #fff;
  top: 15px;
}
.event .postComments {
  margin-left: -20px;
  margin-right: -20px;
}
.event > footer {
  margin: 20px -20px 0;
  padding: 10px 20px;
  border-bottom-left-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
  background-color: #fafafa;
}
.event > footer::after {
  display: block;
  clear: both;
  content: "";
}
.event > footer :global .thumb {
  margin-left: 10px;
}
@media (min-width: 992px) {
  .timeline .event {
    width: 45%;
  }
  .timeline > li.onLeft .event {
    float: left;
  }
  .timeline > li.onLeft .event::before {
    right: auto;
    left: 100%;
    border-right-color: rgba(0, 0, 0, 0);
    border-left-color: #fff;
  }
}

.timeline .eventTime {
  float: left;
  width: 18%;
  margin-top: 5px;
  text-align: right;
}
.timeline .eventTime > .date {
  display: block;
  font-size: 1.1rem;
}
.timeline .eventTime > .time {
  display: block;
  font-size: 1.25rem;
  font-weight: 400;
}
@media (min-width: 992px) {
  .timeline .eventTime {
    width: 46%;
  }
  .timeline > li.onLeft .eventTime {
    float: right;
    text-align: left;
  }
}

.eventIcon :global .glyphicon {
  top: -2px;
}
.timeline .eventIcon {
  position: absolute;
  left: 24%;
  width: 50px;
  height: 50px;
  line-height: 37px;
  margin-left: -25px;
  background-color: #fff;
  border: 7px solid #fff;
  border-radius: 50%;
  text-align: center;
  box-shadow: var(--widget-shadow);
}
.timeline .eventIcon.eventIconDanger {
  background-color: #FF5574;
  border-color: #ff7991;
}
.timeline .eventIcon.eventIconWarning {
  background-color: #EBB834;
  border-color: #eec354;
}
.timeline .eventIcon.eventIconSuccess {
  background-color: #26CD5F;
  border-color: #3cdb72;
}
.timeline .eventIcon.eventIconInfo {
  background-color: #10CFD0;
  border-color: #17eced;
}
.timeline .eventIcon.eventIconPrimary {
  background-color: #6FB0F9;
  border-color: #91c3fa;
}
.timeline .eventIcon.eventIconDanger, .timeline .eventIcon.eventIconWarning, .timeline .eventIcon.eventIconSuccess, .timeline .eventIcon.eventIconInfo, .timeline .eventIcon.eventIconPrimary {
  color: #fff;
}
@media (min-width: 992px) {
  .timeline .eventIcon {
    left: 50%;
  }
}
.timeline .eventIcon > img {
  width: 36px;
  height: 36px;
  margin-top: -4px;
}

.eventHeading {
  margin: 0 0 2px;
  font-weight: 600;
}
.eventHeading > a {
  text-decoration: none;
  color: #7ca9dd;
}
.eventHeading > small {
  font-weight: 600;
}
.eventHeading > small > a {
  text-decoration: none;
  color: #798892;
}

.eventMap {
  display: block;
  height: 200px;
  margin: 0 -20px -20px;
  overflow: visible !important;
}

.eventImage {
  margin: 0 -20px -20px;
  max-height: 260px;
  overflow: hidden;
}
.eventImage > img {
  max-width: 100%;
}/*# sourceMappingURL=Timeline.module.css.map */