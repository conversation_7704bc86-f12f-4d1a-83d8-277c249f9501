/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.root h4 {
  font-size: 14px;
}
.root :global {
  /* ****  Full Calendar Custom   **** */
}
.root :global .fc-grid th {
  text-transform: uppercase;
}
.root :global .fc .fc-today {
  background-color: rgba(16, 207, 208, 0.2);
}
.root :global .fc-day-grid-event {
  margin: 0;
  padding: 0;
}
.root :global .fc-event {
  border: none;
  font-weight: 400;
  background-color: #e9ecef;
  margin-left: 0 !important;
  color: #0C2236;
}
.root :global a.fc-event {
  height: auto;
  line-height: 1.5;
  width: 100%;
}
.root :global .full-calendar {
  margin-top: 10px;
}
.root :global .calendar-controls .btn {
  font-size: 0.9rem;
}
.root :global .calendar-external-events {
  margin-top: 20px;
}
@media (min-width: 1200px) {
  .root :global .widget-calendar {
    margin-top: -100px;
  }
}

.customExternalEvent {
  margin: 10px 0;
  padding: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  border-radius: 0.3rem;
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: var(--widget-shadow);
}/*# sourceMappingURL=Calendar.module.css.map */