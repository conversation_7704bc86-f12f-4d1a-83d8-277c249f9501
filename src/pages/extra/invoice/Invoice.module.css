/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.root .invoiceLogo {
  max-height: 50px;
}
.root .invoiceBody {
  margin-top: 70px;
}
.root :global .widget {
  padding: 10px 20px;
}/*# sourceMappingURL=Invoice.module.css.map */