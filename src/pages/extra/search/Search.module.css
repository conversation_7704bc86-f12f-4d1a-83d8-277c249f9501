/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.root .searchResultCategories > li > a {
  color: #798892;
  font-weight: 400;
}
.root .searchResultCategories > li > a:hover {
  color: #495057;
  background-color: #c1ccd3;
}
.root .searchResultsCount {
  margin-top: 10px;
}
.root .searchResultItem {
  padding: 20px;
  background-color: #fff;
  border-radius: 0.3rem;
  box-shadow: var(--widget-shadow);
}
.root .searchResultItem:first-of-type {
  overflow: hidden;
}
.root .searchResultItem::after {
  display: block;
  clear: both;
  content: "";
}
.root .searchResultItem .imageLink {
  display: block;
  overflow: hidden;
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}
@media (min-width: 768px) {
  .root .searchResultItem .imageLink {
    display: inline-block;
    margin: -20px 0 -20px -20px;
    float: left;
    width: 200px;
  }
}
@media (max-width: 575.98px) {
  .root .searchResultItem .imageLink {
    max-height: 200px;
  }
}
.root .searchResultItem .image {
  max-width: 100%;
}
.root .searchResultItem .info {
  margin-top: 2px;
  font-size: 0.875rem;
  color: #798892;
}
.root .searchResultItem .description {
  font-size: 0.9rem;
  margin-bottom: -5px;
}
.root .searchResultItem + .searchResultItem {
  margin-top: 20px;
}
.root .searchResultItem .searchResultItemBody {
  height: auto;
}
@media (max-width: 575.98px) {
  .root .searchResultItem .searchResultItemBody {
    margin-top: 10px;
  }
}
@media (min-width: 768px) {
  .root .searchResultItem .searchResultItemBody {
    margin-left: 200px;
  }
}
.root .searchResultItem .searchResultItemHeading {
  font-weight: 400;
}
.root .searchResultItem .searchResultItemHeading > a {
  color: #0C2236;
}
@media (min-width: 768px) {
  .root .searchResultItem .searchResultItemHeading {
    margin: 0;
  }
}/*# sourceMappingURL=Search.module.css.map */