@import '../../../styles/app';

.root {
  :global .tile {
    display: inline-block;
  }
}

.galleryControls {
  display: flex;
  justify-content: space-between;
  margin-bottom: $spacer;
}

.gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-gap: 15px;
}

.picture {
  padding: 3px;
  border-radius: $border-radius;
  background-color: $white;

  > a {
    overflow: hidden;
  }

  :global .figure-img {
    width: 100%;
    transition: $transition-base;
  }

  &:hover {
    :global .figure-img {
      transform: scale(1.1, 1.1);
    }
  }
}

.description {
  padding: ($spacer * 0.85) ($spacer * 0.5);
}
