import React from 'react';
import PropTypes from 'prop-types';
import { with<PERSON>outer } from 'react-router-dom';
import config from '../../../config';
import { connect } from 'react-redux';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'reactstrap';
import Widget from '../../../components/Widget';
import { loginUser, receiveToken, doInit } from '../../../Services/actions/auth';
import jwt from "jsonwebtoken";
// import { push } from 'connected-react-router';
import { Col, Row } from 'react-bootstrap';
import imageurl from '../../../images';

class Login extends React.Component {
    static propTypes = {
        dispatch: PropTypes.func.isRequired,
    };

    static isAuthenticated() {
        const token = localStorage.getItem('token');
        if (!config.isBackend && token) return true;
        if (!token) return;
        const date = new Date().getTime() / 1000;
        const data = jwt.decode(token);
        if (!data) return;
        return date < data.exp;
    }

    constructor(props) {
        super(props);

        this.state = {
            email: '',
            password: '',
            passwordState: false
        };

        this.changeEmail = this.changeEmail.bind(this);
        this.changePassword = this.changePassword.bind(this);
        this.doLogin = this.doLogin.bind(this);
        this.showPassword = this.showPassword.bind(this);
        // this.googleLogin = this.googleLogin.bind(this);
        // this.microsoftLogin = this.microsoftLogin.bind(this);
        // this.changeEmail = this.changeEmail.bind(this);
        // this.changePassword = this.changePassword.bind(this);
    }

    changeEmail(event) {
        this.setState({ ...this.setState, email: event.target.value });
    }

    changePassword(event) {
        this.setState({ ...this.setState, password: event.target.value });
    }

    doLogin(e) {
        e.preventDefault();
        this.props.dispatch(loginUser({ email: this.state.email, password: this.state.password }));
    }


    componentDidMount() {
        const params = new URLSearchParams(this.props.location.search);
        const token = params.get('token');
        if (token) {
            this.props.dispatch(receiveToken(token));
            this.props.dispatch(doInit());
        }
    }

    showPassword() {
        if (this.state.passwordState) {
            this.setState({ ...this.setState, passwordState: false });
        } else {
            this.setState({ ...this.setState, passwordState: true });
        }
    }
    render() {
        return (
            <div className="auth-page">
                <Row className='h-100'>
                    <Col lg={5} className="auth-left pt-5 position-relative">
                        <div className='auth-logo'>
                            <img src={imageurl.logo} className="img-fluid" alt="Logo" />
                        </div>
                        <img src={imageurl.authBg} className="img-fluid auth-bg" alt="Background " />
                    </Col>
                    <Col lg={7} className="bg-white d-flex">
                        <Widget className="widget-auth mx-auto my-auto h-auto" title={<h2 className="mt-0">Welcome</h2>}>
                            <form className="mt" onSubmit={this.doLogin}>
                                {
                                    this.props.errorMessage && (
                                        <Alert className="alert-sm" color="danger">
                                            {this.props.errorMessage.error.error_message}
                                        </Alert>
                                    )
                                }
                                <div className="form-group">
                                    <input className="form-control no-border" value={this.state.email} onChange={this.changeEmail} type="email" required name="email" placeholder="Email" />
                                </div>
                                <div className="form-group pass-field">
                                    <input className="form-control no-border" value={this.state.password} onChange={this.changePassword} type={this.state.passwordState ? "text" : "password"} required name="password" placeholder="Password" />
                                    <button type='button' className='pass-hide_show' onClick={this.showPassword} > <i className={`fa-regular ${this.state.passwordState ? "fa-eye-slash" : "fa-regular fa-eye"}`}></i></button>
                                </div>
                                {/* <Link className="d-block text-right mb-3 mt-1 fs-sm" to="forgot">Forgot password?</Link> */}
                                <Button type="submit" color="primary" className="auth-btn mb-3" size="sm">{this.props.isFetching ? 'Loading...' : 'Login'}</Button>
                            </form>
                            {/* <p className="widget-auth-info">
                            Don't have an account? Sign up now!
                        </p>
                        <Link className="d-block text-center" to="register">Create an Account</Link> */}
                        </Widget>
                    </Col>
                </Row>
            </div>
        );
    }
}

function mapStateToProps(state) {
    return {
        isFetching: state.auth.isFetching,
        isAuthenticated: state.auth.isAuthenticated,
        errorMessage: state.auth.errorMessage,
    };
}
export default withRouter(connect(mapStateToProps)(Login));