/**
 * Customized version of bootstrap using variables from _variables.scss.
 * This file is loaded via separate loader thus allowing to use original bootstrap classes (e.g. .btn-default) through out the app.
 */
/*
 * Typography
 * ======================================================================== */
/**
 * Custom application mixins available through out the app
 */
/* Chat Scrollbar */
.profileContactContainer {
  margin-top: -75px;
}

.profileContacts {
  padding-left: 0;
  list-style: none;
  display: inline-block;
  text-align: left;
}
.profileContacts > li {
  margin-bottom: 0.5rem;
}
.profileContacts > li > a {
  color: #2871b3;
  text-decoration: none;
}
.profileContacts > li > a:hover, .profileContacts > li > a:focus {
  color: #0C2236;
}

.profileAvatar {
  border: 3px solid #fff;
}

.profileStat {
  border-left: none !important;
  padding-right: 0 !important;
}

.profileStatValue {
  font-size: 28px;
  font-weight: 300 !important;
  margin-bottom: 0;
}

.event {
  background: #fff;
  border-radius: 0.3rem;
  padding: 20px 20px 0;
  position: relative;
  margin-bottom: 1rem;
  box-shadow: var(--widget-shadow);
}

.eventTitle {
  margin-bottom: 2px;
  font-weight: 600;
}
.eventTitle a {
  text-decoration: none;
  color: #7ca9dd;
}
.eventTitle small > a {
  color: #798892;
}

.eventAvatar {
  float: left;
  margin-right: 1rem;
}

.eventAvatar > img {
  width: 34px;
}

.eventBody {
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.eventFooter {
  background-color: #f8f9fa;
  margin: 20px -20px 0;
  padding: 10px 20px;
}

.eventTimestamp {
  color: #798892;
}

.btn-toolbar {
  display: flex;
  justify-content: space-between !important;
}/*# sourceMappingURL=Profile.module.css.map */