{"name": "delight-diamonds-admin", "version": "6.0.0", "private": true, "scripts": {"build": " node scripts/build.js", "start": "node scripts/start.js", "start:backend": "cross-env REACT_APP_BACKEND=true node scripts/start.js", "test": "node scripts/test.js"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "eslintConfig": {"extends": "react-app"}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx}"], "moduleFileExtensions": ["web.js", "js", "json", "web.jsx", "jsx", "node"], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "resolver": "jest-pnp-resolver", "setupFiles": ["react-app-polyfill/jsdom"], "testEnvironment": "jsdom", "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx}", "<rootDir>/src/**/?(*.)(spec|test).{js,jsx}"], "testURL": "http://localhost:3000", "transform": {"^.+\\.(js|jsx)$": "<rootDir>/node_modules/babel-jest", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx)$", "^.+\\.module\\.(css|sass|scss)$"]}, "dependencies": {"@amcharts/amcharts4": "^4.10.25", "@amcharts/amcharts4-geodata": "^4.1.23", "@fullcalendar/core": "5.11.0", "@fullcalendar/daygrid": "5.11.0", "@fullcalendar/interaction": "5.11.0", "@fullcalendar/list": "5.11.0", "@fullcalendar/react": "5.11.1", "@fullcalendar/timegrid": "5.11.0", "@progress/kendo-data-query": "^1.6.0", "@progress/kendo-drawing": "^1.17.4", "@progress/kendo-licensing": "^1.3.0", "@progress/kendo-react-animation": "^5.11.0", "@progress/kendo-react-buttons": "^5.11.0", "@progress/kendo-react-data-tools": "^5.11.0", "@progress/kendo-react-dateinputs": "^5.11.0", "@progress/kendo-react-dropdowns": "^5.11.0", "@progress/kendo-react-grid": "^5.11.0", "@progress/kendo-react-inputs": "^5.11.0", "@progress/kendo-react-intl": "^5.11.0", "@progress/kendo-react-progressbars": "^5.12.0", "@progress/kendo-react-treeview": "^5.11.0", "@progress/kendo-svg-icons": "^1.4.0", "animate.css": "4.1.1", "apexcharts": "^3.35.3", "awesome-bootstrap-checkbox": "https://github.com/flatlogic/awesome-bootstrap-checkbox#bump-to-bootstrap5", "axios": "^0.27.2", "bootstrap": "^5.1.3", "bootstrap-slider": "^10.6.2", "bootstrap_calendar": "https://github.com/xero/bootstrap_calendar.git#1.0.1", "chroma-js": "^2.4.2", "ckeditor4": "^4.21.0", "ckeditor4-react": "^4.2.0", "classnames": "^2.3.2", "connected-react-router": "6.9.2", "cross-env": "^7.0.3", "draft-js": "^0.11.7", "echarts": "^4.9.0", "echarts-for-react": "^2.0.16", "file-saver": "^2.0.5", "filesize": "^6.4.0", "flot": "^0.8.3", "flot.dashes": "https://github.com/cquartier/flot.dashes.git", "font-awesome": "4.7.0", "formik": "1.5.8", "formsy-react": "0.19.5", "glyphicons-halflings": "^1.9.1", "highcharts": "^7.2.2", "highcharts-react-official": "^3.1.0", "history": "^4.10.1", "jasny-bootstrap": "^3.1.3", "jsonwebtoken": "^8.5.1", "line-awesome": "^1.3.0", "md5": "^2.3.0", "messenger": "git+https://github.com/HubSpot/messenger.git#v1.4.2", "moment": "^2.29.3", "nvd3": "1.8.6", "qs": "^6.10.5", "query-string": "^6.14.1", "rc-color-picker": "^1.2.6", "rc-hammerjs": "0.6.10", "rc-slider": "^8.7.1", "react": "^17.0.2", "react-animate-height": "^2.1.2", "react-animated-number": "^0.4.4", "react-apexcharts": "^1.4.0", "react-app-polyfill": "^0.2.2", "react-autosize-textarea": "^5.0.1", "react-bootstrap": "^1.6.5", "react-bootstrap-table": "4.3.1", "react-bootstrap-table-next": "^4.0.3", "react-bootstrap-table2-editor": "^1.4.0", "react-bootstrap-table2-filter": "^1.3.3", "react-bootstrap-table2-overlay": "^2.0.0", "react-bootstrap-table2-paginator": "^2.1.2", "react-bootstrap-table2-toolkit": "^2.1.3", "react-datetime": "^2.16.3", "react-dev-utils": "^6.1.1", "react-dom": "^17.0.2", "react-dropzone": "^6.2.4", "react-google-maps": "^9.4.5", "react-images": "^0.5.19", "react-joyride": "^2.4.0", "react-maskedinput": "^4.0.1", "react-mde": "2.3.4", "react-multi-select-component": "^4.3.4", "react-redux": "^7.2.8", "react-router": "5.3.3", "react-router-dom": "5.3.3", "react-router-hash-link": "^1.2.2", "react-scrollspy": "^3.4.3", "react-select": "^3.2.0", "react-shuffle": "2.1.0", "react-slick": "^0.29.0", "react-sortable-hoc": "^1.11.0", "react-sortable-tree": "^2.8.0", "react-sortablejs": "1.5.1", "react-sparklines": "^1.7.0", "react-syntax-highlighter": "^10.3.5", "react-table": "6.11.5", "react-tagsinput": "^3.19.0", "react-toastify": "^5.5.0", "react-transition-group": "^2.9.0", "react-trend": "^1.2.5", "reactstrap": "9.1.1", "redux": "^4.2.0", "redux-thunk": "^2.4.1", "reselect": "^4.1.6", "rickshaw": "1.7.1", "sass": "^1.84.0", "serve": "^14.2.0", "showdown": "1.9.1", "skycons": "^1.0.0", "sortablejs": "1.15.0", "styled-components": "^5.3.5", "text-editor-react": "^1.0.3", "toastr": "^2.1.4", "uuid": "^3.4.0", "xlsx": "^0.18.5", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.18.5", "@babel/plugin-proposal-class-properties": "7.17.12", "@babel/plugin-proposal-optional-chaining": "^7.17.12", "@babel/preset-env": "^7.20.2", "@svgr/webpack": "4.3.3", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.1.0", "babel-jest": "24.9.0", "babel-loader": "^8.2.5", "babel-plugin-named-asset-import": "1.0.0-next.fb6e6f70", "babel-preset-react-app": "9.1.2", "bfj": "6.1.2", "bundle-loader": "0.5.6", "case-sensitive-paths-webpack-plugin": "2.4.0", "chalk": "2.4.2", "css-loader": "2.1.1", "dotenv": "8.6.0", "dotenv-expand": "5.1.0", "eslint": "5.16.0", "eslint-config-react-app": "4.0.1", "eslint-loader": "2.2.1", "eslint-plugin-flowtype": "3.13.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-plugin-react": "7.30.0", "eslint-plugin-react-hooks": "1.7.0", "expose-loader": "0.7.5", "file-loader": "3.0.1", "fs-extra": "7.0.1", "html-webpack-plugin": "4.5.2", "identity-obj-proxy": "3.0.0", "imports-loader": "0.8.0", "jest": "24.9.0", "jest-pnp-resolver": "1.2.2", "jest-resolve": "24.9.0", "lodash": "4.17.21", "mini-css-extract-plugin": "0.12.0", "optimize-css-assets-webpack-plugin": "5.0.8", "pnp-webpack-plugin": "1.7.0", "postcss-flexbugs-fixes": "4.2.1", "postcss-loader": "3.0.0", "postcss-preset-env": "6.7.1", "postcss-safe-parser": "4.0.2", "resolve": "1.22.0", "sass-loader": "7.3.1", "style-loader": "0.23.1", "terser-webpack-plugin": "1.4.5", "url-loader": "1.1.2", "webpack": "^4.46.0", "webpack-dev-server": "3.11.3", "webpack-manifest-plugin": "2.2.0", "workbox-webpack-plugin": "4.3.1"}, "resolutions": {"react": "^17.0.2", "react-dom": "^17.0.2"}, "engines": {"node": ">=16"}}