const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  // Basic Diamond Information
  stone_id: {
    type: String,
    required: [true, 'Stone ID is required'],
    unique: true,
    trim: true
  },
  cert_no: {
    type: String,
    required: [true, 'Certificate number is required'],
    trim: true
  },
  cert_type: {
    type: String,
    required: [true, 'Certificate type is required'],
    enum: ['GIA', 'IGI', 'SSEF', 'GÜBELIN', 'AGS', 'EGL', 'GCAL', 'Other']
  },
  cert_url: {
    type: String,
    trim: true
  },
  
  // Media
  image: {
    type: String, // URL to image
    trim: true
  },
  video: {
    type: String, // URL to video
    trim: true
  },
  
  // Diamond Type and Basic Properties
  diamond_type: {
    type: String,
    required: [true, 'Diamond type is required'],
    enum: ['natural', 'lab_grown']
  },
  carat: {
    type: Number,
    required: [true, 'Carat is required'],
    min: [0.01, 'Carat must be at least 0.01']
  },
  
  // Shape and Color
  shape_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Shape',
    required: [true, 'Shape is required']
  },
  color_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Color',
    required: [true, 'Color is required']
  },
  colors_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FancyColor'
  },
  overtone_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Overtone'
  },
  intensity_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Intensity'
  },
  
  // Quality Grades
  clarity_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Clarity',
    required: [true, 'Clarity is required']
  },
  cut_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Cut'
  },
  polish_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Polish'
  },
  symmetry_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Symmetry'
  },
  fluorescence_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Fluorescence'
  },
  fluorescence_color_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FluorescenceColor'
  },
  
  // Pricing
  rapo_rate: {
    type: Number,
    min: 0
  },
  discount: {
    type: Number,
    min: 0,
    max: 100
  },
  rate: {
    type: Number,
    required: [true, 'Rate is required'],
    min: 0
  },
  amount: {
    type: Number,
    required: [true, 'Amount is required'],
    min: 0
  },
  
  // Measurements
  table_per: {
    type: Number,
    min: 0,
    max: 100
  },
  depth_per: {
    type: Number,
    min: 0,
    max: 100
  },
  length: {
    type: Number,
    min: 0
  },
  width: {
    type: Number,
    min: 0
  },
  height: {
    type: Number,
    min: 0
  },
  ratio: {
    type: Number,
    min: 0
  },
  
  // Location
  bgm_id: {
    type: String,
    trim: true
  },
  city: {
    type: String,
    trim: true
  },
  country: {
    type: String,
    trim: true
  },
  
  // Additional Properties
  pair: {
    type: String,
    trim: true
  },
  h_a: {
    type: String,
    trim: true
  },
  growth_type: {
    type: String,
    enum: ['HPHT', 'CVD', 'Natural', '']
  },
  eye_clean: {
    type: Boolean,
    default: false
  },
  milky: {
    type: String,
    enum: ['None', 'Faint', 'Medium', 'Strong', '']
  },
  shade: {
    type: String,
    enum: ['None', 'Brown', 'Green', 'Gray', '']
  },
  
  // Advanced Measurements
  crown_angle: {
    type: Number,
    min: 0
  },
  crown_height: {
    type: Number,
    min: 0
  },
  pavilion_angle: {
    type: Number,
    min: 0
  },
  pavilion_height: {
    type: Number,
    min: 0
  },
  
  // Inclusion Details
  white_table: {
    type: String,
    trim: true
  },
  white_side: {
    type: String,
    trim: true
  },
  table_black: {
    type: String,
    trim: true
  },
  side_black: {
    type: String,
    trim: true
  },
  table_open: {
    type: String,
    trim: true
  },
  side_open: {
    type: String,
    trim: true
  },
  
  // Status and Ownership
  status: {
    type: String,
    enum: ['available', 'hold', 'sold', 'reserved', 'inactive'],
    default: 'available'
  },
  vendor_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor'
  },
  
  // Metadata
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for better performance
productSchema.index({ stone_id: 1 });
productSchema.index({ cert_no: 1 });
productSchema.index({ diamond_type: 1 });
productSchema.index({ shape_id: 1 });
productSchema.index({ color_id: 1 });
productSchema.index({ clarity_id: 1 });
productSchema.index({ carat: 1 });
productSchema.index({ status: 1 });
productSchema.index({ vendor_id: 1 });
productSchema.index({ createdAt: -1 });

// Compound indexes
productSchema.index({ diamond_type: 1, status: 1 });
productSchema.index({ shape_id: 1, color_id: 1, clarity_id: 1 });

module.exports = mongoose.model('Product', productSchema);
