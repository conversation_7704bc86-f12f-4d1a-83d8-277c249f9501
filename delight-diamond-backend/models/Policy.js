const mongoose = require('mongoose');

const policySchema = new mongoose.Schema({
  // Basic Information
  title: {
    type: String,
    required: [true, 'Policy title is required'],
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  content: {
    type: String,
    required: [true, 'Policy content is required']
  },
  
  // Policy Type and Category
  policy_type: {
    type: String,
    enum: ['terms_of_service', 'privacy_policy', 'return_policy', 'shipping_policy', 'payment_policy', 'general', 'vendor_policy', 'merchant_policy'],
    required: [true, 'Policy type is required']
  },
  category: {
    type: String,
    enum: ['legal', 'business', 'operational', 'technical', 'financial'],
    default: 'business'
  },
  
  // Applicability
  applies_to: {
    type: [String],
    enum: ['all', 'vendors', 'merchants', 'customers', 'admins'],
    default: ['all']
  },
  
  // Status and Versioning
  status: {
    type: String,
    enum: ['draft', 'active', 'inactive', 'archived'],
    default: 'draft'
  },
  version: {
    type: String,
    default: '1.0'
  },
  previous_version_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Policy'
  },
  
  // Dates
  effective_date: {
    type: Date,
    required: [true, 'Effective date is required']
  },
  expiry_date: {
    type: Date
  },
  last_reviewed_date: {
    type: Date
  },
  next_review_date: {
    type: Date
  },
  
  // Approval and Review
  approved_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approval_date: {
    type: Date
  },
  reviewed_by: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    review_date: {
      type: Date,
      default: Date.now
    },
    comments: {
      type: String,
      trim: true
    }
  }],
  
  // Content Management
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  keywords: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  
  // Display and Access
  is_public: {
    type: Boolean,
    default: false
  },
  display_order: {
    type: Number,
    default: 0
  },
  requires_acceptance: {
    type: Boolean,
    default: false
  },
  
  // Attachments
  attachments: [{
    filename: {
      type: String,
      required: true
    },
    original_name: {
      type: String,
      required: true
    },
    file_path: {
      type: String,
      required: true
    },
    file_size: {
      type: Number
    },
    mime_type: {
      type: String
    },
    uploaded_at: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Metadata
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
policySchema.index({ policy_type: 1 });
policySchema.index({ category: 1 });
policySchema.index({ status: 1 });
policySchema.index({ effective_date: 1 });
policySchema.index({ applies_to: 1 });
policySchema.index({ tags: 1 });
policySchema.index({ keywords: 1 });
policySchema.index({ is_public: 1 });
policySchema.index({ created_by: 1 });

// Compound indexes
policySchema.index({ policy_type: 1, status: 1 });
policySchema.index({ status: 1, effective_date: 1 });
policySchema.index({ applies_to: 1, status: 1 });

// Text index for search
policySchema.index({
  title: 'text',
  description: 'text',
  content: 'text',
  tags: 'text',
  keywords: 'text'
});

module.exports = mongoose.model('Policy', policySchema);
