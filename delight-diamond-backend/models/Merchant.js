const mongoose = require('mongoose');

const merchantSchema = new mongoose.Schema({
  // Basic Information
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    lowercase: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  
  // Personal Information
  first_name: {
    type: String,
    required: [true, 'First name is required'],
    trim: true
  },
  last_name: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true
  },
  
  // Company Information
  company: {
    type: String,
    required: [true, 'Company name is required'],
    trim: true
  },
  website: {
    type: String,
    trim: true
  },
  job_title: {
    type: String,
    trim: true
  },
  
  // Contact Information
  mobile: {
    type: String,
    required: [true, 'Mobile number is required'],
    trim: true
  },
  other_phone: {
    type: String,
    trim: true
  },
  
  // Business Details
  business_type: [{
    type: Number, // Array of business type IDs
    required: true
  }],
  buying_group: [{
    type: Number, // Array of buying group IDs
    required: true
  }],
  group_title: {
    type: String,
    trim: true
  },
  about: {
    type: String,
    trim: true
  },
  
  // Address Information
  address: {
    type: String,
    required: [true, 'Address is required'],
    trim: true
  },
  city: {
    type: String,
    required: [true, 'City is required'],
    trim: true
  },
  state: {
    type: String,
    required: [true, 'State is required'],
    trim: true
  },
  pincode: {
    type: String,
    required: [true, 'Pincode is required'],
    trim: true
  },
  country_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Country',
    required: [true, 'Country is required']
  },
  
  // Status and Verification
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending', 'suspended'],
    default: 'pending'
  },
  verified: {
    type: Boolean,
    default: false
  },
  
  // Verification
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: {
    type: String
  },
  emailVerificationExpires: {
    type: Date
  },
  
  // Business Metrics
  total_orders: {
    type: Number,
    default: 0
  },
  total_purchases: {
    type: Number,
    default: 0
  },
  total_amount_spent: {
    type: Number,
    default: 0
  },
  
  // Preferences
  preferred_shapes: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Shape'
  }],
  preferred_colors: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Color'
  }],
  preferred_clarity: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Clarity'
  }],
  carat_range: {
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 10
    }
  },
  budget_range: {
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 1000000
    }
  },
  
  // Metadata
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  last_activity: {
    type: Date
  }
}, {
  timestamps: true
});

// Indexes
merchantSchema.index({ email: 1 });
merchantSchema.index({ username: 1 });
merchantSchema.index({ status: 1 });
merchantSchema.index({ country_id: 1 });
merchantSchema.index({ business_type: 1 });
merchantSchema.index({ buying_group: 1 });

// Generate email verification token
merchantSchema.methods.generateEmailVerificationToken = function() {
  const crypto = require('crypto');
  const verificationToken = crypto.randomBytes(32).toString('hex');
  
  this.emailVerificationToken = crypto.createHash('sha256').update(verificationToken).digest('hex');
  this.emailVerificationExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours
  
  return verificationToken;
};

// Transform output
merchantSchema.methods.toJSON = function() {
  const merchant = this.toObject();
  delete merchant.emailVerificationToken;
  delete merchant.emailVerificationExpires;
  return merchant;
};

module.exports = mongoose.model('Merchant', merchantSchema);
