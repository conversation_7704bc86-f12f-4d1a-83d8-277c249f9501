const mongoose = require('mongoose');

const invalidStoneSchema = new mongoose.Schema({
  // Original Data
  raw_data: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, 'Raw data is required']
  },
  
  // Identification
  stone_id: {
    type: String,
    trim: true
  },
  cert_no: {
    type: String,
    trim: true
  },
  vendor_ref: {
    type: String,
    trim: true
  },
  
  // Upload Information
  upload_history_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'UploadHistory',
    required: [true, 'Upload history reference is required']
  },
  row_number: {
    type: Number,
    required: [true, 'Row number is required']
  },
  
  // Diamond Type
  diamond_type: {
    type: String,
    enum: ['natural', 'lab_grown', 'star_melee'],
    required: [true, 'Diamond type is required']
  },
  
  // Validation Errors
  validation_errors: [{
    field: {
      type: String,
      required: true,
      trim: true
    },
    error_type: {
      type: String,
      enum: ['required', 'invalid_format', 'invalid_value', 'reference_not_found', 'duplicate', 'out_of_range'],
      required: true
    },
    error_message: {
      type: String,
      required: true,
      trim: true
    },
    expected_value: {
      type: String,
      trim: true
    },
    actual_value: {
      type: String,
      trim: true
    },
    suggestions: [{
      type: String,
      trim: true
    }]
  }],
  
  // Processing Errors
  processing_errors: [{
    error_code: {
      type: String,
      trim: true
    },
    error_message: {
      type: String,
      required: true,
      trim: true
    },
    stack_trace: {
      type: String,
      trim: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Status and Resolution
  status: {
    type: String,
    enum: ['pending', 'reviewing', 'corrected', 'rejected', 'ignored'],
    default: 'pending'
  },
  resolution_status: {
    type: String,
    enum: ['unresolved', 'auto_corrected', 'manually_corrected', 'rejected', 'duplicate_removed'],
    default: 'unresolved'
  },
  
  // Correction Attempts
  correction_attempts: [{
    attempted_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    attempted_at: {
      type: Date,
      default: Date.now
    },
    corrected_data: {
      type: mongoose.Schema.Types.Mixed
    },
    correction_notes: {
      type: String,
      trim: true
    },
    success: {
      type: Boolean,
      default: false
    },
    new_errors: [{
      field: String,
      error_message: String
    }]
  }],
  
  // Auto-correction
  auto_correction_attempted: {
    type: Boolean,
    default: false
  },
  auto_correction_successful: {
    type: Boolean,
    default: false
  },
  auto_corrected_fields: [{
    field: {
      type: String,
      trim: true
    },
    original_value: {
      type: String,
      trim: true
    },
    corrected_value: {
      type: String,
      trim: true
    },
    correction_method: {
      type: String,
      trim: true
    }
  }],
  
  // Duplicate Detection
  potential_duplicates: [{
    record_id: {
      type: mongoose.Schema.Types.ObjectId
    },
    record_type: {
      type: String,
      enum: ['Product', 'StarMelee']
    },
    similarity_score: {
      type: Number,
      min: 0,
      max: 100
    },
    matching_fields: [{
      type: String,
      trim: true
    }]
  }],
  
  // Reference Validation
  missing_references: [{
    field: {
      type: String,
      trim: true
    },
    reference_type: {
      type: String,
      trim: true
    },
    searched_value: {
      type: String,
      trim: true
    },
    suggestions: [{
      id: mongoose.Schema.Types.ObjectId,
      name: String,
      similarity_score: Number
    }]
  }],
  
  // Priority and Categorization
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  category: {
    type: String,
    enum: ['data_quality', 'missing_reference', 'duplicate', 'format_error', 'business_rule_violation'],
    required: true
  },
  
  // Assignment and Review
  assigned_to: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  assigned_date: {
    type: Date
  },
  reviewed_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewed_date: {
    type: Date
  },
  review_notes: {
    type: String,
    trim: true
  },
  
  // Final Resolution
  final_action: {
    type: String,
    enum: ['', 'imported', 'rejected', 'merged', 'manual_entry_required'],
    default: ''
  },
  final_record_id: {
    type: mongoose.Schema.Types.ObjectId
  },
  final_record_type: {
    type: String,
    enum: ['Product', 'StarMelee']
  },
  resolution_notes: {
    type: String,
    trim: true
  },
  
  // Metadata
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
invalidStoneSchema.index({ upload_history_id: 1 });
invalidStoneSchema.index({ diamond_type: 1 });
invalidStoneSchema.index({ status: 1 });
invalidStoneSchema.index({ resolution_status: 1 });
invalidStoneSchema.index({ priority: 1 });
invalidStoneSchema.index({ category: 1 });
invalidStoneSchema.index({ assigned_to: 1 });
invalidStoneSchema.index({ created_by: 1 });
invalidStoneSchema.index({ stone_id: 1 });
invalidStoneSchema.index({ cert_no: 1 });

// Compound indexes
invalidStoneSchema.index({ status: 1, priority: 1 });
invalidStoneSchema.index({ diamond_type: 1, status: 1 });
invalidStoneSchema.index({ assigned_to: 1, status: 1 });
invalidStoneSchema.index({ upload_history_id: 1, row_number: 1 });

module.exports = mongoose.model('InvalidStone', invalidStoneSchema);
