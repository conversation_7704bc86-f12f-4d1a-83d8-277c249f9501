const mongoose = require('mongoose');

const appointmentSchema = new mongoose.Schema({
  // Basic Information
  title: {
    type: String,
    required: [true, 'Appointment title is required'],
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  
  // Participants
  customer_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required']
  },
  vendor_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor'
  },
  merchant_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Merchant'
  },
  assigned_to: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User' // Admin user who handles the appointment
  },
  
  // Timing
  appointment_date: {
    type: Date,
    required: [true, 'Appointment date is required']
  },
  appointment_time: {
    type: String,
    required: [true, 'Appointment time is required']
  },
  duration: {
    type: Number, // Duration in minutes
    default: 60
  },
  timezone: {
    type: String,
    default: 'UTC'
  },
  
  // Location/Method
  meeting_type: {
    type: String,
    enum: ['in_person', 'video_call', 'phone_call', 'online'],
    default: 'in_person'
  },
  location: {
    type: String,
    trim: true
  },
  meeting_link: {
    type: String,
    trim: true
  },
  
  // Status and Priority
  status: {
    type: String,
    enum: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'rescheduled'],
    default: 'scheduled'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  
  // Related Products
  related_products: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  }],
  
  // Communication
  customer_email: {
    type: String,
    trim: true,
    lowercase: true
  },
  customer_phone: {
    type: String,
    trim: true
  },
  
  // Notes and Follow-up
  notes: {
    type: String,
    trim: true
  },
  internal_notes: {
    type: String,
    trim: true
  },
  follow_up_required: {
    type: Boolean,
    default: false
  },
  follow_up_date: {
    type: Date
  },
  
  // Outcome
  outcome: {
    type: String,
    enum: ['', 'sale_made', 'follow_up_scheduled', 'no_interest', 'needs_more_info', 'price_negotiation'],
    default: ''
  },
  sale_amount: {
    type: Number,
    default: 0
  },
  
  // Reminders
  reminder_sent: {
    type: Boolean,
    default: false
  },
  reminder_date: {
    type: Date
  },
  
  // Metadata
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  cancelled_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  cancellation_reason: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Indexes
appointmentSchema.index({ customer_id: 1 });
appointmentSchema.index({ vendor_id: 1 });
appointmentSchema.index({ merchant_id: 1 });
appointmentSchema.index({ assigned_to: 1 });
appointmentSchema.index({ appointment_date: 1 });
appointmentSchema.index({ status: 1 });
appointmentSchema.index({ priority: 1 });
appointmentSchema.index({ meeting_type: 1 });
appointmentSchema.index({ created_by: 1 });

// Compound indexes
appointmentSchema.index({ appointment_date: 1, status: 1 });
appointmentSchema.index({ customer_id: 1, status: 1 });
appointmentSchema.index({ assigned_to: 1, appointment_date: 1 });

module.exports = mongoose.model('Appointment', appointmentSchema);
