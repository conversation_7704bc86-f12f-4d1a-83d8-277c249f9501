const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const vendorSchema = new mongoose.Schema({
  // Basic Information
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    lowercase: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters']
  },
  
  // Personal Information
  first_name: {
    type: String,
    required: [true, 'First name is required'],
    trim: true
  },
  last_name: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true
  },
  
  // Company Information
  company: {
    type: String,
    required: [true, 'Company name is required'],
    trim: true
  },
  website: {
    type: String,
    trim: true
  },
  job_title: {
    type: String,
    trim: true
  },
  
  // Contact Information
  mobile: {
    type: String,
    required: [true, 'Mobile number is required'],
    trim: true
  },
  other_phone: {
    type: String,
    trim: true
  },
  
  // Business Details
  business_type: [{
    type: Number, // Array of business type IDs
    required: true
  }],
  buying_group: [{
    type: Number, // Array of buying group IDs
    required: true
  }],
  group_title: {
    type: String,
    trim: true
  },
  about: {
    type: String,
    trim: true
  },
  
  // Address Information
  address: {
    type: String,
    required: [true, 'Address is required'],
    trim: true
  },
  city: {
    type: String,
    required: [true, 'City is required'],
    trim: true
  },
  state: {
    type: String,
    required: [true, 'State is required'],
    trim: true
  },
  pincode: {
    type: String,
    required: [true, 'Pincode is required'],
    trim: true
  },
  country_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Country',
    required: [true, 'Country is required']
  },
  
  // Status and Verification
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending', 'suspended'],
    default: 'pending'
  },
  verified: {
    type: Boolean,
    default: false
  },
  
  // Verification
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: {
    type: String
  },
  emailVerificationExpires: {
    type: Date
  },
  
  // Password Reset
  passwordResetToken: {
    type: String
  },
  passwordResetExpires: {
    type: Date
  },
  
  // Business Metrics
  total_products: {
    type: Number,
    default: 0
  },
  active_products: {
    type: Number,
    default: 0
  },
  total_sales: {
    type: Number,
    default: 0
  },
  
  // Metadata
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  last_login: {
    type: Date
  }
}, {
  timestamps: true
});

// Indexes
vendorSchema.index({ email: 1 });
vendorSchema.index({ username: 1 });
vendorSchema.index({ status: 1 });
vendorSchema.index({ country_id: 1 });
vendorSchema.index({ business_type: 1 });
vendorSchema.index({ buying_group: 1 });

// Hash password before saving
vendorSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
vendorSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Generate password reset token
vendorSchema.methods.generatePasswordResetToken = function() {
  const crypto = require('crypto');
  const resetToken = crypto.randomBytes(32).toString('hex');
  
  this.passwordResetToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  this.passwordResetExpires = Date.now() + 10 * 60 * 1000; // 10 minutes
  
  return resetToken;
};

// Transform output
vendorSchema.methods.toJSON = function() {
  const vendor = this.toObject();
  delete vendor.password;
  delete vendor.passwordResetToken;
  delete vendor.passwordResetExpires;
  delete vendor.emailVerificationToken;
  delete vendor.emailVerificationExpires;
  return vendor;
};

module.exports = mongoose.model('Vendor', vendorSchema);
