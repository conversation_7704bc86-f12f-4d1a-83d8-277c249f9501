const mongoose = require('mongoose');

const demandSchema = new mongoose.Schema({
  // Customer Information
  customer_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Customer is required']
  },
  customer_name: {
    type: String,
    required: [true, 'Customer name is required'],
    trim: true
  },
  customer_email: {
    type: String,
    required: [true, 'Customer email is required'],
    trim: true,
    lowercase: true
  },
  customer_phone: {
    type: String,
    trim: true
  },
  
  // Demand Details
  title: {
    type: String,
    required: [true, 'Demand title is required'],
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  
  // Diamond Specifications
  shape_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Shape'
  },
  color_preferences: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Color'
  }],
  clarity_preferences: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Clarity'
  }],
  
  // Size and Budget
  carat_range: {
    min: {
      type: Number,
      min: 0
    },
    max: {
      type: Number,
      min: 0
    }
  },
  budget_range: {
    min: {
      type: Number,
      min: 0
    },
    max: {
      type: Number,
      min: 0
    }
  },
  
  // Additional Preferences
  cut_preferences: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Cut'
  }],
  fluorescence_preferences: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Fluorescence'
  }],
  certification_preferences: [{
    type: String,
    enum: ['GIA', 'IGI', 'SSEF', 'GÜBELIN', 'AGS', 'EGL', 'GCAL', 'Other']
  }],
  
  // Timeline
  required_by: {
    type: Date
  },
  urgency: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  
  // Status and Processing
  status: {
    type: String,
    enum: ['open', 'in_progress', 'matched', 'fulfilled', 'cancelled', 'expired'],
    default: 'open'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  
  // Assignment
  assigned_to: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  assigned_date: {
    type: Date
  },
  
  // Matching and Fulfillment
  matched_products: [{
    product_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product'
    },
    match_score: {
      type: Number,
      min: 0,
      max: 100
    },
    suggested_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    suggested_date: {
      type: Date,
      default: Date.now
    },
    customer_response: {
      type: String,
      enum: ['', 'interested', 'not_interested', 'needs_more_info'],
      default: ''
    }
  }],
  
  fulfilled_with: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  },
  fulfillment_date: {
    type: Date
  },
  
  // Communication
  notes: {
    type: String,
    trim: true
  },
  internal_notes: {
    type: String,
    trim: true
  },
  
  // Follow-up
  follow_up_required: {
    type: Boolean,
    default: false
  },
  follow_up_date: {
    type: Date
  },
  last_contact_date: {
    type: Date
  },
  
  // Source
  source: {
    type: String,
    enum: ['website', 'phone', 'email', 'walk_in', 'referral', 'social_media', 'other'],
    default: 'website'
  },
  source_details: {
    type: String,
    trim: true
  },
  
  // Metadata
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
demandSchema.index({ customer_id: 1 });
demandSchema.index({ status: 1 });
demandSchema.index({ priority: 1 });
demandSchema.index({ urgency: 1 });
demandSchema.index({ assigned_to: 1 });
demandSchema.index({ shape_id: 1 });
demandSchema.index({ required_by: 1 });
demandSchema.index({ created_by: 1 });

// Compound indexes
demandSchema.index({ status: 1, priority: 1 });
demandSchema.index({ assigned_to: 1, status: 1 });
demandSchema.index({ customer_id: 1, status: 1 });

// Text index for search
demandSchema.index({
  title: 'text',
  description: 'text',
  customer_name: 'text',
  notes: 'text'
});

module.exports = mongoose.model('Demand', demandSchema);
