const mongoose = require('mongoose');

// Base schema for master data
const baseMasterSchema = {
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true
  },
  code: {
    type: String,
    trim: true,
    uppercase: true
  },
  description: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  sort_order: {
    type: Number,
    default: 0
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
};

const timestampOptions = { timestamps: true };

// Shape Model
const shapeSchema = new mongoose.Schema({
  ...baseMasterSchema,
  is_fancy: {
    type: Boolean,
    default: false
  }
}, timestampOptions);

// Color Model
const colorSchema = new mongoose.Schema({
  ...baseMasterSchema,
  grade_order: {
    type: Number,
    default: 0
  }
}, timestampOptions);

// Clarity Model
const claritySchema = new mongoose.Schema({
  ...baseMasterSchema,
  grade_order: {
    type: Number,
    default: 0
  }
}, timestampOptions);

// Size Model
const sizeSchema = new mongoose.Schema({
  ...baseMasterSchema,
  min_carat: {
    type: Number,
    default: 0
  },
  max_carat: {
    type: Number,
    default: 10
  }
}, timestampOptions);

// Cut Model
const cutSchema = new mongoose.Schema({
  ...baseMasterSchema,
  grade_order: {
    type: Number,
    default: 0
  }
}, timestampOptions);

// Polish Model
const polishSchema = new mongoose.Schema({
  ...baseMasterSchema,
  grade_order: {
    type: Number,
    default: 0
  }
}, timestampOptions);

// Symmetry Model
const symmetrySchema = new mongoose.Schema({
  ...baseMasterSchema,
  grade_order: {
    type: Number,
    default: 0
  }
}, timestampOptions);

// Fluorescence Model
const fluorescenceSchema = new mongoose.Schema({
  ...baseMasterSchema,
  intensity_level: {
    type: String,
    enum: ['None', 'Faint', 'Medium', 'Strong', 'Very Strong'],
    default: 'None'
  }
}, timestampOptions);

// Fluorescence Color Model
const fluorescenceColorSchema = new mongoose.Schema({
  ...baseMasterSchema,
  hex_code: {
    type: String,
    trim: true
  }
}, timestampOptions);

// Fancy Color Model
const fancyColorSchema = new mongoose.Schema({
  ...baseMasterSchema,
  color_family: {
    type: String,
    enum: ['Yellow', 'Brown', 'Orange', 'Pink', 'Red', 'Purple', 'Violet', 'Blue', 'Green', 'Gray', 'Black', 'White'],
    required: true
  },
  hex_code: {
    type: String,
    trim: true
  }
}, timestampOptions);

// Finish Model
const finishSchema = new mongoose.Schema({
  ...baseMasterSchema,
  finish_type: {
    type: String,
    enum: ['Polish', 'Symmetry', 'Both'],
    default: 'Both'
  }
}, timestampOptions);

// Intensity Model (for fancy colors)
const intensitySchema = new mongoose.Schema({
  ...baseMasterSchema,
  intensity_level: {
    type: Number,
    min: 1,
    max: 10,
    default: 1
  }
}, timestampOptions);

// Overtone Model (for fancy colors)
const overtoneSchema = new mongoose.Schema({
  ...baseMasterSchema,
  primary_color: {
    type: String,
    trim: true
  }
}, timestampOptions);

// Country Model
const countrySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Country name is required'],
    trim: true
  },
  code: {
    type: String,
    required: [true, 'Country code is required'],
    trim: true,
    uppercase: true,
    unique: true
  },
  iso_code: {
    type: String,
    trim: true,
    uppercase: true
  },
  phone_code: {
    type: String,
    trim: true
  },
  currency: {
    type: String,
    trim: true,
    uppercase: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  }
}, timestampOptions);

// Business Type Model
const businessTypeSchema = new mongoose.Schema({
  ...baseMasterSchema
}, timestampOptions);

// Buying Group Model
const buyingGroupSchema = new mongoose.Schema({
  ...baseMasterSchema
}, timestampOptions);

// Add indexes for better performance
const addIndexes = (schema) => {
  schema.index({ name: 1 });
  schema.index({ code: 1 });
  schema.index({ status: 1 });
  schema.index({ sort_order: 1 });
};

addIndexes(shapeSchema);
addIndexes(colorSchema);
addIndexes(claritySchema);
addIndexes(sizeSchema);
addIndexes(cutSchema);
addIndexes(polishSchema);
addIndexes(symmetrySchema);
addIndexes(fluorescenceSchema);
addIndexes(fluorescenceColorSchema);
addIndexes(fancyColorSchema);
addIndexes(finishSchema);
addIndexes(intensitySchema);
addIndexes(overtoneSchema);
addIndexes(businessTypeSchema);
addIndexes(buyingGroupSchema);

countrySchema.index({ name: 1 });
countrySchema.index({ code: 1 });
countrySchema.index({ status: 1 });

// Export all models
module.exports = {
  Shape: mongoose.model('Shape', shapeSchema),
  Color: mongoose.model('Color', colorSchema),
  Clarity: mongoose.model('Clarity', claritySchema),
  Size: mongoose.model('Size', sizeSchema),
  Cut: mongoose.model('Cut', cutSchema),
  Polish: mongoose.model('Polish', polishSchema),
  Symmetry: mongoose.model('Symmetry', symmetrySchema),
  Fluorescence: mongoose.model('Fluorescence', fluorescenceSchema),
  FluorescenceColor: mongoose.model('FluorescenceColor', fluorescenceColorSchema),
  FancyColor: mongoose.model('FancyColor', fancyColorSchema),
  Finish: mongoose.model('Finish', finishSchema),
  Intensity: mongoose.model('Intensity', intensitySchema),
  Overtone: mongoose.model('Overtone', overtoneSchema),
  Country: mongoose.model('Country', countrySchema),
  BusinessType: mongoose.model('BusinessType', businessTypeSchema),
  BuyingGroup: mongoose.model('BuyingGroup', buyingGroupSchema)
};
