const mongoose = require('mongoose');

const cartSchema = new mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  product_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, 'Product ID is required']
  },
  quantity: {
    type: Number,
    default: 1,
    min: [1, 'Quantity must be at least 1']
  },
  price_at_time: {
    type: Number,
    required: [true, 'Price at time of adding is required']
  },
  status: {
    type: String,
    enum: ['active', 'hold', 'confirmed', 'removed'],
    default: 'active'
  },
  hold_until: {
    type: Date
  },
  notes: {
    type: String,
    trim: true
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
cartSchema.index({ user_id: 1 });
cartSchema.index({ product_id: 1 });
cartSchema.index({ status: 1 });
cartSchema.index({ user_id: 1, status: 1 });

// Ensure unique product per user in active cart
cartSchema.index({ user_id: 1, product_id: 1, status: 1 }, { unique: true });

module.exports = mongoose.model('Cart', cartSchema);
