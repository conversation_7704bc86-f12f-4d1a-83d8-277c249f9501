const mongoose = require('mongoose');

const uploadHistorySchema = new mongoose.Schema({
  // File Information
  filename: {
    type: String,
    required: [true, 'Filename is required'],
    trim: true
  },
  original_filename: {
    type: String,
    required: [true, 'Original filename is required'],
    trim: true
  },
  file_path: {
    type: String,
    required: [true, 'File path is required'],
    trim: true
  },
  file_size: {
    type: Number,
    required: [true, 'File size is required'],
    min: 0
  },
  file_type: {
    type: String,
    required: [true, 'File type is required'],
    enum: ['csv', 'xlsx', 'xls', 'json', 'xml'],
    trim: true
  },
  mime_type: {
    type: String,
    required: [true, 'MIME type is required'],
    trim: true
  },
  
  // Upload Details
  upload_type: {
    type: String,
    required: [true, 'Upload type is required'],
    enum: ['products', 'star_melee', 'vendors', 'merchants', 'users', 'master_data'],
    trim: true
  },
  upload_method: {
    type: String,
    enum: ['manual', 'api', 'scheduled', 'bulk'],
    default: 'manual'
  },
  
  // Processing Status
  status: {
    type: String,
    enum: ['uploaded', 'processing', 'completed', 'failed', 'cancelled'],
    default: 'uploaded'
  },
  processing_started_at: {
    type: Date
  },
  processing_completed_at: {
    type: Date
  },
  
  // Results
  total_records: {
    type: Number,
    default: 0
  },
  processed_records: {
    type: Number,
    default: 0
  },
  successful_records: {
    type: Number,
    default: 0
  },
  failed_records: {
    type: Number,
    default: 0
  },
  skipped_records: {
    type: Number,
    default: 0
  },
  
  // Error Handling
  errors: [{
    row_number: {
      type: Number
    },
    field: {
      type: String,
      trim: true
    },
    error_message: {
      type: String,
      trim: true
    },
    error_code: {
      type: String,
      trim: true
    },
    raw_data: {
      type: mongoose.Schema.Types.Mixed
    }
  }],
  
  // Validation Results
  validation_errors: [{
    row_number: {
      type: Number
    },
    field: {
      type: String,
      trim: true
    },
    expected_value: {
      type: String,
      trim: true
    },
    actual_value: {
      type: String,
      trim: true
    },
    error_message: {
      type: String,
      trim: true
    }
  }],
  
  // Processing Configuration
  mapping_config: {
    type: mongoose.Schema.Types.Mixed // Field mapping configuration
  },
  processing_options: {
    skip_duplicates: {
      type: Boolean,
      default: true
    },
    update_existing: {
      type: Boolean,
      default: false
    },
    validate_references: {
      type: Boolean,
      default: true
    },
    batch_size: {
      type: Number,
      default: 100
    }
  },
  
  // Summary
  summary: {
    type: String,
    trim: true
  },
  processing_time: {
    type: Number, // Processing time in milliseconds
    default: 0
  },
  
  // Related Data
  created_records: [{
    record_id: {
      type: mongoose.Schema.Types.ObjectId
    },
    record_type: {
      type: String,
      trim: true
    },
    external_id: {
      type: String,
      trim: true
    }
  }],
  
  updated_records: [{
    record_id: {
      type: mongoose.Schema.Types.ObjectId
    },
    record_type: {
      type: String,
      trim: true
    },
    external_id: {
      type: String,
      trim: true
    },
    changes: {
      type: mongoose.Schema.Types.Mixed
    }
  }],
  
  // Backup and Recovery
  backup_created: {
    type: Boolean,
    default: false
  },
  backup_path: {
    type: String,
    trim: true
  },
  can_rollback: {
    type: Boolean,
    default: false
  },
  rollback_completed: {
    type: Boolean,
    default: false
  },
  rollback_date: {
    type: Date
  },
  
  // Metadata
  uploaded_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  processed_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  notes: {
    type: String,
    trim: true
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }]
}, {
  timestamps: true
});

// Indexes
uploadHistorySchema.index({ uploaded_by: 1 });
uploadHistorySchema.index({ upload_type: 1 });
uploadHistorySchema.index({ status: 1 });
uploadHistorySchema.index({ file_type: 1 });
uploadHistorySchema.index({ createdAt: -1 });
uploadHistorySchema.index({ processing_completed_at: -1 });

// Compound indexes
uploadHistorySchema.index({ uploaded_by: 1, status: 1 });
uploadHistorySchema.index({ upload_type: 1, status: 1 });
uploadHistorySchema.index({ status: 1, createdAt: -1 });

// Text index for search
uploadHistorySchema.index({
  filename: 'text',
  original_filename: 'text',
  summary: 'text',
  notes: 'text',
  tags: 'text'
});

// Calculate processing time before saving
uploadHistorySchema.pre('save', function(next) {
  if (this.processing_started_at && this.processing_completed_at) {
    this.processing_time = this.processing_completed_at.getTime() - this.processing_started_at.getTime();
  }
  next();
});

module.exports = mongoose.model('UploadHistory', uploadHistorySchema);
