const mongoose = require('mongoose');

const starMeleeSchema = new mongoose.Schema({
  // Basic Information
  stone_id: {
    type: String,
    required: [true, 'Stone ID is required'],
    unique: true,
    trim: true
  },
  lot_number: {
    type: String,
    trim: true
  },
  
  // Diamond Properties
  shape_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Shape',
    required: [true, 'Shape is required']
  },
  color_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Color',
    required: [true, 'Color is required']
  },
  clarity_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Clarity',
    required: [true, 'Clarity is required']
  },
  
  // Size and Weight
  size_range: {
    type: String,
    required: [true, 'Size range is required'],
    trim: true
  },
  total_carat: {
    type: Number,
    required: [true, 'Total carat is required'],
    min: [0.01, 'Total carat must be at least 0.01']
  },
  pieces_count: {
    type: Number,
    required: [true, 'Pieces count is required'],
    min: [1, 'Pieces count must be at least 1']
  },
  average_carat: {
    type: Number,
    min: 0
  },
  
  // Quality
  cut_grade: {
    type: String,
    enum: ['Excellent', 'Very Good', 'Good', 'Fair', 'Poor', ''],
    default: ''
  },
  polish: {
    type: String,
    enum: ['Excellent', 'Very Good', 'Good', 'Fair', 'Poor', ''],
    default: ''
  },
  symmetry: {
    type: String,
    enum: ['Excellent', 'Very Good', 'Good', 'Fair', 'Poor', ''],
    default: ''
  },
  
  // Pricing
  rate_per_carat: {
    type: Number,
    required: [true, 'Rate per carat is required'],
    min: 0
  },
  total_amount: {
    type: Number,
    required: [true, 'Total amount is required'],
    min: 0
  },
  discount: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  final_amount: {
    type: Number,
    min: 0
  },
  
  // Certification
  certification: {
    type: String,
    enum: ['GIA', 'IGI', 'SSEF', 'GÜBELIN', 'AGS', 'EGL', 'GCAL', 'None', 'Other'],
    default: 'None'
  },
  cert_number: {
    type: String,
    trim: true
  },
  cert_url: {
    type: String,
    trim: true
  },
  
  // Media
  images: [{
    type: String // URLs to images
  }],
  videos: [{
    type: String // URLs to videos
  }],
  
  // Location and Origin
  origin: {
    type: String,
    trim: true
  },
  location: {
    type: String,
    trim: true
  },
  
  // Status and Availability
  status: {
    type: String,
    enum: ['available', 'hold', 'sold', 'reserved', 'inquiry', 'inactive'],
    default: 'available'
  },
  availability: {
    type: String,
    enum: ['in_stock', 'on_memo', 'sold', 'reserved'],
    default: 'in_stock'
  },
  
  // Vendor Information
  vendor_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor'
  },
  vendor_ref: {
    type: String,
    trim: true
  },
  
  // Additional Properties
  fluorescence: {
    type: String,
    enum: ['None', 'Faint', 'Medium', 'Strong', 'Very Strong'],
    default: 'None'
  },
  treatment: {
    type: String,
    enum: ['None', 'HPHT', 'Irradiated', 'Laser Drilled', 'Other'],
    default: 'None'
  },
  
  // Measurements (for the lot)
  measurements: {
    min_size: {
      type: Number,
      min: 0
    },
    max_size: {
      type: Number,
      min: 0
    },
    average_size: {
      type: Number,
      min: 0
    }
  },
  
  // Inquiry Information
  inquiry_status: {
    type: String,
    enum: ['none', 'pending', 'responded', 'closed'],
    default: 'none'
  },
  inquiries: [{
    customer_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    inquiry_date: {
      type: Date,
      default: Date.now
    },
    message: {
      type: String,
      trim: true
    },
    response: {
      type: String,
      trim: true
    },
    response_date: {
      type: Date
    },
    status: {
      type: String,
      enum: ['pending', 'responded', 'closed'],
      default: 'pending'
    }
  }],
  
  // Notes
  description: {
    type: String,
    trim: true
  },
  internal_notes: {
    type: String,
    trim: true
  },
  
  // Metadata
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
starMeleeSchema.index({ stone_id: 1 });
starMeleeSchema.index({ shape_id: 1 });
starMeleeSchema.index({ color_id: 1 });
starMeleeSchema.index({ clarity_id: 1 });
starMeleeSchema.index({ status: 1 });
starMeleeSchema.index({ availability: 1 });
starMeleeSchema.index({ vendor_id: 1 });
starMeleeSchema.index({ total_carat: 1 });
starMeleeSchema.index({ rate_per_carat: 1 });
starMeleeSchema.index({ inquiry_status: 1 });

// Compound indexes
starMeleeSchema.index({ shape_id: 1, color_id: 1, clarity_id: 1 });
starMeleeSchema.index({ status: 1, availability: 1 });
starMeleeSchema.index({ vendor_id: 1, status: 1 });

// Calculate final amount before saving
starMeleeSchema.pre('save', function(next) {
  if (this.total_amount && this.discount) {
    this.final_amount = this.total_amount * (1 - this.discount / 100);
  } else {
    this.final_amount = this.total_amount;
  }
  
  if (this.total_carat && this.pieces_count) {
    this.average_carat = this.total_carat / this.pieces_count;
  }
  
  next();
});

module.exports = mongoose.model('StarMelee', starMeleeSchema);
