const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const productRoutes = require('./routes/products');
const vendorRoutes = require('./routes/vendors');
const merchantRoutes = require('./routes/merchants');
const masterRoutes = require('./routes/master');
const cartRoutes = require('./routes/cart');
const appointmentRoutes = require('./routes/appointments');
const policyRoutes = require('./routes/policies');
const demandRoutes = require('./routes/demand');
const starMeleeRoutes = require('./routes/starMelee');
const uploadHistoryRoutes = require('./routes/uploadHistory');
const invalidStoneRoutes = require('./routes/invalidStones');

// Import middleware
const errorHandler = require('./middleware/errorHandler');
const { authenticateToken } = require('./middleware/auth');

const app = express();

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/admin/', limiter);

// CORS configuration
const corsOptions = {
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static('uploads'));

// Database connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/delight_diamond', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('Connected to MongoDB');
})
.catch((error) => {
  console.error('MongoDB connection error:', error);
  process.exit(1);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API Routes
app.use('/admin/auth', authRoutes);
app.use('/admin/users', authenticateToken, userRoutes);
app.use('/admin/product', authenticateToken, productRoutes);
app.use('/admin/vendor', authenticateToken, vendorRoutes);
app.use('/admin/merchant', authenticateToken, merchantRoutes);
app.use('/admin', authenticateToken, masterRoutes);
app.use('/admin/cart', authenticateToken, cartRoutes);
app.use('/admin/appointment', authenticateToken, appointmentRoutes);
app.use('/admin/policy', authenticateToken, policyRoutes);
app.use('/admin/demand', authenticateToken, demandRoutes);
app.use('/admin/star-melee', authenticateToken, starMeleeRoutes);
app.use('/admin/upload-history', authenticateToken, uploadHistoryRoutes);
app.use('/admin/invalid-stones', authenticateToken, invalidStoneRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Error handling middleware
app.use(errorHandler);

const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
});

module.exports = app;
