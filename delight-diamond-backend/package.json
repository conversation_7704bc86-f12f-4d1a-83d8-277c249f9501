{"name": "delight-diamond-backend", "version": "1.0.0", "description": "Backend API for Delight Diamond Admin System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seed.js", "migrate": "node scripts/migrate.js"}, "keywords": ["diamond", "admin", "api", "nodejs", "express"], "author": "Delight Diamond", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "morgan": "^1.10.0", "compression": "^1.7.4", "moment": "^2.29.4", "nodemailer": "^6.9.4", "crypto": "^1.0.1", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}