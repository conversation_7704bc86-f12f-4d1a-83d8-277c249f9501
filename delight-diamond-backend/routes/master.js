const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { requireAdmin } = require('../middleware/auth');
const {
  Shape, Color, Clarity, Size, Cut, Polish, Symmetry,
  Fluorescence, FluorescenceColor, FancyColor, Finish,
  Intensity, Overtone, Country, BusinessType, BuyingGroup
} = require('../models/MasterData');

const router = express.Router();

// Generic function to handle CRUD operations for master data
const createMasterDataRoutes = (modelName, Model, routePath) => {
  // Get all records
  router.get(`/${routePath}`, [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('search').optional().trim(),
    query('status').optional().isIn(['active', 'inactive'])
  ], async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 50;
      const skip = (page - 1) * limit;
      const search = req.query.search;
      const status = req.query.status;

      let query = {};
      
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { code: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }
      
      if (status) {
        query.status = status;
      }

      const records = await Model.find(query)
        .populate('created_by', 'fullname username')
        .populate('updated_by', 'fullname username')
        .sort({ sort_order: 1, name: 1 })
        .skip(skip)
        .limit(limit);

      const total = await Model.countDocuments(query);

      res.json({
        success: true,
        data: {
          result: records,
          pagination: {
            current_page: page,
            per_page: limit,
            total: total,
            total_pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error(`Get ${modelName} error:`, error);
      res.status(500).json({
        success: false,
        error: {
          error_message: `Server error fetching ${modelName.toLowerCase()}`
        }
      });
    }
  });

  // Get record by ID
  router.get(`/${routePath}/:id`, async (req, res) => {
    try {
      const record = await Model.findById(req.params.id)
        .populate('created_by', 'fullname username')
        .populate('updated_by', 'fullname username');

      if (!record) {
        return res.status(404).json({
          success: false,
          error: {
            error_message: `${modelName} not found`
          }
        });
      }

      res.json({
        success: true,
        data: {
          result: record
        }
      });
    } catch (error) {
      console.error(`Get ${modelName} error:`, error);
      res.status(500).json({
        success: false,
        error: {
          error_message: `Server error fetching ${modelName.toLowerCase()}`
        }
      });
    }
  });

  // Create new record (Admin only)
  router.post(`/${routePath}`, [
    requireAdmin,
    body('name').trim().isLength({ min: 1 }),
    body('code').optional().trim(),
    body('description').optional().trim(),
    body('status').optional().isIn(['active', 'inactive']),
    body('sort_order').optional().isInt({ min: 0 })
  ], async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: 'Validation failed',
            details: errors.array()
          }
        });
      }

      const recordData = {
        ...req.body,
        created_by: req.user._id
      };

      const record = new Model(recordData);
      await record.save();

      const populatedRecord = await Model.findById(record._id)
        .populate('created_by', 'fullname username');

      res.status(201).json({
        success: true,
        data: {
          message: `${modelName} created successfully`,
          result: populatedRecord
        }
      });
    } catch (error) {
      console.error(`Create ${modelName} error:`, error);
      if (error.code === 11000) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: `${modelName} with this name or code already exists`
          }
        });
      }
      res.status(500).json({
        success: false,
        error: {
          error_message: `Server error creating ${modelName.toLowerCase()}`
        }
      });
    }
  });

  // Update record (Admin only)
  router.put(`/${routePath}/:id`, [
    requireAdmin,
    body('name').optional().trim().isLength({ min: 1 }),
    body('code').optional().trim(),
    body('description').optional().trim(),
    body('status').optional().isIn(['active', 'inactive']),
    body('sort_order').optional().isInt({ min: 0 })
  ], async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: 'Validation failed',
            details: errors.array()
          }
        });
      }

      const recordId = req.params.id;
      const updateData = {
        ...req.body,
        updated_by: req.user._id
      };

      const record = await Model.findByIdAndUpdate(
        recordId,
        updateData,
        { new: true, runValidators: true }
      ).populate('created_by', 'fullname username')
       .populate('updated_by', 'fullname username');

      if (!record) {
        return res.status(404).json({
          success: false,
          error: {
            error_message: `${modelName} not found`
          }
        });
      }

      res.json({
        success: true,
        data: {
          message: `${modelName} updated successfully`,
          result: record
        }
      });
    } catch (error) {
      console.error(`Update ${modelName} error:`, error);
      if (error.code === 11000) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: `${modelName} with this name or code already exists`
          }
        });
      }
      res.status(500).json({
        success: false,
        error: {
          error_message: `Server error updating ${modelName.toLowerCase()}`
        }
      });
    }
  });

  // Delete record (Admin only)
  router.delete(`/${routePath}/:id`, requireAdmin, async (req, res) => {
    try {
      const record = await Model.findByIdAndDelete(req.params.id);
      if (!record) {
        return res.status(404).json({
          success: false,
          error: {
            error_message: `${modelName} not found`
          }
        });
      }

      res.json({
        success: true,
        data: {
          message: `${modelName} deleted successfully`
        }
      });
    } catch (error) {
      console.error(`Delete ${modelName} error:`, error);
      res.status(500).json({
        success: false,
        error: {
          error_message: `Server error deleting ${modelName.toLowerCase()}`
        }
      });
    }
  });
};

// Create routes for all master data models
createMasterDataRoutes('Shape', Shape, 'shapes');
createMasterDataRoutes('Color', Color, 'colors');
createMasterDataRoutes('Clarity', Clarity, 'clarity');
createMasterDataRoutes('Size', Size, 'sizes');
createMasterDataRoutes('Cut', Cut, 'cuts');
createMasterDataRoutes('Polish', Polish, 'polish');
createMasterDataRoutes('Symmetry', Symmetry, 'symmetry');
createMasterDataRoutes('Fluorescence', Fluorescence, 'fluorescence');
createMasterDataRoutes('FluorescenceColor', FluorescenceColor, 'fluorescence-colors');
createMasterDataRoutes('FancyColor', FancyColor, 'fancy-colors');
createMasterDataRoutes('Finish', Finish, 'finish');
createMasterDataRoutes('Intensity', Intensity, 'intensity');
createMasterDataRoutes('Overtone', Overtone, 'overtone');
createMasterDataRoutes('Country', Country, 'countries');
createMasterDataRoutes('BusinessType', BusinessType, 'business-types');
createMasterDataRoutes('BuyingGroup', BuyingGroup, 'buying-groups');

module.exports = router;
