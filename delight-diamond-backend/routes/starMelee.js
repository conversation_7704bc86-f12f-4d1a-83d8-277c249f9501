const express = require('express');
const { body, validationResult, query } = require('express-validator');
const StarMelee = require('../models/StarMelee');
const upload = require('../middleware/upload');

const router = express.Router();

// Get all star melee
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('search').optional().trim(),
  query('status').optional().isIn(['available', 'hold', 'sold', 'reserved', 'inquiry', 'inactive']),
  query('shape_id').optional().isMongoId(),
  query('color_id').optional().isMongoId(),
  query('clarity_id').optional().isMongoId()
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    let query = {};
    
    if (req.query.search) {
      query.$or = [
        { stone_id: { $regex: req.query.search, $options: 'i' } },
        { lot_number: { $regex: req.query.search, $options: 'i' } }
      ];
    }
    
    if (req.query.status) query.status = req.query.status;
    if (req.query.shape_id) query.shape_id = req.query.shape_id;
    if (req.query.color_id) query.color_id = req.query.color_id;
    if (req.query.clarity_id) query.clarity_id = req.query.clarity_id;

    const starMelees = await StarMelee.find(query)
      .populate('shape_id', 'name code')
      .populate('color_id', 'name code')
      .populate('clarity_id', 'name code')
      .populate('vendor_id', 'company first_name last_name')
      .populate('created_by', 'fullname username')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await StarMelee.countDocuments(query);

    res.json({
      success: true,
      data: {
        result: starMelees,
        pagination: {
          current_page: page,
          per_page: limit,
          total: total,
          total_pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get star melee error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching star melee'
      }
    });
  }
});

// Get star melee by ID
router.get('/:id', async (req, res) => {
  try {
    const starMelee = await StarMelee.findById(req.params.id)
      .populate('shape_id', 'name code')
      .populate('color_id', 'name code')
      .populate('clarity_id', 'name code')
      .populate('vendor_id', 'company first_name last_name email mobile')
      .populate('created_by', 'fullname username')
      .populate('inquiries.customer_id', 'fullname email mobile');

    if (!starMelee) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Star melee not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        result: starMelee
      }
    });
  } catch (error) {
    console.error('Get star melee error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching star melee'
      }
    });
  }
});

// Create new star melee
router.post('/', [
  upload.multiple('images', 5),
  body('stone_id').trim().notEmpty(),
  body('shape_id').isMongoId(),
  body('color_id').isMongoId(),
  body('clarity_id').isMongoId(),
  body('size_range').trim().notEmpty(),
  body('total_carat').isFloat({ min: 0.01 }),
  body('pieces_count').isInt({ min: 1 }),
  body('rate_per_carat').isFloat({ min: 0 }),
  body('total_amount').isFloat({ min: 0 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    // Check if stone_id already exists
    const existingStarMelee = await StarMelee.findOne({ stone_id: req.body.stone_id });
    if (existingStarMelee) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Star melee with this stone ID already exists'
        }
      });
    }

    const starMeleeData = { ...req.body };
    
    // Add image URLs if uploaded
    if (req.files && req.files.length > 0) {
      starMeleeData.images = req.files.map(file => `/uploads/images/${file.filename}`);
    }

    starMeleeData.created_by = req.user._id;

    const starMelee = new StarMelee(starMeleeData);
    await starMelee.save();

    const populatedStarMelee = await StarMelee.findById(starMelee._id)
      .populate('shape_id', 'name code')
      .populate('color_id', 'name code')
      .populate('clarity_id', 'name code');

    res.status(201).json({
      success: true,
      data: {
        message: 'Star melee created successfully',
        result: populatedStarMelee
      }
    });
  } catch (error) {
    console.error('Create star melee error:', error);
    if (error.code === 11000) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Star melee with this stone ID already exists'
        }
      });
    }
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error creating star melee'
      }
    });
  }
});

// Update star melee
router.put('/:id', [
  upload.multiple('images', 5),
  body('stone_id').optional().trim().notEmpty(),
  body('shape_id').optional().isMongoId(),
  body('color_id').optional().isMongoId(),
  body('clarity_id').optional().isMongoId(),
  body('size_range').optional().trim().notEmpty(),
  body('total_carat').optional().isFloat({ min: 0.01 }),
  body('pieces_count').optional().isInt({ min: 1 }),
  body('rate_per_carat').optional().isFloat({ min: 0 }),
  body('total_amount').optional().isFloat({ min: 0 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const starMeleeId = req.params.id;
    const updateData = { ...req.body };

    // Check if star melee exists
    const starMelee = await StarMelee.findById(starMeleeId);
    if (!starMelee) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Star melee not found'
        }
      });
    }

    // Check for duplicate stone_id if being updated
    if (updateData.stone_id && updateData.stone_id !== starMelee.stone_id) {
      const existingStarMelee = await StarMelee.findOne({
        stone_id: updateData.stone_id,
        _id: { $ne: starMeleeId }
      });
      if (existingStarMelee) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: 'Star melee with this stone ID already exists'
          }
        });
      }
    }

    // Add new image URLs if uploaded
    if (req.files && req.files.length > 0) {
      const newImages = req.files.map(file => `/uploads/images/${file.filename}`);
      updateData.images = [...(starMelee.images || []), ...newImages];
    }

    updateData.updated_by = req.user._id;

    const updatedStarMelee = await StarMelee.findByIdAndUpdate(
      starMeleeId,
      updateData,
      { new: true, runValidators: true }
    ).populate('shape_id', 'name code')
     .populate('color_id', 'name code')
     .populate('clarity_id', 'name code');

    res.json({
      success: true,
      data: {
        message: 'Star melee updated successfully',
        result: updatedStarMelee
      }
    });
  } catch (error) {
    console.error('Update star melee error:', error);
    if (error.code === 11000) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Star melee with this stone ID already exists'
        }
      });
    }
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating star melee'
      }
    });
  }
});

// Delete star melee
router.delete('/:id', async (req, res) => {
  try {
    const starMelee = await StarMelee.findByIdAndDelete(req.params.id);
    if (!starMelee) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Star melee not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Star melee deleted successfully'
      }
    });
  } catch (error) {
    console.error('Delete star melee error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error deleting star melee'
      }
    });
  }
});

module.exports = router;
