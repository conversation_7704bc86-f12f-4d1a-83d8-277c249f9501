const express = require('express');
const { body, validationResult, query } = require('express-validator');
const InvalidStone = require('../models/InvalidStone');
const { requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get all invalid stones
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('diamond_type').optional().isIn(['natural', 'lab_grown', 'star_melee']),
  query('status').optional().isIn(['pending', 'reviewing', 'corrected', 'rejected', 'ignored']),
  query('priority').optional().isIn(['low', 'medium', 'high', 'critical']),
  query('category').optional().isIn(['data_quality', 'missing_reference', 'duplicate', 'format_error', 'business_rule_violation'])
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let query = {};
    
    if (req.query.diamond_type) query.diamond_type = req.query.diamond_type;
    if (req.query.status) query.status = req.query.status;
    if (req.query.priority) query.priority = req.query.priority;
    if (req.query.category) query.category = req.query.category;

    const invalidStones = await InvalidStone.find(query)
      .populate('upload_history_id', 'filename original_filename upload_type')
      .populate('assigned_to', 'fullname username')
      .populate('reviewed_by', 'fullname username')
      .populate('created_by', 'fullname username')
      .sort({ priority: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await InvalidStone.countDocuments(query);

    res.json({
      success: true,
      data: {
        result: invalidStones,
        pagination: {
          current_page: page,
          per_page: limit,
          total: total,
          total_pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get invalid stones error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching invalid stones'
      }
    });
  }
});

// Get invalid stone by ID
router.get('/:id', async (req, res) => {
  try {
    const invalidStone = await InvalidStone.findById(req.params.id)
      .populate('upload_history_id', 'filename original_filename upload_type')
      .populate('assigned_to', 'fullname username')
      .populate('reviewed_by', 'fullname username')
      .populate('created_by', 'fullname username')
      .populate('correction_attempts.attempted_by', 'fullname username');

    if (!invalidStone) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Invalid stone record not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        result: invalidStone
      }
    });
  } catch (error) {
    console.error('Get invalid stone error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching invalid stone'
      }
    });
  }
});

// Update invalid stone status (Admin only)
router.patch('/:id/status', [
  requireAdmin,
  body('status').isIn(['pending', 'reviewing', 'corrected', 'rejected', 'ignored']),
  body('resolution_notes').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          error_message: 'Invalid input data',
          details: errors.array()
        }
      });
    }

    const { status, resolution_notes } = req.body;

    const invalidStone = await InvalidStone.findByIdAndUpdate(
      req.params.id,
      {
        status,
        resolution_notes,
        reviewed_by: req.user._id,
        reviewed_date: new Date(),
        updated_by: req.user._id
      },
      { new: true }
    );

    if (!invalidStone) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Invalid stone record not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Invalid stone status updated successfully',
        result: invalidStone
      }
    });
  } catch (error) {
    console.error('Update invalid stone status error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating invalid stone status'
      }
    });
  }
});

// Assign invalid stone to user (Admin only)
router.patch('/:id/assign', [
  requireAdmin,
  body('assigned_to').isMongoId()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          error_message: 'Invalid user ID',
          details: errors.array()
        }
      });
    }

    const { assigned_to } = req.body;

    const invalidStone = await InvalidStone.findByIdAndUpdate(
      req.params.id,
      {
        assigned_to,
        assigned_date: new Date(),
        updated_by: req.user._id
      },
      { new: true }
    ).populate('assigned_to', 'fullname username');

    if (!invalidStone) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Invalid stone record not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Invalid stone assigned successfully',
        result: invalidStone
      }
    });
  } catch (error) {
    console.error('Assign invalid stone error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error assigning invalid stone'
      }
    });
  }
});

// Add correction attempt
router.post('/:id/correction', [
  body('corrected_data').isObject(),
  body('correction_notes').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { corrected_data, correction_notes } = req.body;

    const invalidStone = await InvalidStone.findById(req.params.id);
    if (!invalidStone) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Invalid stone record not found'
        }
      });
    }

    // Add correction attempt
    invalidStone.correction_attempts.push({
      attempted_by: req.user._id,
      corrected_data,
      correction_notes,
      success: false // This would be determined by validation logic
    });

    await invalidStone.save();

    res.json({
      success: true,
      data: {
        message: 'Correction attempt added successfully',
        result: invalidStone
      }
    });
  } catch (error) {
    console.error('Add correction attempt error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error adding correction attempt'
      }
    });
  }
});

// Delete invalid stone (Admin only)
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const invalidStone = await InvalidStone.findByIdAndDelete(req.params.id);
    if (!invalidStone) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Invalid stone record not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Invalid stone record deleted successfully'
      }
    });
  } catch (error) {
    console.error('Delete invalid stone error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error deleting invalid stone'
      }
    });
  }
});

module.exports = router;
