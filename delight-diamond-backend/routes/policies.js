const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Policy = require('../models/Policy');
const { requireAdmin } = require('../middleware/auth');
const upload = require('../middleware/upload');

const router = express.Router();

// Get all policies
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('policy_type').optional().isIn(['terms_of_service', 'privacy_policy', 'return_policy', 'shipping_policy', 'payment_policy', 'general', 'vendor_policy', 'merchant_policy']),
  query('status').optional().isIn(['draft', 'active', 'inactive', 'archived']),
  query('search').optional().trim()
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let query = {};
    
    if (req.query.policy_type) {
      query.policy_type = req.query.policy_type;
    }
    
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    if (req.query.search) {
      query.$text = { $search: req.query.search };
    }

    const policies = await Policy.find(query)
      .populate('created_by', 'fullname username')
      .populate('approved_by', 'fullname username')
      .sort({ effective_date: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Policy.countDocuments(query);

    res.json({
      success: true,
      data: {
        result: policies,
        pagination: {
          current_page: page,
          per_page: limit,
          total: total,
          total_pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get policies error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching policies'
      }
    });
  }
});

// Get policy by ID
router.get('/:id', async (req, res) => {
  try {
    const policy = await Policy.findById(req.params.id)
      .populate('created_by', 'fullname username')
      .populate('approved_by', 'fullname username')
      .populate('reviewed_by.user', 'fullname username');

    if (!policy) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Policy not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        result: policy
      }
    });
  } catch (error) {
    console.error('Get policy error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching policy'
      }
    });
  }
});

// Create new policy (Admin only)
router.post('/create', [
  requireAdmin,
  upload.any(),
  body('title').trim().isLength({ min: 1 }),
  body('content').trim().isLength({ min: 1 }),
  body('policy_type').isIn(['terms_of_service', 'privacy_policy', 'return_policy', 'shipping_policy', 'payment_policy', 'general', 'vendor_policy', 'merchant_policy']),
  body('effective_date').isISO8601(),
  body('applies_to').isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const policyData = { ...req.body };
    
    // Handle file attachments
    if (req.files && req.files.length > 0) {
      policyData.attachments = req.files.map(file => ({
        filename: file.filename,
        original_name: file.originalname,
        file_path: file.path,
        file_size: file.size,
        mime_type: file.mimetype
      }));
    }

    policyData.created_by = req.user._id;

    const policy = new Policy(policyData);
    await policy.save();

    const populatedPolicy = await Policy.findById(policy._id)
      .populate('created_by', 'fullname username');

    res.status(201).json({
      success: true,
      data: {
        message: 'Policy created successfully',
        result: populatedPolicy
      }
    });
  } catch (error) {
    console.error('Create policy error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error creating policy'
      }
    });
  }
});

// Update policy (Admin only)
router.put('/:id', [
  requireAdmin,
  upload.any(),
  body('title').optional().trim().isLength({ min: 1 }),
  body('content').optional().trim().isLength({ min: 1 }),
  body('status').optional().isIn(['draft', 'active', 'inactive', 'archived']),
  body('effective_date').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const updateData = { ...req.body };
    
    // Handle file attachments
    if (req.files && req.files.length > 0) {
      const newAttachments = req.files.map(file => ({
        filename: file.filename,
        original_name: file.originalname,
        file_path: file.path,
        file_size: file.size,
        mime_type: file.mimetype
      }));
      
      // Get existing policy to merge attachments
      const existingPolicy = await Policy.findById(req.params.id);
      if (existingPolicy) {
        updateData.attachments = [...(existingPolicy.attachments || []), ...newAttachments];
      } else {
        updateData.attachments = newAttachments;
      }
    }

    updateData.updated_by = req.user._id;

    const policy = await Policy.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('created_by', 'fullname username')
     .populate('updated_by', 'fullname username');

    if (!policy) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Policy not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Policy updated successfully',
        result: policy
      }
    });
  } catch (error) {
    console.error('Update policy error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating policy'
      }
    });
  }
});

// Update policy status (Admin only)
router.post('/status/:id', [
  requireAdmin,
  body('status').isIn(['draft', 'active', 'inactive', 'archived'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          error_message: 'Invalid status value'
        }
      });
    }

    const policy = await Policy.findByIdAndUpdate(
      req.params.id,
      { 
        status: req.body.status,
        updated_by: req.user._id
      },
      { new: true }
    );

    if (!policy) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Policy not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Policy status updated successfully',
        result: policy
      }
    });
  } catch (error) {
    console.error('Update policy status error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating policy status'
      }
    });
  }
});

// Delete policy (Admin only)
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const policy = await Policy.findByIdAndDelete(req.params.id);
    if (!policy) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Policy not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Policy deleted successfully'
      }
    });
  } catch (error) {
    console.error('Delete policy error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error deleting policy'
      }
    });
  }
});

module.exports = router;
