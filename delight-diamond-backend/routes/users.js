const express = require('express');
const { body, validationResult, query } = require('express-validator');
const User = require('../models/User');
const { requireAdmin } = require('../middleware/auth');
const upload = require('../middleware/upload');

const router = express.Router();

// Get all users (Admin only)
router.get('/', [
  requireAdmin,
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('search').optional().trim(),
  query('role').optional().isIn(['admin', 'user', 'vendor', 'merchant']),
  query('status').optional().isIn(['active', 'inactive', 'pending', 'suspended'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          error_message: 'Invalid query parameters',
          details: errors.array()
        }
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search;
    const role = req.query.role;
    const status = req.query.status;

    // Build query
    let query = {};
    
    if (search) {
      query.$or = [
        { fullname: { $regex: search, $options: 'i' } },
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { company: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (role) {
      query.role = role;
    }
    
    if (status) {
      query.status = status;
    }

    const users = await User.find(query)
      .populate('country_id', 'name code')
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await User.countDocuments(query);

    res.json({
      success: true,
      data: {
        result: users,
        pagination: {
          current_page: page,
          per_page: limit,
          total: total,
          total_pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching users'
      }
    });
  }
});

// Get user by ID
router.get('/:id', requireAdmin, async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .populate('country_id', 'name code')
      .select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'User not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        result: user
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching user'
      }
    });
  }
});

// Create new user (Admin only)
router.post('/', [
  requireAdmin,
  upload.single('avatar'),
  body('fullname').trim().isLength({ min: 2 }),
  body('username').trim().isLength({ min: 3 }).isAlphanumeric(),
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
  body('role').optional().isIn(['admin', 'user', 'vendor', 'merchant']),
  body('status').optional().isIn(['active', 'inactive', 'pending', 'suspended']),
  body('mobile').optional().trim(),
  body('company').optional().trim(),
  body('country_id').optional().isMongoId()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const userData = req.body.data || req.body;
    const { fullname, username, email, password, role, status, mobile, company, country_id, whatsapp, wechat, skype } = userData;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'User with this email or username already exists'
        }
      });
    }

    // Create new user
    const user = new User({
      fullname,
      username,
      email,
      password,
      role: role || 'user',
      status: status || 'active',
      mobile,
      company,
      country_id,
      whatsapp,
      wechat,
      skype,
      avatar: req.file ? `/uploads/images/${req.file.filename}` : undefined
    });

    await user.save();

    res.status(201).json({
      success: true,
      data: {
        message: 'User created successfully',
        result: user.toJSON()
      }
    });
  } catch (error) {
    console.error('Create user error:', error);
    if (error.code === 11000) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Email or username already exists'
        }
      });
    }
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error creating user'
      }
    });
  }
});

// Update user
router.put('/:id', [
  requireAdmin,
  upload.single('avatar'),
  body('fullname').optional().trim().isLength({ min: 2 }),
  body('username').optional().trim().isLength({ min: 3 }).isAlphanumeric(),
  body('email').optional().isEmail().normalizeEmail(),
  body('role').optional().isIn(['admin', 'user', 'vendor', 'merchant']),
  body('status').optional().isIn(['active', 'inactive', 'pending', 'suspended']),
  body('mobile').optional().trim(),
  body('company').optional().trim(),
  body('country_id').optional().isMongoId()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const userId = req.params.id;
    const updateData = req.body.data || req.body;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'User not found'
        }
      });
    }

    // Check for duplicate email/username if being updated
    if (updateData.email || updateData.username) {
      const duplicateQuery = {
        _id: { $ne: userId },
        $or: []
      };
      
      if (updateData.email) {
        duplicateQuery.$or.push({ email: updateData.email });
      }
      if (updateData.username) {
        duplicateQuery.$or.push({ username: updateData.username });
      }
      
      const existingUser = await User.findOne(duplicateQuery);
      if (existingUser) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: 'Email or username already exists'
          }
        });
      }
    }

    // Update avatar if uploaded
    if (req.file) {
      updateData.avatar = `/uploads/images/${req.file.filename}`;
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { ...updateData, updated_by: req.user._id },
      { new: true, runValidators: true }
    ).populate('country_id', 'name code');

    res.json({
      success: true,
      data: {
        message: 'User updated successfully',
        result: updatedUser.toJSON()
      }
    });
  } catch (error) {
    console.error('Update user error:', error);
    if (error.code === 11000) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Email or username already exists'
        }
      });
    }
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating user'
      }
    });
  }
});

// Delete user (Admin only)
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const userId = req.params.id;

    // Prevent admin from deleting themselves
    if (userId === req.user._id.toString()) {
      return res.status(400).json({
        success: false,
        error: {
          error_message: 'Cannot delete your own account'
        }
      });
    }

    const user = await User.findByIdAndDelete(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'User not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'User deleted successfully'
      }
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error deleting user'
      }
    });
  }
});

// Update user status (Admin only)
router.patch('/:id/status', [
  requireAdmin,
  body('status').isIn(['active', 'inactive', 'pending', 'suspended'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          error_message: 'Invalid status value'
        }
      });
    }

    const userId = req.params.id;
    const { status } = req.body;

    const user = await User.findByIdAndUpdate(
      userId,
      { status, updated_by: req.user._id },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'User not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'User status updated successfully',
        result: user.toJSON()
      }
    });
  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating user status'
      }
    });
  }
});

module.exports = router;
