const express = require('express');
const { query } = require('express-validator');
const UploadHistory = require('../models/UploadHistory');

const router = express.Router();

// Get all upload history
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('upload_type').optional().isIn(['products', 'star_melee', 'vendors', 'merchants', 'users', 'master_data']),
  query('status').optional().isIn(['uploaded', 'processing', 'completed', 'failed', 'cancelled'])
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let query = {};
    
    if (req.query.upload_type) query.upload_type = req.query.upload_type;
    if (req.query.status) query.status = req.query.status;

    const uploads = await UploadHistory.find(query)
      .populate('uploaded_by', 'fullname username')
      .populate('processed_by', 'fullname username')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await UploadHistory.countDocuments(query);

    res.json({
      success: true,
      data: {
        result: uploads,
        pagination: {
          current_page: page,
          per_page: limit,
          total: total,
          total_pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get upload history error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching upload history'
      }
    });
  }
});

// Get upload history by ID
router.get('/:id', async (req, res) => {
  try {
    const upload = await UploadHistory.findById(req.params.id)
      .populate('uploaded_by', 'fullname username')
      .populate('processed_by', 'fullname username');

    if (!upload) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Upload record not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        result: upload
      }
    });
  } catch (error) {
    console.error('Get upload history error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching upload history'
      }
    });
  }
});

module.exports = router;
