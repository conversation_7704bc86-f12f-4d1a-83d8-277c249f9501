const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Cart = require('../models/Cart');
const Product = require('../models/Product');

const router = express.Router();

// Get user's cart items
router.get('/', [
  query('status').optional().isIn(['active', 'hold', 'confirmed', 'removed'])
], async (req, res) => {
  try {
    const userId = req.user._id;
    const status = req.query.status || 'active';

    const cartItems = await Cart.find({ user_id: userId, status })
      .populate({
        path: 'product_id',
        populate: [
          { path: 'shape_id', select: 'name code' },
          { path: 'color_id', select: 'name code' },
          { path: 'clarity_id', select: 'name code' }
        ]
      })
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: {
        result: cartItems
      }
    });
  } catch (error) {
    console.error('Get cart error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching cart items'
      }
    });
  }
});

// Add item to cart
router.post('/add', [
  body('product_id').isMongoId(),
  body('quantity').optional().isInt({ min: 1 }),
  body('notes').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { product_id, quantity = 1, notes } = req.body;
    const userId = req.user._id;

    // Check if product exists and is available
    const product = await Product.findById(product_id);
    if (!product) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Product not found'
        }
      });
    }

    if (product.status !== 'available') {
      return res.status(400).json({
        success: false,
        error: {
          error_message: 'Product is not available'
        }
      });
    }

    // Check if item already exists in cart
    const existingCartItem = await Cart.findOne({
      user_id: userId,
      product_id: product_id,
      status: 'active'
    });

    if (existingCartItem) {
      return res.status(400).json({
        success: false,
        error: {
          error_message: 'Product already in cart'
        }
      });
    }

    // Add to cart
    const cartItem = new Cart({
      user_id: userId,
      product_id: product_id,
      quantity: quantity,
      price_at_time: product.amount,
      notes: notes,
      created_by: userId
    });

    await cartItem.save();

    // Populate the cart item
    const populatedCartItem = await Cart.findById(cartItem._id)
      .populate({
        path: 'product_id',
        populate: [
          { path: 'shape_id', select: 'name code' },
          { path: 'color_id', select: 'name code' },
          { path: 'clarity_id', select: 'name code' }
        ]
      });

    res.status(201).json({
      success: true,
      data: {
        message: 'Product added to cart successfully',
        result: populatedCartItem
      }
    });
  } catch (error) {
    console.error('Add to cart error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error adding to cart'
      }
    });
  }
});

// Update cart item
router.put('/:id', [
  body('quantity').optional().isInt({ min: 1 }),
  body('notes').optional().trim(),
  body('status').optional().isIn(['active', 'hold', 'confirmed', 'removed'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const cartItemId = req.params.id;
    const userId = req.user._id;
    const updateData = req.body;

    // Find cart item and ensure it belongs to the user
    const cartItem = await Cart.findOne({
      _id: cartItemId,
      user_id: userId
    });

    if (!cartItem) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Cart item not found'
        }
      });
    }

    // Update cart item
    const updatedCartItem = await Cart.findByIdAndUpdate(
      cartItemId,
      updateData,
      { new: true, runValidators: true }
    ).populate({
      path: 'product_id',
      populate: [
        { path: 'shape_id', select: 'name code' },
        { path: 'color_id', select: 'name code' },
        { path: 'clarity_id', select: 'name code' }
      ]
    });

    res.json({
      success: true,
      data: {
        message: 'Cart item updated successfully',
        result: updatedCartItem
      }
    });
  } catch (error) {
    console.error('Update cart item error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating cart item'
      }
    });
  }
});

// Remove item from cart
router.delete('/:id', async (req, res) => {
  try {
    const cartItemId = req.params.id;
    const userId = req.user._id;

    // Find and delete cart item, ensuring it belongs to the user
    const cartItem = await Cart.findOneAndDelete({
      _id: cartItemId,
      user_id: userId
    });

    if (!cartItem) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Cart item not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Cart item removed successfully'
      }
    });
  } catch (error) {
    console.error('Remove cart item error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error removing cart item'
      }
    });
  }
});

// Hold diamonds (move to hold status)
router.post('/hold', [
  body('cart_item_ids').isArray(),
  body('hold_until').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { cart_item_ids, hold_until } = req.body;
    const userId = req.user._id;

    // Update cart items to hold status
    const result = await Cart.updateMany(
      {
        _id: { $in: cart_item_ids },
        user_id: userId,
        status: 'active'
      },
      {
        status: 'hold',
        hold_until: hold_until ? new Date(hold_until) : new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours default
      }
    );

    res.json({
      success: true,
      data: {
        message: `${result.modifiedCount} items moved to hold`,
        modified_count: result.modifiedCount
      }
    });
  } catch (error) {
    console.error('Hold diamonds error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error holding diamonds'
      }
    });
  }
});

// Confirm diamonds (move to confirmed status)
router.post('/confirm', [
  body('cart_item_ids').isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { cart_item_ids } = req.body;
    const userId = req.user._id;

    // Update cart items to confirmed status
    const result = await Cart.updateMany(
      {
        _id: { $in: cart_item_ids },
        user_id: userId,
        status: { $in: ['active', 'hold'] }
      },
      {
        status: 'confirmed'
      }
    );

    res.json({
      success: true,
      data: {
        message: `${result.modifiedCount} items confirmed`,
        modified_count: result.modifiedCount
      }
    });
  } catch (error) {
    console.error('Confirm diamonds error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error confirming diamonds'
      }
    });
  }
});

// Clear cart
router.delete('/clear', async (req, res) => {
  try {
    const userId = req.user._id;

    const result = await Cart.deleteMany({
      user_id: userId,
      status: 'active'
    });

    res.json({
      success: true,
      data: {
        message: `${result.deletedCount} items removed from cart`,
        deleted_count: result.deletedCount
      }
    });
  } catch (error) {
    console.error('Clear cart error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error clearing cart'
      }
    });
  }
});

module.exports = router;
