const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Demand = require('../models/Demand');

const router = express.Router();

// Get all demands
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['open', 'in_progress', 'matched', 'fulfilled', 'cancelled', 'expired']),
  query('priority').optional().isIn(['low', 'medium', 'high']),
  query('urgency').optional().isIn(['low', 'medium', 'high', 'urgent'])
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let query = {};
    
    if (req.query.status) query.status = req.query.status;
    if (req.query.priority) query.priority = req.query.priority;
    if (req.query.urgency) query.urgency = req.query.urgency;

    const demands = await Demand.find(query)
      .populate('customer_id', 'fullname email mobile')
      .populate('shape_id', 'name')
      .populate('color_preferences', 'name')
      .populate('clarity_preferences', 'name')
      .populate('assigned_to', 'fullname username')
      .populate('matched_products.product_id', 'stone_id carat shape_id color_id clarity_id amount')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Demand.countDocuments(query);

    res.json({
      success: true,
      data: {
        result: demands,
        pagination: {
          current_page: page,
          per_page: limit,
          total: total,
          total_pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get demands error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching demands'
      }
    });
  }
});

// Create new demand
router.post('/', [
  body('customer_name').trim().isLength({ min: 1 }),
  body('customer_email').isEmail().normalizeEmail(),
  body('title').trim().isLength({ min: 1 }),
  body('urgency').optional().isIn(['low', 'medium', 'high', 'urgent']),
  body('priority').optional().isIn(['low', 'medium', 'high'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const demand = new Demand({
      ...req.body,
      customer_id: req.user._id,
      created_by: req.user._id
    });

    await demand.save();

    const populatedDemand = await Demand.findById(demand._id)
      .populate('customer_id', 'fullname email mobile')
      .populate('shape_id', 'name');

    res.status(201).json({
      success: true,
      data: {
        message: 'Demand created successfully',
        result: populatedDemand
      }
    });
  } catch (error) {
    console.error('Create demand error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error creating demand'
      }
    });
  }
});

// Update demand
router.put('/:id', [
  body('title').optional().trim().isLength({ min: 1 }),
  body('status').optional().isIn(['open', 'in_progress', 'matched', 'fulfilled', 'cancelled', 'expired']),
  body('priority').optional().isIn(['low', 'medium', 'high']),
  body('urgency').optional().isIn(['low', 'medium', 'high', 'urgent'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const demand = await Demand.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updated_by: req.user._id },
      { new: true, runValidators: true }
    ).populate('customer_id', 'fullname email mobile')
     .populate('assigned_to', 'fullname username');

    if (!demand) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Demand not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Demand updated successfully',
        result: demand
      }
    });
  } catch (error) {
    console.error('Update demand error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating demand'
      }
    });
  }
});

// Delete demand
router.delete('/:id', async (req, res) => {
  try {
    const demand = await Demand.findByIdAndDelete(req.params.id);
    if (!demand) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Demand not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Demand deleted successfully'
      }
    });
  } catch (error) {
    console.error('Delete demand error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error deleting demand'
      }
    });
  }
});

module.exports = router;
