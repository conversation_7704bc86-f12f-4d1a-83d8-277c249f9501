const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Vendor = require('../models/Vendor');
const { requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get all vendors
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('search').optional().trim(),
  query('status').optional().isIn(['active', 'inactive', 'pending', 'suspended'])
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let query = {};
    
    if (req.query.search) {
      query.$or = [
        { first_name: { $regex: req.query.search, $options: 'i' } },
        { last_name: { $regex: req.query.search, $options: 'i' } },
        { company: { $regex: req.query.search, $options: 'i' } },
        { email: { $regex: req.query.search, $options: 'i' } }
      ];
    }
    
    if (req.query.status) {
      query.status = req.query.status;
    }

    const vendors = await Vendor.find(query)
      .populate('country_id', 'name code')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Vendor.countDocuments(query);

    res.json({
      success: true,
      data: {
        result: vendors,
        pagination: {
          current_page: page,
          per_page: limit,
          total: total,
          total_pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get vendors error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching vendors'
      }
    });
  }
});

// Get vendor by ID
router.get('/:id', async (req, res) => {
  try {
    const vendor = await Vendor.findById(req.params.id)
      .populate('country_id', 'name code');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Vendor not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        result: vendor
      }
    });
  } catch (error) {
    console.error('Get vendor error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching vendor'
      }
    });
  }
});

// Create new vendor
router.post('/', [
  body('username').trim().isLength({ min: 3 }).isAlphanumeric(),
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 6 }),
  body('first_name').trim().isLength({ min: 1 }),
  body('last_name').trim().isLength({ min: 1 }),
  body('company').trim().isLength({ min: 1 }),
  body('mobile').trim().isLength({ min: 1 }),
  body('address').trim().isLength({ min: 1 }),
  body('city').trim().isLength({ min: 1 }),
  body('state').trim().isLength({ min: 1 }),
  body('pincode').trim().isLength({ min: 1 }),
  body('country_id').isMongoId(),
  body('business_type').isArray(),
  body('buying_group').isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    // Check if vendor already exists
    const existingVendor = await Vendor.findOne({
      $or: [{ email: req.body.email }, { username: req.body.username }]
    });

    if (existingVendor) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Vendor with this email or username already exists'
        }
      });
    }

    const vendor = new Vendor({
      ...req.body,
      created_by: req.user._id
    });

    await vendor.save();

    res.status(201).json({
      success: true,
      data: {
        message: 'Vendor created successfully',
        result: vendor.toJSON()
      }
    });
  } catch (error) {
    console.error('Create vendor error:', error);
    if (error.code === 11000) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Email or username already exists'
        }
      });
    }
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error creating vendor'
      }
    });
  }
});

// Update vendor
router.put('/:id', [
  body('username').optional().trim().isLength({ min: 3 }).isAlphanumeric(),
  body('email').optional().isEmail().normalizeEmail(),
  body('first_name').optional().trim().isLength({ min: 1 }),
  body('last_name').optional().trim().isLength({ min: 1 }),
  body('company').optional().trim().isLength({ min: 1 }),
  body('mobile').optional().trim().isLength({ min: 1 }),
  body('address').optional().trim().isLength({ min: 1 }),
  body('city').optional().trim().isLength({ min: 1 }),
  body('state').optional().trim().isLength({ min: 1 }),
  body('pincode').optional().trim().isLength({ min: 1 }),
  body('country_id').optional().isMongoId(),
  body('business_type').optional().isArray(),
  body('buying_group').optional().isArray(),
  body('status').optional().isIn(['active', 'inactive', 'pending', 'suspended'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const vendorId = req.params.id;
    const updateData = {
      ...req.body,
      updated_by: req.user._id
    };

    // Check for duplicate email/username if being updated
    if (updateData.email || updateData.username) {
      const duplicateQuery = {
        _id: { $ne: vendorId },
        $or: []
      };
      
      if (updateData.email) {
        duplicateQuery.$or.push({ email: updateData.email });
      }
      if (updateData.username) {
        duplicateQuery.$or.push({ username: updateData.username });
      }
      
      const existingVendor = await Vendor.findOne(duplicateQuery);
      if (existingVendor) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: 'Email or username already exists'
          }
        });
      }
    }

    const vendor = await Vendor.findByIdAndUpdate(
      vendorId,
      updateData,
      { new: true, runValidators: true }
    ).populate('country_id', 'name code');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Vendor not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Vendor updated successfully',
        result: vendor.toJSON()
      }
    });
  } catch (error) {
    console.error('Update vendor error:', error);
    if (error.code === 11000) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Email or username already exists'
        }
      });
    }
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating vendor'
      }
    });
  }
});

// Delete vendor
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const vendor = await Vendor.findByIdAndDelete(req.params.id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Vendor not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Vendor deleted successfully'
      }
    });
  } catch (error) {
    console.error('Delete vendor error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error deleting vendor'
      }
    });
  }
});

module.exports = router;
