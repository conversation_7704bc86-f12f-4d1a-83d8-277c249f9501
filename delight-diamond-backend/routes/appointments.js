const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Appointment = require('../models/Appointment');

const router = express.Router();

// Get all appointments
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'rescheduled']),
  query('date_from').optional().isISO8601(),
  query('date_to').optional().isISO8601()
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let query = {};
    
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    if (req.query.date_from || req.query.date_to) {
      query.appointment_date = {};
      if (req.query.date_from) {
        query.appointment_date.$gte = new Date(req.query.date_from);
      }
      if (req.query.date_to) {
        query.appointment_date.$lte = new Date(req.query.date_to);
      }
    }

    const appointments = await Appointment.find(query)
      .populate('customer_id', 'fullname email mobile')
      .populate('vendor_id', 'company first_name last_name')
      .populate('merchant_id', 'company first_name last_name')
      .populate('assigned_to', 'fullname username')
      .populate('related_products', 'stone_id carat shape_id color_id clarity_id')
      .sort({ appointment_date: 1 })
      .skip(skip)
      .limit(limit);

    const total = await Appointment.countDocuments(query);

    res.json({
      success: true,
      data: {
        result: appointments,
        pagination: {
          current_page: page,
          per_page: limit,
          total: total,
          total_pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get appointments error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching appointments'
      }
    });
  }
});

// Create new appointment
router.post('/', [
  body('title').trim().isLength({ min: 1 }),
  body('customer_id').isMongoId(),
  body('appointment_date').isISO8601(),
  body('appointment_time').trim().isLength({ min: 1 }),
  body('meeting_type').isIn(['in_person', 'video_call', 'phone_call', 'online']),
  body('priority').optional().isIn(['low', 'medium', 'high', 'urgent'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const appointment = new Appointment({
      ...req.body,
      created_by: req.user._id
    });

    await appointment.save();

    const populatedAppointment = await Appointment.findById(appointment._id)
      .populate('customer_id', 'fullname email mobile')
      .populate('assigned_to', 'fullname username');

    res.status(201).json({
      success: true,
      data: {
        message: 'Appointment created successfully',
        result: populatedAppointment
      }
    });
  } catch (error) {
    console.error('Create appointment error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error creating appointment'
      }
    });
  }
});

// Update appointment
router.put('/:id', [
  body('title').optional().trim().isLength({ min: 1 }),
  body('appointment_date').optional().isISO8601(),
  body('appointment_time').optional().trim().isLength({ min: 1 }),
  body('status').optional().isIn(['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'rescheduled']),
  body('priority').optional().isIn(['low', 'medium', 'high', 'urgent'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const appointment = await Appointment.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updated_by: req.user._id },
      { new: true, runValidators: true }
    ).populate('customer_id', 'fullname email mobile')
     .populate('assigned_to', 'fullname username');

    if (!appointment) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Appointment not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Appointment updated successfully',
        result: appointment
      }
    });
  } catch (error) {
    console.error('Update appointment error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating appointment'
      }
    });
  }
});

// Delete appointment
router.delete('/:id', async (req, res) => {
  try {
    const appointment = await Appointment.findByIdAndDelete(req.params.id);
    if (!appointment) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Appointment not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Appointment deleted successfully'
      }
    });
  } catch (error) {
    console.error('Delete appointment error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error deleting appointment'
      }
    });
  }
});

module.exports = router;
