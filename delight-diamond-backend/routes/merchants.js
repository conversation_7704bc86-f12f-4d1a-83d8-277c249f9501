const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Merchant = require('../models/Merchant');
const { requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get all merchants
router.get('/', [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('search').optional().trim(),
  query('status').optional().isIn(['active', 'inactive', 'pending', 'suspended'])
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let query = {};
    
    if (req.query.search) {
      query.$or = [
        { first_name: { $regex: req.query.search, $options: 'i' } },
        { last_name: { $regex: req.query.search, $options: 'i' } },
        { company: { $regex: req.query.search, $options: 'i' } },
        { email: { $regex: req.query.search, $options: 'i' } }
      ];
    }
    
    if (req.query.status) {
      query.status = req.query.status;
    }

    const merchants = await Merchant.find(query)
      .populate('country_id', 'name code')
      .populate('preferred_shapes', 'name')
      .populate('preferred_colors', 'name')
      .populate('preferred_clarity', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Merchant.countDocuments(query);

    res.json({
      success: true,
      data: {
        result: merchants,
        pagination: {
          current_page: page,
          per_page: limit,
          total: total,
          total_pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get merchants error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching merchants'
      }
    });
  }
});

// Get merchant by ID
router.get('/:id', async (req, res) => {
  try {
    const merchant = await Merchant.findById(req.params.id)
      .populate('country_id', 'name code')
      .populate('preferred_shapes', 'name')
      .populate('preferred_colors', 'name')
      .populate('preferred_clarity', 'name');

    if (!merchant) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Merchant not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        result: merchant
      }
    });
  } catch (error) {
    console.error('Get merchant error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error fetching merchant'
      }
    });
  }
});

// Create new merchant
router.post('/', [
  body('username').trim().isLength({ min: 3 }).isAlphanumeric(),
  body('email').isEmail().normalizeEmail(),
  body('first_name').trim().isLength({ min: 1 }),
  body('last_name').trim().isLength({ min: 1 }),
  body('company').trim().isLength({ min: 1 }),
  body('mobile').trim().isLength({ min: 1 }),
  body('address').trim().isLength({ min: 1 }),
  body('city').trim().isLength({ min: 1 }),
  body('state').trim().isLength({ min: 1 }),
  body('pincode').trim().isLength({ min: 1 }),
  body('country_id').isMongoId(),
  body('business_type').isArray(),
  body('buying_group').isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    // Check if merchant already exists
    const existingMerchant = await Merchant.findOne({
      $or: [{ email: req.body.email }, { username: req.body.username }]
    });

    if (existingMerchant) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Merchant with this email or username already exists'
        }
      });
    }

    const merchant = new Merchant({
      ...req.body,
      created_by: req.user._id
    });

    await merchant.save();

    res.status(201).json({
      success: true,
      data: {
        message: 'Merchant created successfully',
        result: merchant.toJSON()
      }
    });
  } catch (error) {
    console.error('Create merchant error:', error);
    if (error.code === 11000) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Email or username already exists'
        }
      });
    }
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error creating merchant'
      }
    });
  }
});

// Update merchant
router.put('/:id', [
  body('username').optional().trim().isLength({ min: 3 }).isAlphanumeric(),
  body('email').optional().isEmail().normalizeEmail(),
  body('first_name').optional().trim().isLength({ min: 1 }),
  body('last_name').optional().trim().isLength({ min: 1 }),
  body('company').optional().trim().isLength({ min: 1 }),
  body('mobile').optional().trim().isLength({ min: 1 }),
  body('address').optional().trim().isLength({ min: 1 }),
  body('city').optional().trim().isLength({ min: 1 }),
  body('state').optional().trim().isLength({ min: 1 }),
  body('pincode').optional().trim().isLength({ min: 1 }),
  body('country_id').optional().isMongoId(),
  body('business_type').optional().isArray(),
  body('buying_group').optional().isArray(),
  body('status').optional().isIn(['active', 'inactive', 'pending', 'suspended'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const merchantId = req.params.id;
    const updateData = {
      ...req.body,
      updated_by: req.user._id
    };

    // Check for duplicate email/username if being updated
    if (updateData.email || updateData.username) {
      const duplicateQuery = {
        _id: { $ne: merchantId },
        $or: []
      };
      
      if (updateData.email) {
        duplicateQuery.$or.push({ email: updateData.email });
      }
      if (updateData.username) {
        duplicateQuery.$or.push({ username: updateData.username });
      }
      
      const existingMerchant = await Merchant.findOne(duplicateQuery);
      if (existingMerchant) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: 'Email or username already exists'
          }
        });
      }
    }

    const merchant = await Merchant.findByIdAndUpdate(
      merchantId,
      updateData,
      { new: true, runValidators: true }
    ).populate('country_id', 'name code');

    if (!merchant) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Merchant not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Merchant updated successfully',
        result: merchant.toJSON()
      }
    });
  } catch (error) {
    console.error('Update merchant error:', error);
    if (error.code === 11000) {
      return res.status(422).json({
        success: false,
        error: {
          error_message: 'Email or username already exists'
        }
      });
    }
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error updating merchant'
      }
    });
  }
});

// Delete merchant
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const merchant = await Merchant.findByIdAndDelete(req.params.id);
    if (!merchant) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: 'Merchant not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Merchant deleted successfully'
      }
    });
  } catch (error) {
    console.error('Delete merchant error:', error);
    res.status(500).json({
      success: false,
      error: {
        error_message: 'Server error deleting merchant'
      }
    });
  }
});

module.exports = router;
