const express = require("express");
const { body, validationResult, query } = require("express-validator");
const Product = require("../models/Product");
const UploadHistory = require("../models/UploadHistory");
const InvalidStone = require("../models/InvalidStone");
const upload = require("../middleware/upload");
const csv = require("csv-parser");
const fs = require("fs");

const router = express.Router();

// Get all products
router.get(
  "/",
  [
    query("page").optional().isInt({ min: 1 }),
    query("limit").optional().isInt({ min: 1, max: 100 }),
    query("search").optional().trim(),
    query("diamond_type").optional().isIn(["natural", "lab_grown"]),
    query("status")
      .optional()
      .isIn(["available", "hold", "sold", "reserved", "inactive"]),
    query("shape_id").optional().isMongoId(),
    query("color_id").optional().isMongoId(),
    query("clarity_id").optional().isMongoId(),
    query("min_carat").optional().isFloat({ min: 0 }),
    query("max_carat").optional().isFloat({ min: 0 }),
    query("min_price").optional().isFloat({ min: 0 }),
    query("max_price").optional().isFloat({ min: 0 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            error_message: "Invalid query parameters",
            details: errors.array(),
          },
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;

      // Build query
      let query = {};

      if (req.query.search) {
        query.$or = [
          { stone_id: { $regex: req.query.search, $options: "i" } },
          { cert_no: { $regex: req.query.search, $options: "i" } },
        ];
      }

      if (req.query.diamond_type) query.diamond_type = req.query.diamond_type;
      if (req.query.status) query.status = req.query.status;
      if (req.query.shape_id) query.shape_id = req.query.shape_id;
      if (req.query.color_id) query.color_id = req.query.color_id;
      if (req.query.clarity_id) query.clarity_id = req.query.clarity_id;

      if (req.query.min_carat || req.query.max_carat) {
        query.carat = {};
        if (req.query.min_carat)
          query.carat.$gte = parseFloat(req.query.min_carat);
        if (req.query.max_carat)
          query.carat.$lte = parseFloat(req.query.max_carat);
      }

      if (req.query.min_price || req.query.max_price) {
        query.amount = {};
        if (req.query.min_price)
          query.amount.$gte = parseFloat(req.query.min_price);
        if (req.query.max_price)
          query.amount.$lte = parseFloat(req.query.max_price);
      }

      const products = await Product.find(query)
        .populate("shape_id", "name code")
        .populate("color_id", "name code")
        .populate("clarity_id", "name code")
        .populate("cut_id", "name code")
        .populate("polish_id", "name code")
        .populate("symmetry_id", "name code")
        .populate("fluorescence_id", "name code")
        .populate("vendor_id", "company first_name last_name")
        .populate("created_by", "fullname username")
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Product.countDocuments(query);

      res.json({
        success: true,
        data: {
          result: products,
          pagination: {
            current_page: page,
            per_page: limit,
            total: total,
            total_pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      console.error("Get products error:", error);
      res.status(500).json({
        success: false,
        error: {
          error_message: "Server error fetching products",
        },
      });
    }
  }
);

// Get product by ID
router.get("/:id", async (req, res) => {
  try {
    const product = await Product.findById(req.params.id)
      .populate("shape_id", "name code")
      .populate("color_id", "name code")
      .populate("clarity_id", "name code")
      .populate("cut_id", "name code")
      .populate("polish_id", "name code")
      .populate("symmetry_id", "name code")
      .populate("fluorescence_id", "name code")
      .populate("fluorescence_color_id", "name code")
      .populate("colors_id", "name code")
      .populate("overtone_id", "name code")
      .populate("intensity_id", "name code")
      .populate("vendor_id", "company first_name last_name email mobile")
      .populate("created_by", "fullname username")
      .populate("updated_by", "fullname username");

    if (!product) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: "Product not found",
        },
      });
    }

    res.json({
      success: true,
      data: {
        result: product,
      },
    });
  } catch (error) {
    console.error("Get product error:", error);
    res.status(500).json({
      success: false,
      error: {
        error_message: "Server error fetching product",
      },
    });
  }
});

// Create new product
router.post(
  "/create",
  [
    upload.fields([
      { name: "image", maxCount: 1 },
      { name: "video", maxCount: 1 },
    ]),
    body("stone_id").trim().notEmpty(),
    body("cert_no").trim().notEmpty(),
    body("cert_type").isIn([
      "GIA",
      "IGI",
      "SSEF",
      "GÜBELIN",
      "AGS",
      "EGL",
      "GCAL",
      "Other",
    ]),
    body("diamond_type").isIn(["natural", "lab_grown"]),
    body("carat").isFloat({ min: 0.01 }),
    body("shape_id").isMongoId(),
    body("color_id").isMongoId(),
    body("clarity_id").isMongoId(),
    body("rate").isFloat({ min: 0 }),
    body("amount").isFloat({ min: 0 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: "Validation failed",
            details: errors.array(),
          },
        });
      }

      // Check if stone_id already exists
      const existingProduct = await Product.findOne({
        stone_id: req.body.stone_id,
      });
      if (existingProduct) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: "Product with this stone ID already exists",
          },
        });
      }

      const productData = { ...req.body };

      // Add file URLs if uploaded
      if (req.files) {
        if (req.files.image) {
          productData.image = `/uploads/images/${req.files.image[0].filename}`;
        }
        if (req.files.video) {
          productData.video = `/uploads/videos/${req.files.video[0].filename}`;
        }
      }

      productData.created_by = req.user._id;

      const product = new Product(productData);
      await product.save();

      // Populate the created product
      const populatedProduct = await Product.findById(product._id)
        .populate("shape_id", "name code")
        .populate("color_id", "name code")
        .populate("clarity_id", "name code")
        .populate("created_by", "fullname username");

      res.status(201).json({
        success: true,
        data: {
          message: "Product created successfully",
          result: populatedProduct,
        },
      });
    } catch (error) {
      console.error("Create product error:", error);
      if (error.code === 11000) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: "Product with this stone ID already exists",
          },
        });
      }
      res.status(500).json({
        success: false,
        error: {
          error_message: "Server error creating product",
        },
      });
    }
  }
);

// Update product
router.put(
  "/:id",
  [
    upload.fields([
      { name: "image", maxCount: 1 },
      { name: "video", maxCount: 1 },
    ]),
    body("stone_id").optional().trim().notEmpty(),
    body("cert_no").optional().trim().notEmpty(),
    body("cert_type")
      .optional()
      .isIn(["GIA", "IGI", "SSEF", "GÜBELIN", "AGS", "EGL", "GCAL", "Other"]),
    body("diamond_type").optional().isIn(["natural", "lab_grown"]),
    body("carat").optional().isFloat({ min: 0.01 }),
    body("shape_id").optional().isMongoId(),
    body("color_id").optional().isMongoId(),
    body("clarity_id").optional().isMongoId(),
    body("rate").optional().isFloat({ min: 0 }),
    body("amount").optional().isFloat({ min: 0 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: "Validation failed",
            details: errors.array(),
          },
        });
      }

      const productId = req.params.id;
      const updateData = { ...req.body };

      // Check if product exists
      const product = await Product.findById(productId);
      if (!product) {
        return res.status(404).json({
          success: false,
          error: {
            error_message: "Product not found",
          },
        });
      }

      // Check for duplicate stone_id if being updated
      if (updateData.stone_id && updateData.stone_id !== product.stone_id) {
        const existingProduct = await Product.findOne({
          stone_id: updateData.stone_id,
          _id: { $ne: productId },
        });
        if (existingProduct) {
          return res.status(422).json({
            success: false,
            error: {
              error_message: "Product with this stone ID already exists",
            },
          });
        }
      }

      // Add file URLs if uploaded
      if (req.files) {
        if (req.files.image) {
          updateData.image = `/uploads/images/${req.files.image[0].filename}`;
        }
        if (req.files.video) {
          updateData.video = `/uploads/videos/${req.files.video[0].filename}`;
        }
      }

      updateData.updated_by = req.user._id;

      const updatedProduct = await Product.findByIdAndUpdate(
        productId,
        updateData,
        { new: true, runValidators: true }
      )
        .populate("shape_id", "name code")
        .populate("color_id", "name code")
        .populate("clarity_id", "name code");

      res.json({
        success: true,
        data: {
          message: "Product updated successfully",
          result: updatedProduct,
        },
      });
    } catch (error) {
      console.error("Update product error:", error);
      if (error.code === 11000) {
        return res.status(422).json({
          success: false,
          error: {
            error_message: "Product with this stone ID already exists",
          },
        });
      }
      res.status(500).json({
        success: false,
        error: {
          error_message: "Server error updating product",
        },
      });
    }
  }
);

// Delete product
router.delete("/:id", async (req, res) => {
  try {
    const product = await Product.findByIdAndDelete(req.params.id);
    if (!product) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: "Product not found",
        },
      });
    }

    res.json({
      success: true,
      data: {
        message: "Product deleted successfully",
      },
    });
  } catch (error) {
    console.error("Delete product error:", error);
    res.status(500).json({
      success: false,
      error: {
        error_message: "Server error deleting product",
      },
    });
  }
});

// Update product status
router.patch(
  "/:id/status",
  [body("status").isIn(["available", "hold", "sold", "reserved", "inactive"])],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            error_message: "Invalid status value",
          },
        });
      }

      const productId = req.params.id;
      const { status } = req.body;

      const product = await Product.findByIdAndUpdate(
        productId,
        { status, updated_by: req.user._id },
        { new: true }
      );

      if (!product) {
        return res.status(404).json({
          success: false,
          error: {
            error_message: "Product not found",
          },
        });
      }

      res.json({
        success: true,
        data: {
          message: "Product status updated successfully",
          result: product,
        },
      });
    } catch (error) {
      console.error("Update product status error:", error);
      res.status(500).json({
        success: false,
        error: {
          error_message: "Server error updating product status",
        },
      });
    }
  }
);

// Bulk upload products via CSV
router.post("/bulk-upload", upload.single("csv"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          error_message: "CSV file is required",
        },
      });
    }

    // Create upload history record
    const uploadHistory = new UploadHistory({
      filename: req.file.filename,
      original_filename: req.file.originalname,
      file_path: req.file.path,
      file_size: req.file.size,
      file_type: "csv",
      mime_type: req.file.mimetype,
      upload_type: "products",
      uploaded_by: req.user._id,
      status: "processing",
      processing_started_at: new Date(),
    });

    await uploadHistory.save();

    // Process CSV file asynchronously
    processProductCSV(req.file.path, uploadHistory._id, req.user._id);

    res.json({
      success: true,
      data: {
        message: "File uploaded successfully. Processing started.",
        upload_id: uploadHistory._id,
      },
    });
  } catch (error) {
    console.error("Bulk upload error:", error);
    res.status(500).json({
      success: false,
      error: {
        error_message: "Server error during bulk upload",
      },
    });
  }
});

// Get upload status
router.get("/upload-status/:uploadId", async (req, res) => {
  try {
    const uploadHistory = await UploadHistory.findById(req.params.uploadId);

    if (!uploadHistory) {
      return res.status(404).json({
        success: false,
        error: {
          error_message: "Upload record not found",
        },
      });
    }

    res.json({
      success: true,
      data: {
        result: uploadHistory,
      },
    });
  } catch (error) {
    console.error("Get upload status error:", error);
    res.status(500).json({
      success: false,
      error: {
        error_message: "Server error fetching upload status",
      },
    });
  }
});

// Process CSV file function
async function processProductCSV(filePath, uploadHistoryId, userId) {
  const uploadHistory = await UploadHistory.findById(uploadHistoryId);
  const results = [];
  const errors = [];
  let totalRecords = 0;
  let successfulRecords = 0;
  let failedRecords = 0;

  try {
    const stream = fs
      .createReadStream(filePath)
      .pipe(csv())
      .on("data", (data) => {
        totalRecords++;
        results.push(data);
      })
      .on("end", async () => {
        // Process each row
        for (let i = 0; i < results.length; i++) {
          const row = results[i];
          try {
            // Validate and create product
            const productData = {
              stone_id: row.stone_id || row.Stone_ID,
              cert_no: row.cert_no || row.Certificate_Number,
              cert_type: row.cert_type || row.Certificate_Type || "GIA",
              diamond_type: row.diamond_type || row.Diamond_Type || "natural",
              carat: parseFloat(row.carat || row.Carat),
              shape_id: row.shape_id, // This should be mapped from shape name
              color_id: row.color_id, // This should be mapped from color name
              clarity_id: row.clarity_id, // This should be mapped from clarity name
              rate: parseFloat(row.rate || row.Rate),
              amount: parseFloat(row.amount || row.Amount),
              created_by: userId,
            };

            // Skip if required fields are missing
            if (
              !productData.stone_id ||
              !productData.cert_no ||
              !productData.carat
            ) {
              failedRecords++;
              errors.push({
                row_number: i + 1,
                error_message:
                  "Missing required fields: stone_id, cert_no, or carat",
                raw_data: row,
              });
              continue;
            }

            // Check for duplicate
            const existingProduct = await Product.findOne({
              stone_id: productData.stone_id,
            });
            if (existingProduct) {
              failedRecords++;
              errors.push({
                row_number: i + 1,
                error_message: "Duplicate stone_id",
                raw_data: row,
              });
              continue;
            }

            const product = new Product(productData);
            await product.save();
            successfulRecords++;
          } catch (error) {
            failedRecords++;
            errors.push({
              row_number: i + 1,
              error_message: error.message,
              raw_data: row,
            });
          }
        }

        // Update upload history
        uploadHistory.status = "completed";
        uploadHistory.processing_completed_at = new Date();
        uploadHistory.total_records = totalRecords;
        uploadHistory.successful_records = successfulRecords;
        uploadHistory.failed_records = failedRecords;
        uploadHistory.errors = errors;
        uploadHistory.summary = `Processed ${totalRecords} records. ${successfulRecords} successful, ${failedRecords} failed.`;

        await uploadHistory.save();
      });
  } catch (error) {
    console.error("CSV processing error:", error);
    uploadHistory.status = "failed";
    uploadHistory.processing_completed_at = new Date();
    uploadHistory.errors = [{ error_message: error.message }];
    await uploadHistory.save();
  }
}

module.exports = router;
