const mongoose = require('mongoose');
require('dotenv').config();

const User = require('../models/User');
const {
  Shape, Color, Clarity, Size, Cut, Polish, Symmetry,
  Fluorescence, FluorescenceColor, FancyColor, Finish,
  Intensity, Overtone, Country, BusinessType, BuyingGroup
} = require('../models/MasterData');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/delight_diamond', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const seedData = async () => {
  try {
    console.log('Starting database seeding...');

    // Clear existing data
    await User.deleteMany({});
    await Shape.deleteMany({});
    await Color.deleteMany({});
    await Clarity.deleteMany({});
    await Size.deleteMany({});
    await Cut.deleteMany({});
    await Polish.deleteMany({});
    await Symmetry.deleteMany({});
    await Fluorescence.deleteMany({});
    await FluorescenceColor.deleteMany({});
    await FancyColor.deleteMany({});
    await Finish.deleteMany({});
    await Intensity.deleteMany({});
    await Overtone.deleteMany({});
    await Country.deleteMany({});
    await BusinessType.deleteMany({});
    await BuyingGroup.deleteMany({});

    console.log('Cleared existing data');

    // Create admin user
    const adminUser = new User({
      fullname: 'Admin User',
      username: 'admin',
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      password: process.env.ADMIN_PASSWORD || 'password',
      role: 'admin',
      status: 'active',
      emailVerified: true
    });
    await adminUser.save();
    console.log('Created admin user');

    // Seed Countries
    const countries = [
      { name: 'United States', code: 'US', iso_code: 'USA', phone_code: '+1', currency: 'USD' },
      { name: 'India', code: 'IN', iso_code: 'IND', phone_code: '+91', currency: 'INR' },
      { name: 'Belgium', code: 'BE', iso_code: 'BEL', phone_code: '+32', currency: 'EUR' },
      { name: 'Israel', code: 'IL', iso_code: 'ISR', phone_code: '+972', currency: 'ILS' },
      { name: 'United Kingdom', code: 'GB', iso_code: 'GBR', phone_code: '+44', currency: 'GBP' },
      { name: 'Canada', code: 'CA', iso_code: 'CAN', phone_code: '+1', currency: 'CAD' },
      { name: 'Australia', code: 'AU', iso_code: 'AUS', phone_code: '+61', currency: 'AUD' },
      { name: 'South Africa', code: 'ZA', iso_code: 'ZAF', phone_code: '+27', currency: 'ZAR' }
    ];
    await Country.insertMany(countries);
    console.log('Seeded countries');

    // Seed Shapes
    const shapes = [
      { name: 'Round', code: 'RD', sort_order: 1 },
      { name: 'Princess', code: 'PR', sort_order: 2 },
      { name: 'Emerald', code: 'EM', sort_order: 3 },
      { name: 'Asscher', code: 'AS', sort_order: 4 },
      { name: 'Oval', code: 'OV', sort_order: 5 },
      { name: 'Radiant', code: 'RA', sort_order: 6 },
      { name: 'Cushion', code: 'CU', sort_order: 7 },
      { name: 'Pear', code: 'PS', sort_order: 8 },
      { name: 'Heart', code: 'HT', sort_order: 9 },
      { name: 'Marquise', code: 'MQ', sort_order: 10 }
    ];
    await Shape.insertMany(shapes);
    console.log('Seeded shapes');

    // Seed Colors
    const colors = [
      { name: 'D', code: 'D', grade_order: 1 },
      { name: 'E', code: 'E', grade_order: 2 },
      { name: 'F', code: 'F', grade_order: 3 },
      { name: 'G', code: 'G', grade_order: 4 },
      { name: 'H', code: 'H', grade_order: 5 },
      { name: 'I', code: 'I', grade_order: 6 },
      { name: 'J', code: 'J', grade_order: 7 },
      { name: 'K', code: 'K', grade_order: 8 },
      { name: 'L', code: 'L', grade_order: 9 },
      { name: 'M', code: 'M', grade_order: 10 }
    ];
    await Color.insertMany(colors);
    console.log('Seeded colors');

    // Seed Clarity
    const clarities = [
      { name: 'FL', code: 'FL', description: 'Flawless', grade_order: 1 },
      { name: 'IF', code: 'IF', description: 'Internally Flawless', grade_order: 2 },
      { name: 'VVS1', code: 'VVS1', description: 'Very Very Slightly Included 1', grade_order: 3 },
      { name: 'VVS2', code: 'VVS2', description: 'Very Very Slightly Included 2', grade_order: 4 },
      { name: 'VS1', code: 'VS1', description: 'Very Slightly Included 1', grade_order: 5 },
      { name: 'VS2', code: 'VS2', description: 'Very Slightly Included 2', grade_order: 6 },
      { name: 'SI1', code: 'SI1', description: 'Slightly Included 1', grade_order: 7 },
      { name: 'SI2', code: 'SI2', description: 'Slightly Included 2', grade_order: 8 },
      { name: 'I1', code: 'I1', description: 'Included 1', grade_order: 9 },
      { name: 'I2', code: 'I2', description: 'Included 2', grade_order: 10 },
      { name: 'I3', code: 'I3', description: 'Included 3', grade_order: 11 }
    ];
    await Clarity.insertMany(clarities);
    console.log('Seeded clarity');

    // Seed Cuts
    const cuts = [
      { name: 'Excellent', code: 'EX', grade_order: 1 },
      { name: 'Very Good', code: 'VG', grade_order: 2 },
      { name: 'Good', code: 'GD', grade_order: 3 },
      { name: 'Fair', code: 'FR', grade_order: 4 },
      { name: 'Poor', code: 'PR', grade_order: 5 }
    ];
    await Cut.insertMany(cuts);
    console.log('Seeded cuts');

    // Seed Polish
    const polish = [
      { name: 'Excellent', code: 'EX', grade_order: 1 },
      { name: 'Very Good', code: 'VG', grade_order: 2 },
      { name: 'Good', code: 'GD', grade_order: 3 },
      { name: 'Fair', code: 'FR', grade_order: 4 },
      { name: 'Poor', code: 'PR', grade_order: 5 }
    ];
    await Polish.insertMany(polish);
    console.log('Seeded polish');

    // Seed Symmetry
    const symmetry = [
      { name: 'Excellent', code: 'EX', grade_order: 1 },
      { name: 'Very Good', code: 'VG', grade_order: 2 },
      { name: 'Good', code: 'GD', grade_order: 3 },
      { name: 'Fair', code: 'FR', grade_order: 4 },
      { name: 'Poor', code: 'PR', grade_order: 5 }
    ];
    await Symmetry.insertMany(symmetry);
    console.log('Seeded symmetry');

    // Seed Fluorescence
    const fluorescence = [
      { name: 'None', code: 'NON', intensity_level: 'None' },
      { name: 'Faint', code: 'FNT', intensity_level: 'Faint' },
      { name: 'Medium', code: 'MED', intensity_level: 'Medium' },
      { name: 'Strong', code: 'STG', intensity_level: 'Strong' },
      { name: 'Very Strong', code: 'VST', intensity_level: 'Very Strong' }
    ];
    await Fluorescence.insertMany(fluorescence);
    console.log('Seeded fluorescence');

    // Seed Fluorescence Colors
    const fluorescenceColors = [
      { name: 'Blue', code: 'BL', hex_code: '#0000FF' },
      { name: 'Yellow', code: 'YL', hex_code: '#FFFF00' },
      { name: 'Green', code: 'GR', hex_code: '#00FF00' },
      { name: 'Orange', code: 'OR', hex_code: '#FFA500' },
      { name: 'White', code: 'WH', hex_code: '#FFFFFF' }
    ];
    await FluorescenceColor.insertMany(fluorescenceColors);
    console.log('Seeded fluorescence colors');

    // Seed Fancy Colors
    const fancyColors = [
      { name: 'Fancy Yellow', code: 'FY', color_family: 'Yellow', hex_code: '#FFD700' },
      { name: 'Fancy Brown', code: 'FB', color_family: 'Brown', hex_code: '#8B4513' },
      { name: 'Fancy Pink', code: 'FP', color_family: 'Pink', hex_code: '#FFC0CB' },
      { name: 'Fancy Blue', code: 'FBL', color_family: 'Blue', hex_code: '#0000FF' },
      { name: 'Fancy Green', code: 'FG', color_family: 'Green', hex_code: '#00FF00' },
      { name: 'Fancy Orange', code: 'FO', color_family: 'Orange', hex_code: '#FFA500' },
      { name: 'Fancy Red', code: 'FR', color_family: 'Red', hex_code: '#FF0000' },
      { name: 'Fancy Purple', code: 'FPU', color_family: 'Purple', hex_code: '#800080' }
    ];
    await FancyColor.insertMany(fancyColors);
    console.log('Seeded fancy colors');

    // Seed Business Types
    const businessTypes = [
      { name: 'Manufacturer', code: 'MFG', sort_order: 1 },
      { name: 'Wholesaler', code: 'WHL', sort_order: 2 },
      { name: 'Retailer', code: 'RTL', sort_order: 3 },
      { name: 'Broker', code: 'BRK', sort_order: 4 },
      { name: 'Trader', code: 'TRD', sort_order: 5 },
      { name: 'Cutter', code: 'CUT', sort_order: 6 },
      { name: 'Polisher', code: 'POL', sort_order: 7 }
    ];
    await BusinessType.insertMany(businessTypes);
    console.log('Seeded business types');

    // Seed Buying Groups
    const buyingGroups = [
      { name: 'Independent', code: 'IND', sort_order: 1 },
      { name: 'Chain Store', code: 'CHN', sort_order: 2 },
      { name: 'Online Retailer', code: 'ONL', sort_order: 3 },
      { name: 'Jewelry Designer', code: 'DES', sort_order: 4 },
      { name: 'Custom Jeweler', code: 'CUS', sort_order: 5 }
    ];
    await BuyingGroup.insertMany(buyingGroups);
    console.log('Seeded buying groups');

    console.log('Database seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

seedData();
