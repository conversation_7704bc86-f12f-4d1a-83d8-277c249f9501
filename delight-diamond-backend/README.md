# Delight Diamond Backend API

A comprehensive Node.js backend API for the Delight Diamond Admin System, built with Express.js and MongoDB.

## Features

- **Authentication & Authorization**: JWT-based authentication with role-based access control
- **User Management**: Complete CRUD operations for users, vendors, and merchants
- **Product Management**: Diamond/stock management with comprehensive filtering and search
- **Master Data Management**: Shapes, colors, clarity, sizes, cuts, polish, symmetry, fluorescence, etc.
- **Cart & Order Management**: Shopping cart, hold diamonds, confirmed diamonds
- **Appointment Management**: Schedule and manage customer appointments
- **Policy Management**: Create and manage business policies
- **Demand Management**: Handle customer diamond requests
- **Star Melee Management**: Manage star melee diamond lots
- **File Upload**: Support for images, videos, documents, and CSV files
- **Bulk Import**: CSV import with error handling and validation
- **Upload History**: Track all file uploads and processing status
- **Invalid Stone Management**: Handle and correct invalid diamond data

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **File Upload**: Multer
- **Validation**: Express Validator
- **Security**: Helmet, CORS, Rate Limiting
- **Password Hashing**: bcryptjs

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd delight-diamond-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   PORT=3001
   MONGODB_URI=mongodb://localhost:27017/delight_diamond
   JWT_SECRET=your-super-secret-jwt-key-here
   JWT_EXPIRE=7d
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=password
   ```

4. **Start MongoDB**
   Make sure MongoDB is running on your system.

5. **Seed the database**
   ```bash
   npm run seed
   ```

6. **Start the server**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

## API Endpoints

### Authentication
- `POST /admin/auth/login` - User login
- `POST /admin/auth/signup` - User registration
- `GET /admin/auth/profile` - Get current user profile
- `PUT /admin/auth/password-update` - Update password
- `POST /admin/auth/forgot-password` - Forgot password
- `PUT /admin/auth/password-reset` - Reset password

### User Management
- `GET /admin/users` - Get all users (Admin only)
- `GET /admin/users/:id` - Get user by ID
- `POST /admin/users` - Create new user (Admin only)
- `PUT /admin/users/:id` - Update user (Admin only)
- `DELETE /admin/users/:id` - Delete user (Admin only)
- `PATCH /admin/users/:id/status` - Update user status (Admin only)

### Product Management
- `GET /admin/product` - Get all products with filtering
- `GET /admin/product/:id` - Get product by ID
- `POST /admin/product/create` - Create new product
- `PUT /admin/product/:id` - Update product
- `DELETE /admin/product/:id` - Delete product
- `PATCH /admin/product/:id/status` - Update product status
- `POST /admin/product/bulk-upload` - Bulk upload via CSV
- `GET /admin/product/upload-status/:uploadId` - Get upload status

### Master Data
- `GET /admin/shapes` - Get all shapes
- `POST /admin/shapes` - Create new shape (Admin only)
- `PUT /admin/shapes/:id` - Update shape (Admin only)
- `DELETE /admin/shapes/:id` - Delete shape (Admin only)

Similar endpoints exist for:
- `/admin/colors` - Diamond colors
- `/admin/clarity` - Diamond clarity grades
- `/admin/cuts` - Diamond cuts
- `/admin/polish` - Polish grades
- `/admin/symmetry` - Symmetry grades
- `/admin/fluorescence` - Fluorescence levels
- `/admin/fancy-colors` - Fancy diamond colors
- `/admin/countries` - Countries
- `/admin/business-types` - Business types
- `/admin/buying-groups` - Buying groups

### Vendor Management
- `GET /admin/vendor` - Get all vendors
- `GET /admin/vendor/:id` - Get vendor by ID
- `POST /admin/vendor` - Create new vendor
- `PUT /admin/vendor/:id` - Update vendor
- `DELETE /admin/vendor/:id` - Delete vendor (Admin only)

### Merchant Management
- `GET /admin/merchant` - Get all merchants
- `GET /admin/merchant/:id` - Get merchant by ID
- `POST /admin/merchant` - Create new merchant
- `PUT /admin/merchant/:id` - Update merchant
- `DELETE /admin/merchant/:id` - Delete merchant (Admin only)

### Cart Management
- `GET /admin/cart` - Get user's cart items
- `POST /admin/cart/add` - Add item to cart
- `PUT /admin/cart/:id` - Update cart item
- `DELETE /admin/cart/:id` - Remove item from cart
- `POST /admin/cart/hold` - Hold diamonds
- `POST /admin/cart/confirm` - Confirm diamonds
- `DELETE /admin/cart/clear` - Clear cart

### Appointment Management
- `GET /admin/appointment` - Get all appointments
- `POST /admin/appointment` - Create new appointment
- `PUT /admin/appointment/:id` - Update appointment
- `DELETE /admin/appointment/:id` - Delete appointment

### Policy Management
- `GET /admin/policy` - Get all policies
- `GET /admin/policy/:id` - Get policy by ID
- `POST /admin/policy/create` - Create new policy (Admin only)
- `PUT /admin/policy/:id` - Update policy (Admin only)
- `POST /admin/policy/status/:id` - Update policy status (Admin only)
- `DELETE /admin/policy/:id` - Delete policy (Admin only)

### Additional Features
- `GET /admin/demand` - Demand management
- `GET /admin/star-melee` - Star melee management
- `GET /admin/upload-history` - Upload history tracking
- `GET /admin/invalid-stones` - Invalid stone management

## Authentication

All API endpoints (except auth endpoints) require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## File Uploads

The API supports file uploads for:
- Product images and videos
- User avatars
- Policy attachments
- CSV files for bulk import

Files are stored in the `/uploads` directory with organized subdirectories.

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "error": {
    "error_message": "Error description"
  }
}
```

## Success Responses

Successful responses follow this format:

```json
{
  "success": true,
  "data": {
    "result": "response data",
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 100,
      "total_pages": 10
    }
  }
}
```

## Database Models

The application includes comprehensive models for:
- Users (Admin, Vendors, Merchants)
- Products (Diamonds/Stock)
- Master Data (Shapes, Colors, Clarity, etc.)
- Cart Items
- Appointments
- Policies
- Demands
- Star Melee
- Upload History
- Invalid Stones

## Security Features

- JWT authentication
- Password hashing with bcrypt
- Rate limiting
- CORS protection
- Helmet security headers
- Input validation and sanitization
- Role-based access control

## Development

```bash
# Install dependencies
npm install

# Start development server with auto-reload
npm run dev

# Run database seeding
npm run seed
```

## Production Deployment

1. Set `NODE_ENV=production` in your environment
2. Configure production MongoDB URI
3. Set secure JWT secrets
4. Configure email settings for password reset
5. Set up proper file storage (consider cloud storage)
6. Configure reverse proxy (nginx)
7. Set up SSL certificates

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
